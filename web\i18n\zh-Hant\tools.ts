const translation = {
  title: '工具',
  createCustomTool: '建立自定義工具',
  type: {
    all: '全部',
    builtIn: '內建',
    custom: '自定義',
    workflow: '工作流',
  },
  contribute: {
    line1: '我有興趣為 ',
    line2: 'Dify 貢獻工具。',
    viewGuide: '檢視指南',
  },
  author: '作者',
  auth: {
    authorized: '已授權',
    setup: '要使用請先授權',
    setupModalTitle: '設定授權',
    setupModalTitleDescription: '配置憑據後，工作區中的所有成員都可以在編排應用程式時使用此工具。',
  },
  includeToolNum: '包含 {{num}} 個工具',
  addTool: '新增工具',
  createTool: {
    title: '建立自定義工具',
    editAction: '編輯',
    editTitle: '編輯自定義工具',
    name: '名稱',
    toolNamePlaceHolder: '輸入工具名稱',
    schema: 'Schema',
    schemaPlaceHolder: '在此處輸入您的 OpenAPI schema',
    viewSchemaSpec: '檢視 OpenAPI-Swagger 規範',
    importFromUrl: '從 URL 中匯入',
    importFromUrlPlaceHolder: 'https://...',
    urlError: '請輸入有效的 URL',
    examples: '例子',
    exampleOptions: {
      json: '天氣 (JSON)',
      yaml: '寵物商店 (YAML)',
      blankTemplate: '空白模版',
    },
    availableTools: {
      title: '可用工具',
      name: '名稱',
      description: '描述',
      method: '方法',
      path: '路徑',
      action: '操作',
      test: '測試',
    },
    authMethod: {
      title: '鑑權方法',
      type: '鑑權型別',
      keyTooltip: 'HTTP 頭部名稱，如果你不知道是什麼，可以將其保留為 Authorization 或設定為自定義值',
      types: {
        none: '無',
        api_key: 'API Key',
        apiKeyPlaceholder: 'HTTP 頭部名稱，用於傳遞 API Key',
        apiValuePlaceholder: '輸入 API Key',
      },
      key: '鍵',
      value: '值',
    },
    authHeaderPrefix: {
      title: '鑑權頭部字首',
      types: {
        basic: 'Basic',
        bearer: 'Bearer',
        custom: 'Custom',
      },
    },
    privacyPolicy: '隱私協議',
    privacyPolicyPlaceholder: '請輸入隱私協議',
    customDisclaimer: '自定義免責聲明',
    customDisclaimerPlaceholder: '請輸入自定義免責聲明',
    deleteToolConfirmTitle: '刪除這個工具？',
    deleteToolConfirmContent: '刪除工具是不可逆的。用戶將無法再訪問您的工具。',
    toolInput: {
      labelPlaceholder: '選擇標籤（選擇標籤）',
      label: '標籤',
      required: '必填',
      methodSettingTip: '用戶填寫工具配置',
      name: '名字',
      description: '描述',
      methodParameterTip: '推理期間 LLM 填充',
      method: '方法',
      title: '工具輸入',
      methodSetting: '設置',
      methodParameter: '參數',
      descriptionPlaceholder: '參數含義的描述',
    },
    description: '描述',
    nameForToolCall: '工具調用名稱',
    confirmTitle: '確認儲存？',
    descriptionPlaceholder: '工具用途的簡要描述，例如，獲取特定位置的溫度。',
    nameForToolCallTip: '僅支援數位、字母和下劃線。',
    confirmTip: '使用此工具的應用程式將受到影響',
    nameForToolCallPlaceHolder: '用於機器識別，例如 getCurrentWeather、list_pets',
  },
  test: {
    title: '測試',
    parametersValue: '引數和值',
    parameters: '引數',
    value: '值',
    testResult: '測試結果',
    testResultPlaceholder: '測試結果將顯示在這裡',
  },
  thought: {
    using: '正在使用',
    used: '已使用',
    requestTitle: '請求來自',
    responseTitle: '響應來自',
  },
  setBuiltInTools: {
    info: '資訊',
    setting: '設定',
    toolDescription: '工具描述',
    parameters: '引數',
    string: '字串',
    number: '數字',
    required: '必填',
    infoAndSetting: '資訊和設定',
    file: '檔',
  },
  noCustomTool: {
    title: '沒有自定義工具！',
    content: '在此統一新增和管理你的自定義工具，方便構建應用時使用。',
    createTool: '建立工具',
  },
  noSearchRes: {
    title: '抱歉，沒有結果！',
    content: '我們找不到任何與您的搜尋相匹配的工具。',
    reset: '重置搜尋',
  },
  builtInPromptTitle: '提示詞',
  toolRemoved: '工具已被移除',
  notAuthorized: '工具未授權',
  howToGet: '如何獲取',
  addToolModal: {
    add: '加',
    type: '類型',
    added: '添加',
    manageInTools: '在工具中管理',
    category: '類別',
    custom: {
      title: '沒有可用的自訂工具',
      tip: '創建一個自訂工具',
    },
    workflow: {
      title: '沒有可用的工作流程工具',
      tip: '在 Studio 中將工作流程發佈為工具',
    },
    mcp: {
      title: '沒有可用的 MCP 工具',
      tip: '新增一個 MCP 伺服器',
    },
    agent: {
      title: '沒有可用的代理策略',
    },
  },
  customToolTip: '瞭解有關 Dify 自訂工具的更多資訊',
  toolNameUsageTip: '用於代理推理和提示的工具調用名稱',
  openInStudio: '在 Studio 中打開',
  noTools: '未找到工具',
  copyToolName: '複製名稱',
  mcp: {
    create: {
      cardTitle: '新增 MCP 伺服器 (HTTP)',
      cardLink: '了解更多關於 MCP 伺服器整合',
    },
    noConfigured: '未配置的伺服器',
    updateTime: '已更新',
    toolsCount: '{{count}} 個工具',
    noTools: '沒有可用的工具',
    modal: {
      title: '新增 MCP 伺服器 (HTTP)',
      editTitle: '編輯 MCP 伺服器 (HTTP)',
      name: '名稱與圖示',
      namePlaceholder: '為您的 MCP 伺服器命名',
      serverUrl: '伺服器 URL',
      serverUrlPlaceholder: '伺服器端點的 URL',
      serverUrlWarning: '更新伺服器地址可能會干擾依賴於此伺服器的應用程式',
      serverIdentifier: '伺服器識別碼',
      serverIdentifierTip: '在工作區內 MCP 伺服器的唯一識別碼。僅限小寫字母、數字、底線和連字符。最多 24 個字元。',
      serverIdentifierPlaceholder: '唯一識別碼，例如：my-mcp-server',
      serverIdentifierWarning: '更改 ID 之後，現有應用程式將無法識別伺服器',
      cancel: '取消',
      save: '儲存',
      confirm: '新增並授權',
    },
    delete: '刪除 MCP 伺服器',
    deleteConfirmTitle: '您確定要刪除 {{mcp}} 嗎？',
    operation: {
      edit: '編輯',
      remove: '移除',
    },
    authorize: '授權',
    authorizing: '正在授權...',
    authorizingRequired: '需要授權',
    authorizeTip: '授權後，這裡將顯示工具。',
    update: '更新',
    updating: '更新中',
    gettingTools: '獲取工具...',
    updateTools: '更新工具...',
    toolsEmpty: '工具未加載',
    getTools: '獲取工具',
    toolUpdateConfirmTitle: '更新工具列表',
    toolUpdateConfirmContent: '更新工具列表可能會影響現有應用程式。您要繼續嗎？',
    toolsNum: '{{count}} 個工具包含',
    onlyTool: '包含 1 個工具',
    identifier: '伺服器識別碼 (點擊複製)',
    server: {
      title: 'MCP 伺服器',
      url: '伺服器 URL',
      reGen: '您想要重新生成伺服器 URL 嗎？',
      addDescription: '新增描述',
      edit: '編輯描述',
      modal: {
        addTitle: '新增描述以啟用 MCP 伺服器',
        editTitle: '編輯描述',
        description: '描述',
        descriptionPlaceholder: '說明此工具的用途及如何被 LLM 使用',
        parameters: '參數',
        parametersTip: '為每個參數添加描述，以幫助 LLM 理解其目的和約束。',
        parametersPlaceholder: '參數的目的和約束',
        confirm: '啟用 MCP 伺服器',
      },
      publishTip: '應用程式尚未發布。請先發布應用程式。',
    },
  },
}

export default translation
