# 用户管理接口文档

## 概述

本文档描述了Dify用户管理系统的四个核心接口，用于与数字身份平台进行用户数据同步。

## 接口列表

### ① 用户创建接口
- **接口路径**: `/console/api/UserCreateService`
- **请求方式**: `POST`
- **功能描述**: 创建新用户账户

### ② 用户查询接口
- **接口路径**: `/console/api/UserQueryService`
- **请求方式**: `POST`
- **功能描述**: 查询用户信息

### ③ 用户更新接口
- **接口路径**: `/console/api/UserUpdateService`
- **请求方式**: `POST`
- **功能描述**: 更新用户信息

### ④ 用户删除接口
- **接口路径**: `/console/api/UserDeleteService`
- **请求方式**: `POST`
- **功能描述**: 逻辑删除用户（禁用账户）

### ⑤ 用户物理删除接口
- **接口路径**: `/console/api/UserPhysicalDeleteService`
- **请求方式**: `POST`
- **功能描述**: 物理删除用户（永久删除数据）

## 用户属性列表

| 字段名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| userId | String | 是 | 用户唯一标识符 | "user123456" |
| loginName | String | 是 | 用户登录名 | "<EMAIL>" |
| realName | String | 是 | 用户真实姓名 | "张三" |
| userStatus | String | 是 | 用户状态（数字格式） | "1" |
| gender | String | 否 | 性别 | "男" |
| identityNo | String | 否 | 身份证号 | "110101199001011234" |
| birthDay | String | 否 | 生日（YYYY-MM-DD） | "1990-01-01" |
| mobile | String | 否 | 手机号 | "13800138000" |
| officePhone | String | 否 | 办公电话 | "010-12345678" |
| email | String | 否 | 邮箱地址 | "<EMAIL>" |
| employNo | String | 否 | 工号 | "EMP001" |
| basePostId | String | 否 | 岗位ID | "POST001" |
| job | String | 否 | 职称 | "高级工程师" |
| duty | String | 否 | 职务 | "技术经理" |
| degreeCode | String | 否 | 文化程度 | "本科" |
| comments | String | 否 | 备注 | "备注信息" |
| baseOrgId | String | 否 | 机构ID | "ORG001" |
| sapHRUserId | String | 否 | SAP HR用户ID | "SAP001" |
| userType | String | 否 | 用户类型 | "EMPLOYEE" |
| startDate | String | 否 | 临时用户生效时间 | "2024-01-01" |
| endDate | String | 否 | 临时用户失效时间 | "2024-12-31" |

## 用户状态说明

### 数字平台状态（对外接口）
| 状态值 | 说明 |
|--------|------|
| "1" | 正常 |
| "2" | 禁用 |
| "3" | 锁定 |

### 数据库状态（内部存储）
| 状态值 | 说明 |
|--------|------|
| "active" | 正常 |
| "banned" | 禁用 |
| "locked" | 锁定 |

**注意**: 接口对外使用数字状态，内部自动转换为数据库状态进行存储。

## 用户类型说明

| 类型值 | 说明 |
|--------|------|
| "EMPLOYEE" | 内部用户 |
| "TEMP" | 临时用户 |

## 删除方式说明

| 删除方式 | 说明 | 数据影响 |
|----------|------|----------|
| 逻辑删除 | 将用户状态设置为禁用 | 数据保留，用户无法登录 |
| 物理删除 | 从数据库中永久删除用户记录 | 数据完全删除，包括租户关联 |

## 详细接口说明

### ① 用户创建接口

#### 请求参数
```json
{
    "userId": "user123456",
    "loginName": "<EMAIL>",
    "realName": "张三",
    "userStatus": "1",
    "gender": "男",
    "identityNo": "110101199001011234",
    "birthDay": "1990-01-01",
    "mobile": "13800138000",
    "officePhone": "010-12345678",
    "email": "<EMAIL>",
    "employNo": "EMP001",
    "basePostId": "POST001",
    "job": "高级工程师",
    "duty": "技术经理",
    "degreeCode": "本科",
    "comments": "备注信息",
    "baseOrgId": "ORG001",
    "sapHRUserId": "SAP001",
    "userType": "EMPLOYEE",
    "startDate": "2024-01-01",
    "endDate": "2024-12-31",
    "serialNum": "************-DSFIFASCC"
}
```

#### 响应示例
```json
{
    "code": "200",
    "uid": "user123456",
    "serialNum": "************-DSFIFASCC",
    "message": "success"
}
```

#### 处理逻辑
1. 验证JWT token
2. 检查用户是否已存在（通过userId、email、loginName）
3. 生成12位随机密码
4. 创建用户账户记录
5. 将数字状态转换为数据库状态存储
6. 创建用户专属租户
7. 返回创建结果

### ② 用户查询接口

#### 请求参数
```json
{
    "ids": ["user123456", "user789012"],
    "pageSize": 10,
    "pageNum": 1
}
```

#### 响应示例
```json
{
    "code": 200,
    "status": "success",
    "message": "operation.success",
    "data": {
        "total": 2,
        "dataNum": 2,
        "data": [
            {
                "userId": "user123456",
                "loginName": "<EMAIL>",
                "realName": "张三",
                "userStatus": "1",
                "gender": "男",
                "identityNo": "110101199001011234",
                "birthDay": "1990-01-01",
                "mobile": "13800138000",
                "officePhone": "010-12345678",
                "email": "<EMAIL>",
                "employNo": "EMP001",
                "basePostId": "POST001",
                "job": "高级工程师",
                "duty": "技术经理",
                "degreeCode": "本科",
                "comments": "备注信息",
                "baseOrgId": "ORG001",
                "sapHRUserId": "SAP001",
                "userType": "EMPLOYEE",
                "startDate": "2024-01-01",
                "endDate": "2024-12-31"
            }
        ]
    }
}
```

#### 处理逻辑
1. 验证JWT token
2. 优先通过user_id查询用户
3. 如果user_id查询结果不足，再通过系统id查询
4. 将数据库状态转换为数字状态返回
5. 支持分页查询

### ③ 用户更新接口

#### 请求参数
```json
{
    "uid": "user123456",
    "realName": "张三（已更新）",
    "mobile": "13900139000",
    "userStatus": "1",
    "gender": "男",
    "birthDay": "1990-01-01",
    "officePhone": "010-87654321",
    "email": "<EMAIL>",
    "serialNum": "************-DSFIFASCC"
}
```

#### 响应示例
```json
{
    "code": "200",
    "uid": "user123456",
    "serialNum": "************-DSFIFASCC",
    "message": "success"
}
```

#### 处理逻辑
1. 验证JWT token
2. 查找用户（优先通过user_id，再通过系统id）
3. 将数字状态转换为数据库状态
4. 更新用户信息
5. 返回更新结果

### ④ 用户删除接口（逻辑删除）

#### 请求参数
```json
{
    "uid": "user123456",
    "serialNum": "************-DSFIFASCC"
}
```

#### 响应示例
```json
{
    "code": "200",
    "uid": "user123456",
    "serialNum": "************-DSFIFASCC",
    "message": "success"
}
```

#### 处理逻辑
1. 验证JWT token
2. 查找用户（优先通过user_id，再通过系统id）
3. 将用户状态设置为"banned"（禁用）
4. 返回删除结果

### ⑤ 用户物理删除接口

#### 请求参数
```json
{
    "uid": "user123456",
    "serialNum": "************-DSFIFASCC"
}
```

#### 响应示例
```json
{
    "code": "200",
    "uid": "user123456",
    "serialNum": "************-DSFIFASCC",
    "message": "success"
}
```

#### 处理逻辑
1. 验证JWT token
2. 查找用户（优先通过user_id，再通过系统id）
3. 删除租户关联记录（TenantAccountJoin）
4. 删除账户集成记录（AccountIntegrate）
5. 删除用户账户记录（Account）
6. 返回删除结果

## 返回报文示例

### 成功响应
```json
{
    "code": "200",
    "uid": "user123456",
    "serialNum": "************-DSFIFASCC",
    "message": "success"
}
```

### 失败响应
```json
{
    "code": 500,
    "message": "用户已存在",
    "serialNum": "************-DSFIFASCC"
}
```

## cURL测试指令

### 获取访问令牌

#### 方式一：通过IAM单点登录获取（推荐）
```bash
# 1. 访问IAM授权URL（浏览器操作）
https://{IAM_HOST}/idp/authCenter/authenticate?response_type=code&state=test&redirect_uri=http://dify-host/console/api/oauth/authorize/iam&client_id=your_client_id

# 2. 完成授权后，从回调URL中获取authorization_code

# 3. 使用authorization_code获取access_token
curl -X POST "http://{IAM_HOST}/am-gateway/{namespace}/am-protocol-service/oauth2/getToken" \
  -d "client_id=your_client_id&grant_type=authorization_code&code=authorization_code&client_secret=your_client_secret"

# 4. 响应示例：
# {
#   "access_token": "your_iam_access_token",
#   "expires_in": "1500",
#   "refresh_token": "your_refresh_token",
#   "uid": "user_unique_id"
# }
```

#### 方式二：通过Dify本地登录获取（兼容模式）
```bash
curl -X POST "http://localhost:5001/console/api/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your_password"
  }'
```

### 令牌验证说明

用户管理接口支持智能令牌验证，具有以下特性：

#### 验证策略
1. **智能识别**: 系统会自动识别令牌类型（IAM令牌 vs JWT令牌）
2. **降级处理**: 当IAM验证失败时，自动降级到JWT验证
3. **配置检测**: 自动检测IAM是否已正确配置

#### 支持的令牌类型

1. **Dify JWT令牌（默认支持）**
   - 使用通过Dify本地登录接口获得的JWT令牌
   - 始终支持，确保向后兼容性
   - 适用于IAM未配置或不可用的情况

2. **IAM访问令牌（可选支持）**
   - 使用通过IAM单点登录获得的access_token
   - 需要配置IAM相关环境变量
   - 系统会自动调用IAM的getUserInfo接口验证令牌有效性
   - 根据IAM返回的userName查找对应的Dify用户

#### 配置要求

**IAM支持**需要配置以下环境变量：
```bash
IAM_HOST=your_iam_host
IAM_NAMESPACE=your_namespace  
IAM_CLIENT_ID=your_client_id
```

如果缺少任何一个配置，系统将自动使用JWT验证方式。

#### 令牌格式识别
- **IAM令牌**: 通常为32位以上的随机字符串，不含'.'分隔符
- **JWT令牌**: 包含'.'分隔的三部分结构

#### 验证流程
1. 检查Authorization头格式
2. 提取令牌
3. 检查IAM是否已配置
4. 如果IAM已配置且令牌格式符合IAM特征：
   - 尝试IAM验证
   - 如果失败，降级到JWT验证
5. 否则直接使用JWT验证

#### 错误处理
- 提供详细的错误信息帮助调试
- 支持验证方式的自动降级
- 记录验证失败的详细日志

### ① 用户创建接口
```bash
curl -X POST "http://localhost:5001/console/api/UserCreateService" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_IAM_ACCESS_TOKEN" \
  -d '{
    "userId": "user123456",
    "loginName": "<EMAIL>",
    "realName": "张三",
    "userStatus": "1",
    "gender": "男",
    "mobile": "13800138000",
    "email": "<EMAIL>",
    "serialNum": "************-DSFIFASCC"
  }'
```

### ② 用户查询接口
```bash
curl -X POST "http://localhost:5001/console/api/UserQueryService" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_IAM_ACCESS_TOKEN" \
  -d '{
    "ids": ["user123456"],
    "pageSize": 10,
    "pageNum": 1
  }'
```

### ③ 用户更新接口
```bash
curl -X POST "http://localhost:5001/console/api/UserUpdateService" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_IAM_ACCESS_TOKEN" \
  -d '{
    "uid": "user123456",
    "realName": "张三（已更新）",
    "mobile": "13900139000",
    "userStatus": "1",
    "serialNum": "************-DSFIFASCC"
  }'
```

### ④ 用户删除接口（逻辑删除）
```bash
curl -X POST "http://localhost:5001/console/api/UserDeleteService" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_IAM_ACCESS_TOKEN" \
  -d '{
    "uid": "user123456",
    "serialNum": "************-DSFIFASCC"
  }'
```

### ⑤ 用户物理删除接口
```bash
curl -X POST "http://localhost:5001/console/api/UserPhysicalDeleteService" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_IAM_ACCESS_TOKEN" \
  -d '{
    "uid": "user123456",
    "serialNum": "************-DSFIFASCC"
  }'
```

## 字段说明

### 必填字段
- `userId`: 用户唯一标识符，用于系统间用户识别
- `loginName`: 用户登录名，通常为邮箱地址
- `realName`: 用户真实姓名
- `userStatus`: 用户状态，使用数字格式（1=正常，2=禁用，3=锁定）
- `serialNum`: 流水号，用于请求追踪

### 可选字段
- 其他字段均为可选，可根据实际需要提供

## 注意事项

1. **状态转换**: 接口对外使用数字状态，内部自动转换为数据库状态
2. **用户ID优先级**: 查询、更新、删除操作优先使用user_id字段
3. **租户创建**: 创建用户时会自动创建专属租户
4. **密码生成**: 创建用户时会自动生成12位随机密码
5. **数据完整性**: 物理删除会删除所有相关数据记录

## 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 401 | 未授权 | 检查Authorization头是否正确 |
| 500 | 用户已存在 | 检查userId、email、loginName是否重复 |
| 500 | 用户不存在 | 检查uid是否正确 |
| 500 | 系统错误 | 查看服务器日志 |

## 技术支持

如有问题，请联系技术支持团队。 
