const translation = {
  pageTitle: 'Hey, başlayalım!👋',
  welcome: 'Dify\'ye hoş geldiniz, devam etmek için lütfen giriş yapın.',
  email: 'E-posta adresi',
  emailPlaceholder: 'E-postan<PERSON>z',
  password: '<PERSON><PERSON><PERSON>',
  passwordPlaceholder: '<PERSON><PERSON><PERSON><PERSON>',
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON> adı',
  namePlaceholder: '<PERSON><PERSON><PERSON><PERSON><PERSON> adınız',
  forget: 'Şifrenizi mi unuttunuz?',
  signBtn: 'Giri<PERSON> yap',
  sso: 'SSO ile devam et',
  installBtn: 'Kurulum',
  setAdminAccount: 'Yönetici hesabı ayarlama',
  setAdminAccountDesc: 'Yönetici hesabı için maksimum ayrıcalıklar, uygulama oluşturma ve LLM sağlayıcılarını yönetme gibi işlemler için kullanılabilir.',
  createAndSignIn: 'Oluştur ve giriş yap',
  oneMoreStep: '<PERSON><PERSON> adım kaldı',
  createSample: '<PERSON><PERSON> bil<PERSON><PERSON><PERSON> day<PERSON>, sizin için örnek bir uygulama oluşturacağız',
  invitationCode: 'Davet Kodu',
  invitationCodePlaceholder: 'Davet kodunuz',
  interfaceLanguage: 'Arayüz Dili',
  timezone: 'Zaman dilimi',
  go: 'Dify\'ye git',
  sendUsMail: 'Tanıtımınızı e-posta ile gönderin, davet talebini işleme alalım.',
  acceptPP: 'Gizlilik politikasını okudum ve kabul ediyorum',
  reset: 'Şifrenizi sıfırlamak için şu komutu çalıştırın',
  withGitHub: 'GitHub ile devam et',
  withGoogle: 'Google ile devam et',
  rightTitle: 'LLM\'nin tam potansiyelini ortaya çıkarın',
  rightDesc: 'Görsel olarak çekici, çalışılabilir ve iyileştirilebilir AI uygulamaları oluşturun.',
  tos: 'Hizmet Şartları',
  pp: 'Gizlilik Politikası',
  tosDesc: 'Kaydolarak, Hizmet Şartlarımızı kabul etmiş olursunuz',
  goToInit: 'Hesabı başlatmadıysanız, lütfen başlatma sayfasına gidin',
  dontHave: 'Sahip değil misiniz?',
  invalidInvitationCode: 'Geçersiz davet kodu',
  accountAlreadyInited: 'Hesap zaten başlatılmış',
  forgotPassword: 'Şifrenizi mi unuttunuz?',
  resetLinkSent: 'Sıfırlama bağlantısı gönderildi',
  sendResetLink: 'Sıfırlama bağlantısı gönder',
  backToSignIn: 'Girişe dön',
  forgotPasswordDesc: 'Şifrenizi sıfırlamak için e-posta adresinizi girin. Şifrenizi nasıl sıfırlayacağınıza dair talimatları içeren bir e-posta göndereceğiz.',
  checkEmailForResetLink: 'Şifrenizi sıfırlamak için bir bağlantı içeren e-postayı kontrol edin. Birkaç dakika içinde görünmezse, spam klasörünüzü kontrol ettiğinizden emin olun.',
  passwordChanged: 'Şimdi giriş yapın',
  changePassword: 'Şifre Değiştir',
  changePasswordTip: 'Hesabınız için yeni bir şifre girin',
  invalidToken: 'Geçersiz veya süresi dolmuş token',
  confirmPassword: 'Şifreyi Onayla',
  confirmPasswordPlaceholder: 'Yeni şifrenizi onaylayın',
  passwordChangedTip: 'Şifreniz başarıyla değiştirildi',
  error: {
    emailEmpty: 'E-posta adresi gereklidir',
    emailInValid: 'Geçerli bir e-posta adresi girin',
    nameEmpty: 'İsim gereklidir',
    passwordEmpty: 'Şifre gereklidir',
    passwordLengthInValid: 'Şifre en az 8 karakterden oluşmalıdır',
    passwordInvalid: 'Şifre harf ve rakamlardan oluşmalı ve uzunluğu 8 karakterden fazla olmalıdır',
    registrationNotAllowed: 'Hesap bulunamadı. Kayıt olmak için lütfen sistem yöneticisi ile iletişime geçin.',
  },
  license: {
    tip: 'Dify Community Edition\'ı başlatmadan önce GitHub\'daki',
    link: 'Açık Kaynak Lisansını',
  },
  join: 'Katıl',
  joinTipStart: 'Sizi',
  joinTipEnd: 'takımına davet ediyor',
  invalid: 'Bağlantı süresi doldu',
  explore: 'Dify\'yi Keşfet',
  activatedTipStart: 'Katıldınız',
  activatedTipEnd: 'takımına',
  activated: 'Şimdi giriş yapın',
  adminInitPassword: 'Yönetici başlangıç şifresi',
  validate: 'Doğrula',
  checkCode: {
    emptyCode: 'Kod gereklidir',
    verificationCode: 'Doğrulama kodu',
    verify: 'Doğrulamak',
    validTime: 'Kodun 5 dakika boyunca geçerli olduğunu unutmayın',
    invalidCode: 'Geçersiz kod',
    checkYourEmail: 'E-postanızı kontrol edin',
    verificationCodePlaceholder: '6 haneli kodu girin',
    useAnotherMethod: 'Başka bir yöntem kullanın',
    didNotReceiveCode: 'Kodu almadınız mı?',
    tips: '<strong>{{email}}</strong> adresine bir doğrulama kodu gönderiyoruz',
    resend: 'Tekrar Gönder',
  },
  enterYourName: 'Lütfen kullanıcı adınızı giriniz',
  resetPassword: 'Şifre Sıfırlama',
  noLoginMethod: 'Kimlik doğrulama yöntemi yapılandırılmadı',
  or: 'VEYA',
  continueWithCode: 'Kodla Devam Et',
  setYourAccount: 'Hesabınızı Ayarlayın',
  changePasswordBtn: 'Bir şifre belirleyin',
  withSSO: 'TOA ile devam etme',
  usePassword: 'Şifre Kullan',
  resetPasswordDesc: 'Dify\'a kaydolmak için kullandığınız e-postayı yazın, size bir şifre sıfırlama e-postası gönderelim.',
  backToLogin: 'Girişe geri dön',
  useVerificationCode: 'Doğrulama Kodunu Kullan',
  noLoginMethodTip: 'Bir kimlik doğrulama yöntemi eklemek için lütfen sistem yöneticisine başvurun.',
  sendVerificationCode: 'Doğrulama Kodu Gönder',
  back: 'Geri',
  licenseExpiredTip: 'Çalışma alanınız için Dify Enterprise lisansının süresi doldu. Dify\'ı kullanmaya devam etmek için lütfen yöneticinizle iletişime geçin.',
  licenseLostTip: 'Dify lisans sunucusuna bağlanılamadı. Dify\'ı kullanmaya devam etmek için lütfen yöneticinizle iletişime geçin.',
  licenseInactiveTip: 'Çalışma alanınız için Dify Enterprise lisansı etkin değil. Dify\'ı kullanmaya devam etmek için lütfen yöneticinizle iletişime geçin.',
  licenseExpired: 'Lisansın Süresi Doldu',
  licenseLost: 'Lisans Kaybedildi',
  licenseInactive: 'Lisans Etkin Değil',
  webapp: {
    disabled: 'Web uygulaması kimlik doğrulaması devre dışı. Lütfen bu özelliği etkinleştirmesi için sistem yöneticisi ile iletişime geçin. Uygulamayı doğrudan kullanmayı deneyebilirsiniz.',
    noLoginMethod: 'Web uygulaması için kimlik doğrulama yöntemi yapılandırılmamış',
    noLoginMethodTip: 'Lütfen bir kimlik doğrulama yöntemi eklemek için sistem yöneticisi ile iletişime geçin.',
  },
}

export default translation
