前端本地启动：
源码构建：
docker build . -t dify-web
docker run -it -p 3000:3000 -e CONSOLE_URL=http://127.0.0.1:5001 -e APP_URL=http://127.0.0.1:5001 dify-web
or
直接拉取前端镜像：
docker run --rm -it --name dify-web-dev -p 3000:3000 -e CONSOLE_URL=http://127.0.0.1:5001 -e APP_URL=http://127.0.0.1:5001 langgenius/dify-web:latest

后端中间件：

cd docker
cp middleware.env.example middleware.env
docker compose -f docker-compose.middleware.yaml up -d
