const translation = {
  createApp: 'CREEAZĂ APLICAȚIE',
  types: {
    all: 'Toate',
    chatbot: 'Chatbot',
    agent: 'Agent',
    workflow: 'Flux de lucru',
    completion: 'Finalizare',
    advanced: 'Fluxul de chat',
    basic: 'Bază',
  },
  duplicate: 'Duplicat',
  duplicateTitle: 'Duplică Aplicația',
  export: 'Exportă DSL',
  exportFailed: 'Exportul DSL a eșuat.',
  importDSL: 'Importă fișier DSL',
  createFromConfigFile: 'Creează din fișier DSL',
  deleteAppConfirmTitle: 'Ștergi această aplicație?',
  deleteAppConfirmContent:
    'Ștergerea aplicației este ireversibilă. Utilizatorii nu vor mai putea accesa aplicația ta, iar toate configurațiile promptului și jurnalele vor fi șterse permanent.',
  appDeleted: 'Aplicația a fost ștearsă',
  appDeleteFailed: 'Ștergerea aplicației a eșuat',
  join: 'Alătură-te comunității',
  communityIntro:
    'Discută cu membrii echipei, colaboratorii și dezvoltatorii pe diferite canale.',
  roadmap: 'Vezi planul nostru de dezvoltare',
  newApp: {
    startFromBlank: 'Creează din Nou',
    startFromTemplate: 'Creează din Șablon',
    captionAppType: 'Ce tip de aplicație vrei să creezi?',
    chatbotDescription: 'Construiește o aplicație bazată pe chat. Această aplicație folosește un format întrebare-răspuns, permițând mai multe runde de conversație continuă.',
    completionDescription: 'Construiește o aplicație care generează text de înaltă calitate pe baza indicațiilor, cum ar fi generarea de articole, rezumate, traduceri și mai multe.',
    completionWarning: 'Acest tip de aplicație nu va mai fi acceptat.',
    agentDescription: 'Construiește un Agent inteligent care poate alege în mod autonom instrumentele pentru a îndeplini sarcinile',
    workflowDescription: 'Construiește o aplicație care generează text de înaltă calitate pe baza unui flux de lucru orchestrat cu un grad ridicat de personalizare. Este potrivit pentru utilizatorii experimentați.',
    workflowWarning: 'În prezent în beta',
    chatbotType: 'Metodă de orchestrare a chatbot-ului',
    basic: 'De bază',
    basicTip: 'Pentru începători, se poate comuta la Chatflow mai târziu',
    basicFor: 'PENTRU ÎNCEPĂTORI',
    basicDescription: 'Orchestrarea de bază permite orchestrarea unei aplicații Chatbot folosind setări simple, fără posibilitatea de a modifica prompturile încorporate. Este potrivit pentru începători.',
    advanced: 'Chatflow',
    advancedFor: 'Pentru utilizatori avansați',
    advancedDescription: 'Orchestrarea fluxului de lucru orchestrează chatboți sub forma fluxurilor de lucru, oferind un grad ridicat de personalizare, inclusiv posibilitatea de a edita prompturile încorporate. Este potrivit pentru utilizatorii experimentați.',
    captionName: 'Pictogramă și nume aplicație',
    appNamePlaceholder: 'Dă-i aplicației tale un nume',
    captionDescription: 'Descriere',
    appDescriptionPlaceholder: 'Introduceți descrierea aplicației',
    useTemplate: 'Folosește acest șablon',
    previewDemo: 'Previzualizează demo',
    chatApp: 'Asistent',
    chatAppIntro:
      'Vreau să construiesc o aplicație bazată pe chat. Această aplicație folosește un format întrebare-răspuns, permițând mai multe runde de conversație continuă.',
    agentAssistant: 'Asistent Agent Nou',
    completeApp: 'Generator de text',
    completeAppIntro:
      'Vreau să creez o aplicație care generează text de înaltă calitate pe baza indicațiilor, cum ar fi generarea de articole, rezumate, traduceri și mai multe.',
    showTemplates: 'Vreau să aleg dintr-un șablon',
    hideTemplates: 'Înapoi la selecția modului',
    Create: 'Creează',
    Cancel: 'Anulează',
    nameNotEmpty: 'Numele nu poate fi gol',
    appTemplateNotSelected: 'Vă rugăm să selectați un șablon',
    appTypeRequired: 'Vă rugăm să selectați un tip de aplicație',
    appCreated: 'Aplicația a fost creată',
    appCreateFailed: 'Crearea aplicației a eșuat',
    caution: 'Prudență',
    appCreateDSLErrorPart2: 'Vrei să continui?',
    Confirm: 'Confirma',
    appCreateDSLErrorTitle: 'Incompatibilitate versiune',
    appCreateDSLWarning: 'Atenție: diferența de versiune DSL poate afecta anumite caracteristici',
    appCreateDSLErrorPart3: 'Versiunea DSL a aplicației curente:',
    appCreateDSLErrorPart1: 'A fost detectată o diferență semnificativă în versiunile DSL. Forțarea importului poate cauza funcționarea defectuoasă a aplicației.',
    appCreateDSLErrorPart4: 'Versiune DSL suportată de sistem:',
    chatbotShortDescription: 'Chatbot bazat pe LLM cu configurare simplă',
    forBeginners: 'Tipuri de aplicații mai simple',
    completionShortDescription: 'Asistent AI pentru sarcini de generare de text',
    agentUserDescription: 'Un agent inteligent capabil de raționament iterativ și utilizare autonomă a instrumentelor pentru a atinge obiectivele sarcinii.',
    workflowUserDescription: 'Construiește vizual fluxuri AI autonome cu simplitatea drag-and-drop.',
    optional: 'Facultativ',
    learnMore: 'Află mai multe',
    completionUserDescription: 'Construiește rapid un asistent AI pentru sarcinile de generare a textului cu o configurare simplă.',
    chatbotUserDescription: 'Construiți rapid un chatbot bazat pe LLM cu o configurare simplă. Puteți trece la Chatflow mai târziu.',
    advancedShortDescription: 'Flux de lucru îmbunătățit pentru conversații multi-tur',
    advancedUserDescription: 'Flux de lucru cu funcții suplimentare de memorie și interfață de chatbot.',
    noTemplateFoundTip: 'Încercați să căutați folosind cuvinte cheie diferite.',
    foundResults: '{{număr}} Rezultatele',
    foundResult: '{{număr}} Rezultat',
    noIdeaTip: 'Nicio idee? Consultați șabloanele noastre',
    noAppsFound: 'Nu s-au găsit aplicații',
    workflowShortDescription: 'Flux agentic pentru automatizări inteligente',
    agentShortDescription: 'Agent inteligent cu raționament și utilizare autonomă a uneltelor',
    noTemplateFound: 'Nu s-au găsit șabloane',
    forAdvanced: 'PENTRU UTILIZATORII AVANSAȚI',
    chooseAppType: 'Alegeți un tip de aplicație',
    dropDSLToCreateApp: 'Trageți fișierul DSL aici pentru a crea aplicația',
  },
  editApp: 'Editează Info',
  editAppTitle: 'Editează Info Aplicație',
  editDone: 'Informațiile despre aplicație au fost actualizate',
  editFailed: 'Actualizarea informațiilor despre aplicație a eșuat',
  iconPicker: {
    ok: 'OK',
    cancel: 'Anulează',
    emoji: 'Emoji',
    image: 'Imagine',
  },
  switch: 'Comută la Orchestrare Flux de Lucru',
  switchTipStart: 'O nouă copie a aplicației va fi creată pentru tine, iar noua copie va comuta la Orchestrare Flux de Lucru. Noua copie ',
  switchTip: 'nu va permite',
  switchTipEnd: ' comutarea înapoi la Orchestrare de Bază.',
  switchLabel: 'Copia aplicației care urmează să fie creată',
  removeOriginal: 'Șterge aplicația originală',
  switchStart: 'Începe comutarea',
  typeSelector: {
    all: 'TOATE Tipurile',
    chatbot: 'Chatbot',
    agent: 'Agent',
    workflow: 'Flux de lucru',
    completion: 'Finalizare',
    advanced: 'Fluxul de chat',
  },
  tracing: {
    title: 'Urmărirea performanței aplicației',
    description: 'Configurarea unui furnizor LLMOps terț și urmărirea performanței aplicației.',
    config: 'Configurare',
    collapse: 'Restrânge',
    expand: 'Extinde',
    tracing: 'Urmărire',
    disabled: 'Dezactivat',
    disabledTip: 'Vă rugăm să configurați mai întâi furnizorul',
    enabled: 'În serviciu',
    tracingDescription: 'Captează contextul complet al execuției aplicației, inclusiv apelurile LLM, context, prompt-uri, cereri HTTP și altele, către o platformă de urmărire terță.',
    configProviderTitle: {
      configured: 'Configurat',
      notConfigured: 'Configurați furnizorul pentru a activa urmărirea',
      moreProvider: 'Mai mulți furnizori',
    },
    arize: {
      title: 'Arize',
      description: 'Observabilitate LLM de nivel enterprise, evaluare online și offline, monitorizare și experimentare—alimentată de OpenTelemetry. Proiectată special pentru aplicații bazate pe LLM și agenți.',
    },
    phoenix: {
      title: 'Phoenix',
      description: 'Platformă open-source și bazată pe OpenTelemetry pentru observabilitate, evaluare, inginerie de prompturi și experimentare pentru fluxurile de lucru și agenții LLM.',
    },
    langsmith: {
      title: 'LangSmith',
      description: 'O platformă de dezvoltare all-in-one pentru fiecare etapă a ciclului de viață al aplicației bazate pe LLM.',
    },
    langfuse: {
      title: 'Langfuse',
      description: 'Urmărire, evaluări, gestionarea prompt-urilor și metrici pentru depanarea și îmbunătățirea aplicației dvs. LLM.',
    },
    inUse: 'În utilizare',
    configProvider: {
      title: 'Configurare ',
      placeholder: 'Introduceți {{key}}-ul dvs.',
      project: 'Proiect',
      publicKey: 'Cheie publică',
      secretKey: 'Cheie secretă',
      viewDocsLink: 'Vizualizați documentația {{key}}',
      removeConfirmTitle: 'Eliminați configurația {{key}}?',
      removeConfirmContent: 'Configurația curentă este în uz, eliminarea acesteia va dezactiva funcția de Urmărire.',
    },
    view: 'Vedere',
    opik: {
      description: 'Opik este o platformă open-source pentru evaluarea, testarea și monitorizarea aplicațiilor LLM.',
      title: 'Opik',
    },
    weave: {
      title: 'Împletește',
      description: 'Weave este o platformă open-source pentru evaluarea, testarea și monitorizarea aplicațiilor LLM.',
    },
    aliyun: {
      description: 'Platforma de observabilitate SaaS oferită de Alibaba Cloud permite monitorizarea, urmărirea și evaluarea aplicațiilor Dify din cutie.',
    },
  },
  answerIcon: {
    descriptionInExplore: 'Dacă să utilizați pictograma web app pentru a înlocui 🤖 în Explore',
    description: 'Dacă se utilizează pictograma web app pentru a înlocui 🤖 în aplicația partajată',
    title: 'Utilizați pictograma web app pentru a înlocui 🤖',
  },
  importFromDSL: 'Import din DSL',
  importFromDSLUrl: 'De la URL',
  importFromDSLUrlPlaceholder: 'Lipiți linkul DSL aici',
  importFromDSLFile: 'Din fișierul DSL',
  mermaid: {
    handDrawn: 'Desenat de mână',
    classic: 'Clasic',
  },
  openInExplore: 'Deschide în Explorează',
  newAppFromTemplate: {
    sidebar: {
      Writing: 'Scriere',
      Programming: 'Programare',
      Workflow: 'Flux de lucru',
      Agent: 'Agent',
      Assistant: 'Asistent',
      Recommended: 'Recomandat',
      HR: 'DOMN',
    },
    searchAllTemplate: 'Căutați toate șabloanele...',
    byCategories: 'DUPĂ CATEGORII',
  },
  showMyCreatedAppsOnly: 'Afișează doar aplicațiile create de mine',
  appSelector: {
    label: 'APLICAȚIE',
    params: 'PARAMETRII APLICAȚIEI',
    noParams: 'Nu sunt necesari parametri',
    placeholder: 'Selectați o aplicație...',
  },
  structOutput: {
    notConfiguredTip: 'Ieșirea structurată nu a fost configurată încă',
    LLMResponse: 'Răspuns LLM',
    required: 'Necesar',
    moreFillTip: 'Afișând maxim 10 niveluri de imbricare',
    structured: 'Structurat',
    modelNotSupported: 'Modelul nu este suportat',
    structuredTip: 'Ieșirile structurate sunt o caracteristică care asigură că modelul va genera întotdeauna răspunsuri care respectă schema JSON furnizată.',
    configure: 'Configurează',
    modelNotSupportedTip: 'Modelul actual nu suportă această funcție și este downgradat automat la injecția de prompt.',
  },
  accessItemsDescription: {
    specific: 'Numai grupuri sau membri specifici pot accesa aplicația web.',
    organization: 'Oricine din organizație poate accesa aplicația web',
    anyone: 'Oricine poate accesa aplicația web',
    external: 'Numai utilizatorii externi autentificați pot accesa aplicația web',
  },
  accessControlDialog: {
    accessItems: {
      anyone: 'Oricine are linkul',
      specific: 'Grupuri sau membri specifici',
      organization: 'Numai membrii din cadrul întreprinderii',
      external: 'Utilizatori extern autentificați',
    },
    operateGroupAndMember: {
      searchPlaceholder: 'Caută grupuri și membri',
      allMembers: 'Toți membrii',
      expand: 'Expandează',
      noResult: 'Niciun rezultat',
    },
    title: 'Controlul Accesului la Aplicația Web',
    description: 'Setați permisiunile de acces la aplicația web',
    accessLabel: 'Cine are acces',
    groups_one: '{{count}} GRUP',
    groups_other: '{{count}} GRUPURI',
    members_one: '{{count}} MEMBRU',
    members_other: '{{count}} MEMBRI',
    noGroupsOrMembers: 'Niciun grup sau membri selectați',
    webAppSSONotEnabledTip: 'Vă rugăm să contactați administratorul de întreprindere pentru a configura metoda de autentificare a aplicației web.',
    updateSuccess: 'Actualizare reușită',
  },
  publishApp: {
    title: 'Cine poate accesa aplicația web',
    notSet: 'Nu este setat',
    notSetDesc: 'În prezent, nimeni nu poate accesa aplicația web. Vă rugăm să setați permisiunile.',
  },
  accessControl: 'Controlul Accesului la Aplicația Web',
  noAccessPermission: 'Nici o permisiune pentru a accesa aplicația web',
}

export default translation
