const translation = {
  toVerified: '教育認証を取得',
  toVerifiedTip: {
    front: '現在、教育認証ステータスを取得する資格があります。以下に教育情報を入力し、認証プロセスを完了すると、Dify プロフェッショナルプランの',
    coupon: '100％割引クーポン',
    end: 'を受け取ることができます。',
  },
  currentSigned: '現在ログイン中のアカウントは',
  form: {
    schoolName: {
      title: '学校名',
      placeholder: '学校の正式名称（省略不可）を入力してください。',
    },
    schoolRole: {
      title: '学校での役割',
      option: {
        student: '学生',
        teacher: '教師',
        administrator: '学校管理者',
      },
    },
    terms: {
      title: '利用規約と同意事項',
      desc: {
        front: 'お客様の情報および 教育認証ステータス の利用は、当社の ',
        and: 'および',
        end: 'に従うものとします。送信することで以下を確認します：',
        termsOfService: '利用規約',
        privacyPolicy: 'プライバシーポリシー',
      },
      option: {
        age: '18 歳以上であることを確認します。',
        inSchool: '提供した教育機関に在籍または勤務している ことを確認します。Dify は在籍/雇用証明の提出を求める場合があります。不正な情報を申告した場合、教育認証に基づき免除された費用を支払うことに同意します。',
      },
    },
  },
  submit: '送信',
  submitError: 'フォームの送信に失敗しました。しばらくしてから再度ご提出ください。',
  learn: '教育認証の取得方法はこちら',
  successTitle: 'Dify 教育認証を取得しました！',
  successContent: 'お客様のアカウントに Dify プロフェッショナルプランの 100% 割引クーポン を発行しました。有効期間は 1 年間 ですので、期限内にご利用ください。',
  rejectTitle: 'Dify 教育認証が拒否されました',
  rejectContent: '申し訳ございませんが、このメールアドレスでは 教育認証 の資格を取得できず、Dify プロフェッショナルプランの 100％割引クーポン を受け取ることはできません。',
  emailLabel: '現在のメールアドレス',
}

export default translation
