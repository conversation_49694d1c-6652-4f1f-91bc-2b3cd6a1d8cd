const translation = {
  title: 'Test de Récupération',
  desc: 'Testez l\'effet d\'impact de la Connaissance basée sur le texte de la requête donnée.',
  dateTimeFormat: 'JJ/MM/AAAA hh:mm A',
  recents: 'Récents',
  table: {
    header: {
      source: 'Source',
      text: 'Texte',
      time: 'Temps',
    },
  },
  input: {
    title: 'Texte source',
    placeholder: 'V<PERSON><PERSON>z entrer un texte, une phrase déclarative courte est recommandée.',
    countWarning: 'Jusqu\'à 200 caractères.',
    indexWarning: 'Connaissances de haute qualité uniquement.',
    testing: 'Test',
  },
  hit: {
    title: 'PARAGRAPHES DE RÉCUPÉRATION',
    emptyTip: 'Les résultats des tests de récupération s\'afficheront ici',
  },
  noRecentTip: 'Aucun résultat de requête récent ici',
  viewChart: 'Voir GRAPHIQUE VECTORIEL',
  settingTitle: 'Réglage de récupération',
  viewDetail: 'Voir les détails',
  hitChunks: 'Appuyez sur {{num}} morceaux enfants',
  records: 'Archives',
  chunkDetail: 'Détail du morceau',
  open: 'Ouvrir',
  keyword: 'Mots-clés',
}

export default translation
