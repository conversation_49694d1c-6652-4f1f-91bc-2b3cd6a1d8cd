#!/usr/bin/env python3
"""
测试批量查询agent-records API的脚本 (POST方法)
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:5001"  # 根据实际部署情况调整
API_ENDPOINT = "/console/api/agent-records/batch"

def test_batch_api():
    """测试批量查询API"""
    
    # 测试参数
    test_cases = [
        {
            "name": "基本批量查询",
            "params": {
                "app_ids": ["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************"],
                "limit": 10
            }
        },
        {
            "name": "带时间范围的批量查询",
            "params": {
                "app_ids": ["550e8400-e29b-41d4-a716-************"],
                "start": "2024-01-01 00:00",
                "end": "2024-12-31 23:59",
                "limit": 20
            }
        },
        {
            "name": "单个应用查询",
            "params": {
                "app_ids": ["550e8400-e29b-41d4-a716-************"]
            }
        },
        {
            "name": "无效UUID格式测试",
            "params": {
                "app_ids": ["invalid-uuid", "550e8400-e29b-41d4-a716-************"]
            }
        },
        {
            "name": "空app_ids测试",
            "params": {
                "app_ids": []
            }
        },
        {
            "name": "多个应用查询",
            "params": {
                "app_ids": [
                    "550e8400-e29b-41d4-a716-************",
                    "550e8400-e29b-41d4-a716-************",
                    "550e8400-e29b-41d4-a716-************"
                ],
                "limit": 5
            }
        },
        {
            "name": "带total_limit的查询",
            "params": {
                "app_ids": [
                    "550e8400-e29b-41d4-a716-************",
                    "550e8400-e29b-41d4-a716-************",
                    "550e8400-e29b-41d4-a716-************"
                ],
                "limit": 10,
                "total_limit": 15
            }
        },
        {
            "name": "total_limit小于单个应用limit",
            "params": {
                "app_ids": [
                    "550e8400-e29b-41d4-a716-************",
                    "550e8400-e29b-41d4-a716-************"
                ],
                "limit": 20,
                "total_limit": 5
            }
        }
    ]
    
    print("开始测试批量查询API...")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['name']}")
        print(f"参数: {test_case['params']}")
        
        try:
            response = requests.post(
                f"{BASE_URL}{API_ENDPOINT}",
                json=test_case['params'],
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("响应成功!")
                print(f"返回的应用数量: {len(data.get('data', {}))}")
                
                # 打印元信息
                meta = data.get('meta', {})
                if meta:
                    print(f"元信息:")
                    print(f"  请求应用数: {meta.get('total_apps_requested', 0)}")
                    print(f"  返回应用数: {meta.get('total_apps_returned', 0)}")
                    print(f"  请求记录数: {meta.get('total_records_requested', 0)}")
                    print(f"  返回记录数: {meta.get('total_records_returned', 0)}")
                    print(f"  每应用限制: {meta.get('limit_per_app', 0)}")
                    if 'total_limit' in meta:
                        print(f"  总记录限制: {meta.get('total_limit', 0)}")
                
                # 打印每个应用的基本信息
                for app_id, app_data in data.get('data', {}).items():
                    if 'error' in app_data:
                        print(f"  - {app_id}: {app_data['error']}")
                    else:
                        print(f"  - {app_id}: {app_data.get('app_name', 'Unknown')}")
                        print(f"    运行次数: {app_data.get('runtime_info', {}).get('total_runs', 0)}")
                        print(f"    消息数量: {app_data.get('statistics', {}).get('total_messages', 0)}")
                        print(f"    调用记录数: {len(app_data.get('call_records', []))}")
            else:
                print(f"响应失败: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
        
        print("-" * 30)

def test_original_api():
    """测试原始单个应用API作为对比"""
    print("\n" + "=" * 50)
    print("测试原始单个应用API作为对比...")
    
    app_id = "550e8400-e29b-41d4-a716-************"  # 替换为实际的应用ID
    original_endpoint = f"/apps/{app_id}/agent-records"
    
    try:
        response = requests.get(
            f"{BASE_URL}{original_endpoint}",
            params={"limit": 10},
            timeout=30
        )
        
        print(f"原始API状态码: {response.status_code}")
        if response.status_code == 200:
            print("原始API响应成功!")
        else:
            print(f"原始API响应失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"原始API请求异常: {e}")

if __name__ == "__main__":
    print("批量查询Agent Records API测试 (POST方法)")
    print("请确保Dify API服务正在运行")
    print("请根据实际情况修改BASE_URL和测试用的app_ids")
    
    test_batch_api()
    test_original_api()
    
    print("\n测试完成!") 