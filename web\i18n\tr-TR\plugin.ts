const translation = {
  category: {
    models: 'Model',
    all: 'Tüm',
    bundles: '<PERSON><PERSON><PERSON>',
    agents: '<PERSON><PERSON>',
    tools: 'Araçları',
    extensions: 'Uzantı -ları',
  },
  categorySingle: {
    tool: 'Alet',
    bundle: '<PERSON><PERSON><PERSON>',
    extension: '<PERSON><PERSON><PERSON><PERSON>',
    agent: 'Temsil<PERSON> Stratejisi',
    model: 'Model',
  },
  list: {
    source: {
      github: 'GitHub\'dan yükleyin',
      marketplace: 'Marketten Yükleme',
      local: 'Yerel Paket Dosyasından Yükle',
    },
    noInstalled: 'Yüklü eklenti yok',
    notFound: 'Eklenti bulunamadı',
  },
  source: {
    github: 'GitHub (İngilizce)',
    marketplace: 'Pazar',
    local: 'Yerel Paket Dosyası',
  },
  detailPanel: {
    categoryTip: {
      marketplace: 'Marketplace\'ten yüklendi',
      local: 'Yerel Eklenti',
      debugging: 'Hata Ayıklama Eklentisi',
      github: 'Gith<PERSON>\'dan yükle<PERSON>',
    },
    operation: {
      install: 'Yüklemek',
      detail: '<PERSON><PERSON>',
      checkUpdate: 'Güncellemeyi Kontrol Et',
      remove: 'Kaldırmak',
      info: 'Eklenti Bilgileri',
      viewDetail: 'ayrıntılara bakın',
      update: 'Güncelleştirmek',
    },
    toolSelector: {
      uninstalledContent: 'Bu eklenti yerel/GitHub deposundan yüklenir. Lütfen kurulumdan sonra kullanın.',
      uninstalledLink: 'Eklentilerde Yönet',
      descriptionLabel: 'Araç açıklaması',
      auto: 'Otomatik',
      settings: 'KULLANICI AYARLARI',
      empty: 'Araç eklemek için \'+\' düğmesini tıklayın. Birden fazla araç ekleyebilirsiniz.',
      unsupportedContent: 'Yüklü eklenti sürümü bu eylemi sağlamaz.',
      paramsTip1: 'LLM çıkarım parametrelerini kontrol eder.',
      descriptionPlaceholder: 'Aletin amacının kısa açıklaması, örneğin belirli bir konum için sıcaklığı elde edin.',
      toolLabel: 'Alet',
      placeholder: 'Bir araç seçin...',
      title: 'Araç ekle',
      uninstalledTitle: 'Araç yüklü değil',
      unsupportedContent2: 'Sürümü değiştirmek için tıklayın.',
      params: 'AKIL YÜRÜTME YAPILANDIRMASI',
      paramsTip2: '\'Otomatik\' kapalıyken, varsayılan değer kullanılır.',
      unsupportedTitle: 'Desteklenmeyen Eylem',
      toolSetting: 'Araç Ayarları',
    },
    strategyNum: '{{sayı}} {{strateji}} DAHİL',
    switchVersion: 'Sürümü Değiştir',
    endpointDisableContent: '{{name}} öğesini devre dışı bırakmak ister misiniz?',
    endpointsDocLink: 'Belgeyi görüntüleyin',
    endpointsEmpty: 'Uç nokta eklemek için \'+\' düğmesini tıklayın',
    endpoints: 'Bitiş noktası',
    disabled: 'Sakat',
    endpointModalTitle: 'Uç noktayı ayarlama',
    configureModel: 'Modeli yapılandırma',
    actionNum: '{{sayı}} {{eylem}} DAHİL',
    configureTool: 'Aracı yapılandır',
    endpointsTip: 'Bu eklenti, uç noktalar aracılığıyla belirli işlevler sağlar ve geçerli çalışma alanı için birden çok uç nokta kümesi yapılandırabilirsiniz.',
    configureApp: 'Uygulamayı Yapılandır',
    endpointDeleteTip: 'Uç Noktayı Kaldır',
    endpointDeleteContent: '{{name}} öğesini kaldırmak ister misiniz?',
    endpointModalDesc: 'Yapılandırıldıktan sonra, eklenti tarafından API uç noktaları aracılığıyla sağlanan özellikler kullanılabilir.',
    modelNum: '{{sayı}} DAHİL OLAN MODELLER',
    endpointDisableTip: 'Uç Noktayı Devre Dışı Bırak',
    serviceOk: 'Servis Tamam',
  },
  debugInfo: {
    title: 'Hata ayıklama',
    viewDocs: 'Belgeleri Görüntüle',
  },
  privilege: {
    admins: 'Yöneticiler',
    whoCanDebug: 'Eklentilerde kimler hata ayıklayabilir?',
    everyone: 'Herkes',
    title: 'Eklenti Tercihleri',
    noone: 'Hiç kimse',
    whoCanInstall: 'Eklentileri kimler yükleyebilir ve yönetebilir?',
  },
  pluginInfoModal: {
    packageName: 'Paket',
    repository: 'Depo',
    release: 'Serbest bırakma',
    title: 'Eklenti bilgisi',
  },
  action: {
    checkForUpdates: 'Güncellemeleri kontrol et',
    deleteContentLeft: 'Kaldırmak ister misiniz',
    usedInApps: 'Bu eklenti {{num}} uygulamalarında kullanılıyor.',
    delete: 'Eklentiyi kaldır',
    pluginInfo: 'Eklenti bilgisi',
    deleteContentRight: 'eklenti?',
  },
  installModal: {
    labels: {
      repository: 'Depo',
      version: 'Sürüm',
      package: 'Paket',
    },
    back: 'Geri',
    installComplete: 'Kurulum tamamlandı',
    installing: 'Yükleme...',
    installedSuccessfully: 'Yükleme başarılı',
    installFailedDesc: 'Eklenti yüklenemedi, başarısız oldu.',
    fromTrustSource: 'Lütfen eklentileri yalnızca <trustSource>güvenilir bir kaynaktan</trustSource> yüklediğinizden emin olun.',
    uploadingPackage: '{{packageName}} yükleniyor...',
    readyToInstall: 'Aşağıdaki eklentiyi yüklemek üzere',
    next: 'Önümüzdeki',
    pluginLoadError: 'Eklenti yükleme hatası',
    install: 'Yüklemek',
    cancel: 'İptal',
    installedSuccessfullyDesc: 'Eklenti başarıyla yüklendi.',
    close: 'Kapatmak',
    uploadFailed: 'Karşıya yükleme başarısız oldu',
    installFailed: 'Yükleme başarısız oldu',
    pluginLoadErrorDesc: 'Bu eklenti yüklenmeyecek',
    readyToInstallPackage: 'Aşağıdaki eklentiyi yüklemek üzere',
    readyToInstallPackages: 'Aşağıdaki {{num}} eklentilerini yüklemek üzereyim',
    dropPluginToInstall: 'Yüklemek için eklenti paketini buraya bırakın',
    installPlugin: 'Eklentiyi Yükle',
    installWarning: 'Bu eklentinin yüklenmesine izin verilmemektedir.',
  },
  installFromGitHub: {
    installedSuccessfully: 'Yükleme başarılı',
    installFailed: 'Yükleme başarısız oldu',
    installNote: 'Lütfen eklentileri yalnızca güvenilir bir kaynaktan yüklediğinizden emin olun.',
    selectVersion: 'Sürümü seçin',
    selectPackage: 'Paket seç',
    installPlugin: 'GitHub\'dan eklenti yükleyin',
    selectPackagePlaceholder: 'Lütfen bir paket seçin',
    uploadFailed: 'Karşıya yükleme başarısız oldu',
    selectVersionPlaceholder: 'Lütfen bir sürüm seçin',
    gitHubRepo: 'GitHub deposu',
    updatePlugin: 'GitHub\'dan eklentiyi güncelleyin',
  },
  upgrade: {
    usedInApps: '{{num}} uygulamalarında kullanılır',
    upgrade: 'Yüklemek',
    title: 'Eklentiyi Yükle',
    successfulTitle: 'Yükleme başarılı',
    upgrading: 'Yükleme...',
    close: 'Kapatmak',
    description: 'Aşağıdaki eklentiyi yüklemek üzere',
  },
  error: {
    inValidGitHubUrl: 'Geçersiz GitHub URL\'si. Lütfen şu biçimde geçerli bir URL girin: https://github.com/owner/repo',
    fetchReleasesError: 'Sürümler alınamıyor. Lütfen daha sonra tekrar deneyin.',
    noReleasesFound: 'Yayın bulunamadı. Lütfen GitHub deposunu veya giriş URL\'sini kontrol edin.',
  },
  marketplace: {
    sortOption: {
      newlyReleased: 'Yeni Çıkanlar',
      mostPopular: 'En popüler',
      firstReleased: 'İlk Çıkanlar',
      recentlyUpdated: 'Son Güncelleme',
    },
    and: 've',
    empower: 'Yapay zeka geliştirmenizi güçlendirin',
    pluginsResult: '{{num}} sonuç',
    difyMarketplace: 'Dify Pazar Yeri',
    sortBy: 'Kara şehir',
    moreFrom: 'Marketplace\'ten daha fazlası',
    noPluginFound: 'Eklenti bulunamadı',
    viewMore: 'Daha fazla göster',
    discover: 'Keşfetmek',
    verifiedTip: 'Dify tarafından doğrulanmıştır.',
    partnerTip: 'Dify partner\'ı tarafından doğrulandı',
  },
  task: {
    installedError: '{{errorLength}} eklentileri yüklenemedi',
    clearAll: 'Tümünü temizle',
    installing: '{{installingLength}} eklentilerinin kurulumu, 0 bitti.',
    installingWithSuccess: '{{installingLength}} eklentileri yükleniyor, {{successLength}} başarılı.',
    installError: '{{errorLength}} eklentileri yüklenemedi, görüntülemek için tıklayın',
    installingWithError: '{{installingLength}} eklentileri yükleniyor, {{successLength}} başarılı, {{errorLength}} başarısız oldu',
  },
  allCategories: 'Tüm Kategoriler',
  installAction: 'Yüklemek',
  search: 'Aramak',
  install: '{{num}} yükleme',
  searchPlugins: 'Eklentileri ara',
  searchTools: 'Arama araçları...',
  fromMarketplace: 'Pazar Yerinden',
  installPlugin: 'Eklentiyi yükle',
  installFrom: 'ŞURADAN YÜKLE',
  from: 'Kaynak',
  endpointsEnabled: '{{num}} uç nokta kümesi etkinleştirildi',
  findMoreInMarketplace: 'Marketplace\'te daha fazla bilgi edinin',
  searchCategories: 'Arama Kategorileri',
  searchInMarketplace: 'Marketplace\'te arama yapma',
  metadata: {
    title: 'Eklentiler',
  },
  difyVersionNotCompatible: 'Mevcut Dify sürümü bu eklentiyle uyumlu değil, lütfen gerekli minimum sürüme güncelleyin: {{minimalDifyVersion}}',
  requestAPlugin: 'Bir eklenti iste',
  publishPlugins: 'Eklentileri yayınlayın',
}

export default translation
