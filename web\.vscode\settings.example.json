{"prettier.enable": false, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.format.enable": true, "[python]": {"editor.formatOnType": true}, "[html]": {"editor.defaultFormatter": "vscode.html-language-features"}, "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[javascriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[jsonc]": {"editor.defaultFormatter": "vscode.json-language-features"}, "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "npm.packageManager": "pnpm"}