#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the database migration version issue.
This script will update the alembic_version table to point to the latest migration.
"""

import psycopg2
from psycopg2 import sql

# Database connection parameters (from middleware.env)
DB_HOST = "localhost"
DB_PORT = 5432
DB_NAME = "dify"
DB_USER = "postgres"
DB_PASSWORD = "difyai123456"

def fix_migration_version():
    """Fix the migration version by updating the alembic_version table."""
    try:
        # Connect to the database
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        
        cursor = conn.cursor()
        
        # Get the latest migration version from the files
        # Based on the file listing, the latest version is fca025d3b60f
        latest_version = "fca025d3b60f"
        
        # Update the alembic_version table
        update_query = sql.SQL("UPDATE alembic_version SET version_num = %s")
        cursor.execute(update_query, (latest_version,))
        
        # Commit the changes
        conn.commit()
        
        print(f"Successfully updated alembic_version to: {latest_version}")
        
        # Verify the update
        cursor.execute("SELECT version_num FROM alembic_version")
        result = cursor.fetchone()
        print(f"Current version in database: {result[0] if result else 'None'}")
        
    except Exception as e:
        print(f"Error: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("Fixing database migration version...")
    fix_migration_version()
    print("Done!") 