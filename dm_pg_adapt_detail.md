# PostgreSQL / 达梦 (DM) 数据库动态适配改造详细文档

## 1. 核心目标与原则

本次改造的核心目标是在现有项目基础上，增加对达梦（DM）数据库的支持，并实现与原有 PostgreSQL (PG) 数据库的动态、灵活切换。

**核心原则:**
- **适配而非替换**：保留所有 PG 相关功能，禁止删除。
- **配置驱动切换**：通过环境变量（`.env` 文件）控制数据库类型的选择，应用启动时动态加载相应配置和驱动。
- **代码兼容性**：改造后的代码需同时兼容 PG 和 DM 两种数据库，将数据库特有逻辑隔离在底层。

## 2. 详细改造点 (按模块)

### 2.1. 环境配置 (`.env`)

- **文件路径**: 项目根目录下的 `.env` 文件。
- **改造方案**:
  1.  新增 `DB_TYPE` 变量，用于指定当前使用的数据库类型。
  2.  为两种数据库分别提供连接 URI。

- **代码示例**:
  ```dotenv
  # .env

  # Specify the database type: 'postgres' or 'dameng'
  DB_TYPE=postgres

  # PostgreSQL Connection URI
  DATABASE_URL_PG="postgresql+psycopg2://postgres:difyai123456@localhost:5432/dify"

  # Dameng Connection URI (Example)
  DATABASE_URL_DM="dameng+sqlalchemy_dameng://SYSDBA:SYSDBA@localhost:5236/dify"
  ```

### 2.2. 依赖管理 (`pyproject.toml`)

- **文件路径**: `api/pyproject.toml`
- **改造方案**:
  1.  保留现有的 PostgreSQL 驱动 `psycopg2-binary`.
  2.  在 `[project.dependencies]` 列表中，新增达梦数据库的 SQLAlchemy 方言/驱动包 (例如 `sqlalchemy-dameng`，具体包名需根据官方提供为准)。

- **代码示例 (`api/pyproject.toml`)**:
  ```toml
  # [project.dependencies]
  
  # ... existing dependencies
  psycopg2-binary~=2.9.6   # Keep for PG support
  sqlalchemy-dameng~=x.x.x  # Add Dameng driver/dialect
  # ... other dependencies
  ```

### 2.3. 数据库初始化入口 (`ext_database.py`)

- **文件路径**: `api/extensions/ext_database.py`
- **函数**: `init_app(app: DifyApp)`
- **改造方案**:
  此函数是实现动态切换的核心。需在此处读取环境变量，并根据 `DB_TYPE` 设置正确的数据库连接 URI 和 SQLAlchemy 的元数据(metadata)命名约定。

- **代码示例 (`api/extensions/ext_database.py`)**:
  ```python
  import os
  from dify_app import DifyApp
  # Import db object and naming conventions from models.engine
  from models.engine import db, POSTGRES_INDEXES_NAMING_CONVENTION 

  # Define Dameng naming convention here or in models.engine.py
  DAMENG_INDEXES_NAMING_CONVENTION = {
      "ix": "ix_%(column_0_label)s",
      "uq": "uq_%(table_name)s_%(column_0_name)s",
      "ck": "ck_%(table_name)s_%(constraint_name)s",
      "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
      "pk": "pk_%(table_name)s"
  }

  def init_app(app: DifyApp):
      db_type = os.getenv('DB_TYPE', 'postgres')

      if db_type == 'dameng':
          app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL_DM')
          db.metadata.naming_convention = DAMENG_INDEXES_NAMING_CONVENTION
      else: # Default to postgres
          app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL_PG')
          db.metadata.naming_convention = POSTGRES_INDEXES_NAMING_CONVENTION
      
      db.init_app(app)
  ```

### 2.4. ORM 核心 (`engine.py`)

- **文件路径**: `api/models/engine.py`
- **改造方案**:
  此文件定义了全局的 `db` 对象和 `metadata`。为配合 `ext_database.py` 的改造，主要确保 `POSTGRES_INDEXES_NAMING_CONVENTION` 的定义清晰，以及提供达梦的命名规范（可定义在此文件或 `ext_database.py` 中）。

- **代码示例 (`api/models/engine.py`)**:
  ```python
  from flask_sqlalchemy import SQLAlchemy
  from sqlalchemy import MetaData

  POSTGRES_INDEXES_NAMING_CONVENTION = {
      "ix": "%(column_0_label)s_idx",
      "uq": "%(table_name)s_%(column_0_name)s_key",
      "ck": "%(table_name)s_%(constraint_name)s_check",
      "fk": "%(table_name)s_%(column_0_name)s_fkey",
      "pk": "%(table_name)s_pkey",
  }
  
  # The metadata object will be configured dynamically in ext_database.py
  # We initialize it with the default (PG) convention.
  metadata = MetaData(naming_convention=POSTGRES_INDEXES_NAMING_CONVENTION)

  # ... (warning comments)
  
  db = SQLAlchemy(metadata=metadata)
  ```

### 2.5. 数据库工具脚本 (`db_handle_tools/`)

- **文件路径**: `nanwang_dify/db_handle_tools/` 下的所有 `.py` 文件，如 `reset_database.py`, `fix_migration.py` 等。
- **核心问题**: 这些脚本硬编码了 `psycopg2` 和 PG 特定的 SQL 语法。
- **改造方案**:
  1.  **抽象化数据库连接**: 创建一个统一的数据库连接函数，根据 `DB_TYPE` 环境变量返回相应的数据库连接对象。
  2.  **SQL 语法兼容**: 对脚本中所有手写的 SQL 语句（如 `DROP DATABASE`, `UPDATE alembic_version`）进行条件判断，根据 `DB_TYPE` 执行对应数据库的语法。

- **改造示例 (以 `reset_database.py` 为例)**:

  - **`reset_database.py` - `drop_database()` 函数**:
    ```python
    # nanwang_dify/db_handle_tools/reset_database.py

    # ... imports (add os)
    import os
    # Hypothetical Dameng driver
    # import sqlalchemy_dameng 

    def get_db_connection(db_name="postgres"):
        db_type = os.getenv('DB_TYPE', 'postgres')
        if db_type == 'dameng':
            # Use Dameng connection logic
            # This is a placeholder, actual connection may vary
            return sqlalchemy_dameng.connect(user=DB_USER, password=DB_PASSWORD, host=DB_HOST, port=DB_PORT, database=db_name)
        else:
            # Keep original psycopg2 logic
            return psycopg2.connect(
                host=DB_HOST, port=DB_PORT, database=db_name,
                user=DB_USER, password=DB_PASSWORD
            )

    def drop_database():
        conn = None
        try:
            # Use the abstracted connection function
            conn = get_db_connection() 
            conn.autocommit = True
            cursor = conn.cursor()

            db_type = os.getenv('DB_TYPE', 'postgres')
            if db_type == 'dameng':
                # Dameng might have a different syntax or might not support DROP DATABASE IF EXISTS
                # This needs to be verified.
                drop_query = sql.SQL("DROP DATABASE {}").format(sql.Identifier(DB_NAME))
            else:
                drop_query = sql.SQL("DROP DATABASE IF EXISTS {}").format(sql.Identifier(DB_NAME))
            
            cursor.execute(drop_query)
            print(f"✓ Successfully dropped database: {DB_NAME}")

        except Exception as e:
            print(f"✗ Error dropping database: {e}")
        finally:
            if conn:
                conn.close()

    # Apply similar logic to create_database() and all other scripts in this directory.
    ```
  - **其他脚本 (`fix_migration.py`, `fix_migration_advanced.py`)**: 需同样应用 `get_db_connection` 抽象，并检查 `UPDATE alembic_version` 等SQL的兼容性。

### 2.6. 数据库迁移 (`Alembic`)

- **文件路径**: `api/migrations/` (如果存在)
- **改造方案**:
  1.  Alembic 的迁移脚本（位于 `versions/` 目录下）是自动生成的，但如果其中包含了手写的`op.execute("...")`，则必须检查其 SQL 语法的跨数据库兼容性。
  2.  对所有手写的 SQL，需要增加 `DB_TYPE` 的判断，执行对应数据库的语句。
  3.  测试 `flask db migrate` 和 `flask db upgrade` 命令在两种数据库配置下是否都能正常工作。

## 3. 总结

本次改造工作量**中等偏大**，核心挑战在于将现有写死对 PG 的依赖，改造为一套可动态配置、代码兼容的适配层。主要工作将集中在**初始化逻辑重构**、**工具脚本适配**以及**全面的兼容性测试**上。业务层代码由于使用了 SQLAlchemy ORM，预计改动较小，但仍需排查是否存在原生 SQL 查询。