"""
本地插件管理器
负责扫描、上传、分配本地插件给所有租户
使用Dify标准配置系统
"""

import os
import sys
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any

# Add the 'api' directory to sys.path to resolve modules like 'models', 'configs'
# This is necessary because this script is in 'plugins/' but needs 'api/' modules,
# and we assume the app is run from the 'api/' directory.
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..')
api_dir = os.path.join(project_root, 'api')
if api_dir not in sys.path:
    sys.path.insert(0, api_dir)

logger = logging.getLogger(__name__)


class LocalPluginManager:
    """本地插件管理器"""
    
    def __init__(self, plugins_dir: str = None):
        """
        初始化本地插件管理器
        
        Args:
            plugins_dir: 本地插件目录路径，如果为None则使用配置中的路径
        """
        # 延迟导入配置，避免在初始化时出错
        self._dify_config = None
        self._db = None
        self._tenant_class = None
        self._tenant_status_class = None
        self._plugin_installer_class = None
        self._plugin_installation_source_class = None
        
        if plugins_dir is None:
            # 尝试获取配置中的插件目录路径
            try:
                from configs import dify_config
                plugins_dir = os.path.join(project_root, dify_config.LOCAL_PLUGIN_AUTO_INSTALL_DIR)
            except ImportError:
                # 如果无法导入配置，使用默认路径
                plugins_dir = os.path.join(project_root, "plugins/packages")
        
        self.plugins_dir = plugins_dir
        self.uploaded_plugins = {}  # 缓存已上传的插件 {plugin_file_name: plugin_unique_identifier}
        
    def _get_dify_config(self):
        """延迟获取配置"""
        if self._dify_config is None:
            try:
                from configs import dify_config
                self._dify_config = dify_config
            except ImportError:
                raise ImportError("无法导入Dify配置，请确保在正确的环境中运行")
        return self._dify_config
    
    def _get_db(self):
        """延迟获取数据库"""
        if self._db is None:
            try:
                from extensions.ext_database import db
                self._db = db
            except ImportError:
                raise ImportError("无法导入数据库模块，请确保在正确的环境中运行")
        return self._db
    
    def _get_tenant_class(self):
        """延迟获取租户类"""
        if self._tenant_class is None:
            try:
                from models.account import Tenant
                self._tenant_class = Tenant
            except ImportError:
                raise ImportError("无法导入租户模型，请确保在正确的环境中运行")
        return self._tenant_class
    
    def _get_tenant_status_class(self):
        """延迟获取租户状态类"""
        if self._tenant_status_class is None:
            try:
                from models.account import TenantStatus
                self._tenant_status_class = TenantStatus
            except ImportError:
                raise ImportError("无法导入租户状态模型，请确保在正确的环境中运行")
        return self._tenant_status_class
    
    def _get_plugin_installer_class(self):
        """延迟获取插件安装器类"""
        if self._plugin_installer_class is None:
            try:
                from core.plugin.impl.plugin import PluginInstaller
                self._plugin_installer_class = PluginInstaller
            except ImportError:
                raise ImportError("无法导入插件安装器，请确保在正确的环境中运行")
        return self._plugin_installer_class
    
    def _get_plugin_installation_source_class(self):
        """延迟获取插件安装源类"""
        if self._plugin_installation_source_class is None:
            try:
                from core.plugin.entities.plugin import PluginInstallationSource
                self._plugin_installation_source_class = PluginInstallationSource
            except ImportError:
                raise ImportError("无法导入插件安装源，请确保在正确的环境中运行")
        return self._plugin_installation_source_class
        
    def ensure_plugins_dir(self):
        """确保插件目录存在"""
        os.makedirs(self.plugins_dir, exist_ok=True)
    
    def should_install_plugin(self, plugin_name: str) -> bool:
        """
        判断是否应该安装指定插件
        
        Args:
            plugin_name: 插件名称（不包含扩展名）
            
        Returns:
            bool: 是否应该安装
        """
        try:
            dify_config = self._get_dify_config()
            
            # 检查是否在排除列表中
            if plugin_name in dify_config.LOCAL_PLUGIN_AUTO_INSTALL_EXCLUDE_PLUGINS_LIST:
                logger.info(f"插件 {plugin_name} 在排除列表中，跳过安装")
                return False
            
            # 检查是否在包含列表中（如果包含列表不为空）
            if dify_config.LOCAL_PLUGIN_AUTO_INSTALL_INCLUDE_PLUGINS_LIST and \
               plugin_name not in dify_config.LOCAL_PLUGIN_AUTO_INSTALL_INCLUDE_PLUGINS_LIST:
                logger.info(f"插件 {plugin_name} 不在包含列表中，跳过安装")
                return False
            
            return True
        except ImportError:
            # 如果无法获取配置，默认安装所有插件
            logger.warning("无法获取配置，默认安装所有插件")
            return True
        
    def get_all_tenant_ids(self) -> List[str]:
        """获取所有正常状态的租户ID列表"""
        try:
            db = self._get_db()
            Tenant = self._get_tenant_class()
            TenantStatus = self._get_tenant_status_class()
            
            # 检查是否在Flask应用上下文中
            try:
                from flask import current_app
                if not current_app:
                    logger.warning("不在Flask应用上下文中，无法查询租户")
                    return []
            except ImportError:
                logger.warning("无法导入Flask，可能不在应用上下文中")
                return []
            
            tenants = db.session.query(Tenant).filter(
                Tenant.status == TenantStatus.NORMAL
            ).all()
            return [tenant.id for tenant in tenants]
        except Exception as e:
            logger.error(f"获取租户列表失败: {str(e)}")
            return []
    
    def scan_plugin_files(self) -> List[Path]:
        """扫描插件目录，获取所有插件包文件"""
        self.ensure_plugins_dir()
        plugins_path = Path(self.plugins_dir)
        
        if not plugins_path.exists():
            logger.warning(f"插件目录不存在: {self.plugins_dir}")
            return []
        
        plugin_files = []
        # 支持的插件文件格式
        supported_extensions = ['.difypkg', '.zip', '.tar.gz']
        
        for ext in supported_extensions:
            plugin_files.extend(plugins_path.glob(f"*{ext}"))
        
        # 过滤插件文件
        filtered_plugin_files = []
        for plugin_file in plugin_files:
            plugin_name = plugin_file.stem.split('-')[0]  # 获取插件名 (例如 'google_search-0.0.1' -> 'google_search')
            if self.should_install_plugin(plugin_name):
                filtered_plugin_files.append(plugin_file)
            else:
                logger.info(f"跳过插件文件: {plugin_file.name}")
        
        logger.info(f"在目录 {self.plugins_dir} 中找到 {len(filtered_plugin_files)} 个可安装的插件文件（总共 {len(plugin_files)} 个）")
        return filtered_plugin_files
    
    def upload_plugin_package(self, tenant_id: str, plugin_file: Path) -> Optional[str]:
        """
        上传插件包到指定租户
        
        Args:
            tenant_id: 租户ID
            plugin_file: 插件文件路径
            
        Returns:
            plugin_unique_identifier: 插件唯一标识符，失败返回None
        """
        try:
            dify_config = self._get_dify_config()
            PluginInstaller = self._get_plugin_installer_class()
            plugin_installer = PluginInstaller()
            
            # 检查是否已经上传过该插件
            plugin_filename = plugin_file.name
            if plugin_filename in self.uploaded_plugins and not dify_config.LOCAL_PLUGIN_AUTO_INSTALL_FORCE_REINSTALL:
                logger.info(f"插件 {plugin_filename} 已经上传过，使用缓存的标识符")
                return self.uploaded_plugins[plugin_filename]
            
            logger.info(f"正在为租户 {tenant_id} 上传插件: {plugin_filename}")
            
            with open(plugin_file, 'rb') as f:
                plugin_content = f.read()
            
            # 上传插件包
            response = plugin_installer.upload_pkg(
                tenant_id=tenant_id,
                pkg=plugin_content,
                verify_signature=dify_config.LOCAL_PLUGIN_AUTO_INSTALL_VERIFY_SIGNATURE
            )

            plugin_unique_identifier = response.unique_identifier

            # 缓存已上传的插件
            self.uploaded_plugins[plugin_filename] = plugin_unique_identifier
            
            logger.info(f"插件包上传成功: {plugin_unique_identifier}")
            return plugin_unique_identifier
            
        except Exception as e:
            logger.error(f"上传插件 {plugin_file.name} 到租户 {tenant_id} 失败: {str(e)}", exc_info=True)
            return None
    
    def install_plugin_to_tenant(self, tenant_id: str, plugin_unique_identifier: str) -> bool:
        """
        将插件分配给指定租户
        
        Args:
            tenant_id: 租户ID
            plugin_unique_identifier: 插件唯一标识符
            
        Returns:
            str: "installed", "skipped", "failed"
        """
        try:
            dify_config = self._get_dify_config()
            PluginInstaller = self._get_plugin_installer_class()
            PluginInstallationSource = self._get_plugin_installation_source_class()
            # 引入特定的异常类
            from core.plugin.impl.exc import PluginDaemonBadRequestError
            plugin_installer = PluginInstaller()
            
            # 如果是强制重装，先尝试卸载
            if dify_config.LOCAL_PLUGIN_AUTO_INSTALL_FORCE_REINSTALL:
                try:
                    # 1. 根据 unique_identifier 查询已安装的插件信息
                    installed_plugins = plugin_installer.list_plugins(tenant_id)
                    plugin_to_uninstall = next((p for p in installed_plugins if p.plugin_unique_identifier == plugin_unique_identifier), None)

                    # 2. 如果找到了已安装的插件，执行卸载
                    if plugin_to_uninstall:
                        logger.info(f"强制重装：正在为租户 {tenant_id} 卸载旧版插件 {plugin_unique_identifier} (安装ID: {plugin_to_uninstall.id})")
                        plugin_installer.uninstall(tenant_id=tenant_id, plugin_installation_id=plugin_to_uninstall.id)
                        logger.info(f"旧版插件 {plugin_unique_identifier} 卸载成功")
                    else:
                        logger.info(f"插件 {plugin_unique_identifier} 在租户 {tenant_id} 中未找到，无需卸载")

                except Exception as uninstall_e:
                    # 如果卸载失败，记录警告但继续尝试安装
                    logger.warning(f"卸载插件 {plugin_unique_identifier} 时出现问题（可能无需处理）: {str(uninstall_e)}")

            logger.info(f"正在为租户 {tenant_id} 分配插件: {plugin_unique_identifier}")
            
            # 分配插件给租户
            response = plugin_installer.install_from_identifiers(
                tenant_id=tenant_id,
                identifiers=[plugin_unique_identifier],
                source=PluginInstallationSource.Package,
                metas=[{}]
            )
            
            logger.info(f"插件分配成功，任务ID: {response.task_id}")
            return "installed"
            
        except PluginDaemonBadRequestError as e:
            # 判断是否是“插件已安装”的错误
            if "plugin already installed" in str(e).lower():
                logger.info(f"租户 {tenant_id} 的插件 {plugin_unique_identifier} 已存在，跳过安装")
                return "skipped"
            else:
                logger.error(f"为租户 {tenant_id} 分配插件 {plugin_unique_identifier} 失败（请求错误）: {str(e)}", exc_info=True)
                return "failed"
        except Exception as e:
            logger.error(f"为租户 {tenant_id} 分配插件 {plugin_unique_identifier} 失败（未知错误）: {str(e)}", exc_info=True)
            return "failed"
    
    def install_plugin_for_tenant(self, tenant_id: str, plugin_file: Path) -> str:
        """
        为指定租户安装插件（上传+分配）
        
        Args:
            tenant_id: 租户ID
            plugin_file: 插件文件路径
            
        Returns:
            str: "installed", "skipped", "failed"
        """
        # 1. 上传插件包
        plugin_unique_identifier = self.upload_plugin_package(tenant_id, plugin_file)
        if not plugin_unique_identifier:
            return "failed"
        
        # 2. 分配插件给租户
        return self.install_plugin_to_tenant(tenant_id, plugin_unique_identifier)
    
    def install_plugins_for_all_tenants(self) -> Dict[str, Any]:
        """
        为所有租户安装本地插件
        
        Returns:
            安装结果统计
        """
        plugin_files = self.scan_plugin_files()
        tenant_ids = self.get_all_tenant_ids()
        
        if not plugin_files:
            logger.warning("没有找到可安装的插件文件")
            return {"installed": 0, "skipped": 0, "failed": 0, "total": 0, "details": []}
        
        if not tenant_ids:
            logger.warning("没有找到可用的租户")
            return {"installed": 0, "skipped": 0, "failed": 0, "total": 0, "details": []}
        
        results = {
            "installed": 0,
            "skipped": 0,
            "failed": 0,
            "total": len(plugin_files) * len(tenant_ids),
            "details": []
        }
        
        logger.info(f"开始为 {len(tenant_ids)} 个租户安装/同步 {len(plugin_files)} 个插件")
        
        for plugin_file in plugin_files:
            plugin_results = {
                "plugin_file": plugin_file.name,
                "tenant_results": []
            }
            
            for tenant_id in tenant_ids:
                status = self.install_plugin_for_tenant(tenant_id, plugin_file)
                
                plugin_results["tenant_results"].append({
                    "tenant_id": tenant_id,
                    "status": status
                })
                
                if status in results:
                    results[status] += 1
            
            results["details"].append(plugin_results)
        
        logger.info(f"插件安装/同步完成：新增安装 {results['installed']} (用户), 已同步 {results['skipped']} (用户), 失败 {results['failed']} (用户) / 总计 {results['total']} (次)")
        return results
    
    def install_plugins_for_single_tenant(self, tenant_id: str) -> Dict[str, Any]:
        """
        为单个租户安装所有本地插件
        
        Args:
            tenant_id: 租户ID
            
        Returns:
            安装结果统计
        """
        plugin_files = self.scan_plugin_files()
        
        if not plugin_files:
            logger.warning("没有找到可安装的插件文件")
            return {"installed": 0, "skipped": 0, "failed": 0, "total": 0, "details": []}
        
        results = {
            "installed": 0,
            "skipped": 0,
            "failed": 0,
            "total": len(plugin_files),
            "tenant_id": tenant_id,
            "details": []
        }
        
        logger.info(f"开始为租户 {tenant_id} 安装/同步 {len(plugin_files)} 个插件")
        
        for plugin_file in plugin_files:
            status = self.install_plugin_for_tenant(tenant_id, plugin_file)
            
            results["details"].append({
                "plugin_file": plugin_file.name,
                "status": status
            })
            
            if status in results:
                results[status] += 1
        
        logger.info(f"为租户 {tenant_id} 安装/同步插件完成：新增安装 {results['installed']}, 已同步 {results['skipped']}, 失败 {results['failed']} / 总计 {results['total']}")
        return results


# 全局实例
_local_plugin_manager = None


def get_local_plugin_manager(plugins_dir: str = None) -> LocalPluginManager:
    """获取本地插件管理器实例"""
    global _local_plugin_manager
    if _local_plugin_manager is None:
        _local_plugin_manager = LocalPluginManager(plugins_dir)
    return _local_plugin_manager


def auto_install_plugins_for_all_tenants(plugins_dir: str = None) -> Dict[str, Any]:
    """
    为所有租户自动安装本地插件（便捷函数）
    
    Args:
        plugins_dir: 插件目录路径
        
    Returns:
        安装结果统计
    """
    manager = get_local_plugin_manager(plugins_dir)
    return manager.install_plugins_for_all_tenants()


def auto_install_plugins_for_tenant(tenant_id: str, plugins_dir: str = None) -> Dict[str, Any]:
    """
    为指定租户自动安装本地插件（便捷函数）
    
    Args:
        tenant_id: 租户ID
        plugins_dir: 插件目录路径
        
    Returns:
        安装结果统计
    """
    manager = get_local_plugin_manager(plugins_dir)
    return manager.install_plugins_for_single_tenant(tenant_id) 