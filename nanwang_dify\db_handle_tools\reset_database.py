"""  """#!/usr/bin/env python3
from utils.tools import ROOT_PATH

"""
<PERSON><PERSON>t to reset the database by dropping and recreating it, then running all migrations.
"""

import psycopg2
from psycopg2 import sql
import subprocess
import sys
import os
import time

# Database connection parameters
DB_HOST = "localhost"
DB_PORT = 5432
DB_NAME = "dify"
DB_USER = "postgres"
DB_PASSWORD = "difyai123456"

def drop_database():
    """Drop the dify database if it exists."""
    try:
        # Connect to postgres database (not dify)
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            database="postgres",
            user=DB_USER,
            password=DB_PASSWORD
        )

        conn.autocommit = True  # Required for DROP DATABASE
        cursor = conn.cursor()

        # Drop the dify database if it exists
        drop_query = sql.SQL("DROP DATABASE IF EXISTS {}")
        cursor.execute(drop_query.format(sql.Identifier(DB_NAME)))

        print(f"✓ Successfully dropped database: {DB_NAME}")

    except Exception as e:
        print(f"✗ Error dropping database: {e}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def create_database():
    """Create the dify database."""
    try:
        # Connect to postgres database
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            database="postgres",
            user=DB_USER,
            password=DB_PASSWORD
        )

        conn.autocommit = True  # Required for CREATE DATABASE
        cursor = conn.cursor()

        # Create the dify database
        create_query = sql.SQL("CREATE DATABASE {}")
        cursor.execute(create_query.format(sql.Identifier(DB_NAME)))

        print(f"✓ Successfully created database: {DB_NAME}")

    except Exception as e:
        print(f"✗ Error creating database: {e}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def run_flask_command(command):
    """Run a flask command in the api directory."""
    try:
        # Change to api directory
        api_dir = os.path.join(ROOT_PATH, 'api')
        if not os.path.exists(api_dir):
            print(f"Error: api directory not found at {api_dir}")
            return False

        # Run the command
        result = subprocess.run(
            ['uv', 'run', 'flask'] + command.split(),
            cwd=api_dir,
            capture_output=True,
            text=True
        )

        print("STDOUT:", result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)

        return result.returncode == 0

    except Exception as e:
        print(f"Error running flask command: {e}")
        return False

def main():
    print("=== Database Reset and Migration Process ===")

    # Step 1: Drop the existing database
    print("\n1. Dropping existing database...")
    drop_database()

    # Step 2: Create a new database
    print("\n2. Creating new database...")
    create_database()

    # Step 3: Wait a moment for database to be ready
    print("\n3. Waiting for database to be ready...")
    time.sleep(2)

    # Step 4: Run all migrations from scratch
    print("\n4. Running all migrations from scratch...")
    if run_flask_command("db upgrade"):
        print("✓ All migrations applied successfully")
    else:
        print("✗ Migration application failed")
        return

    # Step 5: Generate new migration for account table changes
    print("\n5. Generating new migration for account table changes...")
    if run_flask_command("db migrate -m add_new_fields_to_account_table"):
        print("✓ New migration generated successfully")
    else:
        print("✗ Migration generation failed")
        return

    # Step 6: Apply the new migration
    print("\n6. Applying the new migration...")
    if run_flask_command("db upgrade"):
        print("✓ New migration applied successfully")
    else:
        print("✗ New migration application failed")
        return

    print("\n=== Database reset and migration completed successfully! ===")
    print("Your account table now has all the new fields you added.")

if __name__ == "__main__":
    # Ask for confirmation
    print("WARNING: This will completely delete and recreate the dify database!")
    print("All existing data will be lost!")
    response = input("Are you sure you want to continue? (yes/no): ")

    if response.lower() in ['yes', 'y']:
        main()
    else:
        print("Operation cancelled.")
