import os
import csv
import re

def get_python_files(directory='.'):
    """
    获取指定目录下所有的Python文件及其相对路径

    Args:
        directory: 要搜索的目录，默认为当前目录

    Returns:
        list: 包含所有Python文件相对路径的列表
    """
    python_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                # 获取相对路径
                relative_path = os.path.relpath(os.path.join(root, file), directory)
                python_files.append(relative_path)
    return python_files

def get_file_content(file_path, max_lines=10):
    """
    读取文件内容，去除import语句，返回前N行代码

    Args:
        file_path: 文件路径
        max_lines: 需要返回的最大行数

    Returns:
        str: 处理后的文件内容
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # 去除import语句
        filtered_lines = []
        for line in lines:
            # 使用正则表达式匹配import语句
            if not re.match(r'^\s*(from\s+\w+\s+)?import\s+', line.strip()):
                filtered_lines.append(line.strip())

        # 返回前N行非空内容
        non_empty_lines = [line for line in filtered_lines if line.strip()]
        return '\n'.join(non_empty_lines[:max_lines])
    except Exception as e:
        return f"Error reading file: {str(e)}"

def save_to_csv():
    """
    将Python文件路径和内容保存到CSV文件
    """
    # 获取所有Python文件
    python_files = get_python_files()

    # 保存文件路径
    with open('python_files_paths.csv', 'w', encoding='utf-8-sig', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['File Path'])  # 写入表头
        for file_path in python_files:
            writer.writerow([file_path])

    # 保存文件内容
    with open('python_files_content.csv', 'w', encoding='utf-8-sig', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['File Path', 'Content'])  # 写入表头
        for file_path in python_files:
            content = get_file_content(file_path)
            writer.writerow([file_path, content])

def main():
    try:
        save_to_csv()
        print("Files have been successfully processed and saved to CSV files.")
    except Exception as e:
        print(f"An error occurred: {str(e)}")

if __name__ == '__main__':
    main()
