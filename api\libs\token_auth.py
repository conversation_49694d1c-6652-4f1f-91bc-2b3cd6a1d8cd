"""
Token认证和用户管理模块
提供token验证、用户查找/创建等功能的装饰器和工具函数
"""

import logging
import secrets
import string
from functools import wraps
from typing import Dict, Any, Optional

from flask import request, g
from werkzeug.exceptions import Unauthorized

from extensions.ext_database import db
from libs.token_utils import TokenUtils, TokenError
from models import Account, Tenant, TenantAccountJoin
from services.account_service import AccountService


class TokenAuthService:
    """Token认证服务类"""

    @staticmethod
    def get_token_from_request() -> Optional[str]:
        """从请求中获取token"""
        return TokenUtils.get_token(request)

    @staticmethod
    def validate_and_parse_token(token: str) -> Dict[str, Any]:
        """验证并解析token - 通过解析成功与否判断验证结果"""
        try:
            # 使用TokenUtils解析token获取用户信息（解析成功即表示token正确）
            user_info = TokenUtils.parse_token(token)

            # 验证必要的用户信息字段
            userid = user_info.get('userid') or user_info.get('userId') or user_info.get('employeeId')
            username = user_info.get('username') or user_info.get('employeeName') or user_info.get('account')

            if not userid or not username:
                raise TokenError(
                    "token中缺少必要的用户标识信息",
                    "TOKEN_004",
                    f"缺少userid({userid})或username({username})"
                )

            # logging.info(f"Token解析成功，验证通过，用户: {username} (ID: {userid})")
            return user_info

        except TokenError:
            # 重新抛出TokenError
            raise
        except Exception as e:
            # 其他异常转换为Unauthorized
            raise Unauthorized(f"Token验证失败: {str(e)}")

    @staticmethod
    def find_or_create_account(user_info: Dict[str, Any]) -> Account:
        """根据token中的用户信息查找或创建Dify账户"""
        # 获取用户标识信息
        userid = user_info.get('userid') or user_info.get('userId') or user_info.get('employeeId')
        username = user_info.get('username') or user_info.get('employeeName') or user_info.get('account')
        user_nickname = user_info.get('user_nickname') or username
        email = user_info.get('email', f"{username}@dify.local")  # 如果没有email，生成一个默认的

        logging.info(f"查找或创建用户: userid={userid}, username={username}")

        # 首先尝试通过user_id字段查找
        account = db.session.query(Account).filter(Account.user_id == str(userid)).first()

        if account:
            logging.info(f"找到现有用户: {account.name} (ID: {account.id})")
            # 可选：更新用户信息
            TokenAuthService.sync_user_info(account, user_info)
            return account

        # 如果通过user_id找不到，尝试通过login_name查找
        account = db.session.query(Account).filter(Account.login_name == username).first()

        if account:
            # 更新user_id字段
            account.user_id = str(userid)
            db.session.commit()
            logging.info(f"找到现有用户并更新user_id: {account.name} (ID: {account.id})")
            TokenAuthService.sync_user_info(account, user_info)
            return account

        # 用户不存在，创建新账户
        logging.info(f"创建新用户: {username}")
        try:
            # 使用AccountService创建账户和租户
            account = AccountService.create_account_and_tenant(
                email=email,
                name=user_nickname,
                interface_language="zh-Hans",
                password=TokenAuthService.generate_random_password(),
                is_setup=True
            )

            # 设置自定义字段
            account.user_id = str(userid)
            account.login_name = username
            account.status = "active"
            account.user_type = "EMPLOYEE"

            # 同步其他用户信息
            TokenAuthService.sync_user_info(account, user_info)

            # 确保租户关系正确设置
            if not account.current_tenant:
                # 重新查询租户关系
                tenant_join = db.session.query(TenantAccountJoin).filter(
                    TenantAccountJoin.account_id == account.id
                ).first()
                if tenant_join:
                    tenant = db.session.query(Tenant).filter(Tenant.id == tenant_join.tenant_id).first()
                    if tenant:
                        account.current_tenant = tenant

            db.session.commit()
            logging.info(f"成功创建新用户: {account.name} (ID: {account.id})")
            return account

        except Exception as e:
            logging.error(f"创建用户失败: {str(e)}")
            db.session.rollback()
            raise TokenError(
                f"创建用户失败: {str(e)}",
                "USER_CREATE_ERROR",
                f"无法为用户 {username} 创建账户"
            )

    @staticmethod
    def sync_user_info(account: Account, user_info: Dict[str, Any]) -> None:
        """同步token中的用户信息到Dify账户"""
        try:
            updated = False

            # 更新姓名
            user_nickname = user_info.get('user_nickname')
            if user_nickname and user_nickname != account.name:
                account.name = user_nickname
                updated = True

            # 更新其他字段（如果token中有的话）
            level = user_info.get('level')
            if level and hasattr(account, 'level'):
                account.level = level
                updated = True

            user_center_id = user_info.get('user_center_id')
            if user_center_id and hasattr(account, 'user_center_id'):
                account.user_center_id = str(user_center_id)
                updated = True

            if updated:
                db.session.commit()
                logging.info(f"已同步用户信息: {account.name}")

        except Exception as e:
            logging.warning(f"同步用户信息失败: {str(e)}")

    @staticmethod
    def generate_random_password() -> str:
        """生成随机密码"""
        return ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(12))


def token_required(fallback_to_session: bool = True):
    """
    Token认证装饰器

    Args:
        fallback_to_session: 是否在token验证失败时降级到传统会话认证

    功能:
        - 验证token并解析用户信息
        - 自动查找或创建Dify用户
        - 将用户信息和账户对象存储到Flask g对象中
        - 支持降级到传统认证

    使用方法:
        @token_required()
        def your_api_method(self):
            user_info = g.token_user_info  # token中的用户信息
            account = g.token_account      # Dify账户对象
            use_token_auth = g.use_token_auth  # 是否使用token认证
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 初始化Flask g对象
            g.token_user_info = None
            g.token_account = None
            g.use_token_auth = False

            try:
                # 尝试获取token
                token = TokenAuthService.get_token_from_request()
                if not token:
                    if fallback_to_session:
                        logging.info("未找到token，降级到传统认证")
                        return f(*args, **kwargs)
                    else:
                        raise Unauthorized("未找到token，请检查Authorization header、cookies或access_token参数")

                # 验证并解析token
                user_info = TokenAuthService.validate_and_parse_token(token)

                # 查找或创建用户
                account = TokenAuthService.find_or_create_account(user_info)

                # 存储到Flask g对象中
                g.token_user_info = user_info
                g.token_account = account
                g.use_token_auth = True

                # 设置current_user以兼容@login_required装饰器
                g._login_user = account

                # 确保account的current_tenant正确设置
                if not account.current_tenant:
                    # 重新查询租户关系
                    tenant_join = db.session.query(TenantAccountJoin).filter(
                        TenantAccountJoin.account_id == account.id
                    ).first()
                    if tenant_join:
                        tenant = db.session.query(Tenant).filter(Tenant.id == tenant_join.tenant_id).first()
                        if tenant:
                            account.current_tenant = tenant
                            db.session.commit()

                logging.info("Token认证成功，使用Token验证方式")
                return f(*args, **kwargs)

            except (Unauthorized, TokenError) as e:
                if fallback_to_session:
                    logging.info(f"Token验证失败，降级到传统认证: {str(e)}")
                    # 检查是否有传统认证
                    from flask_login import current_user
                    if not current_user.is_authenticated:
                        error_msg = str(e)
                        if isinstance(e, TokenError):
                            error_msg = f"{e.error_code}: {e.message}"
                        return {"error": "认证失败", "message": f"需要登录或有效的token。详细错误: {error_msg}"}, 401
                    return f(*args, **kwargs)
                else:
                    # 不允许降级，直接返回错误
                    error_msg = str(e)
                    if isinstance(e, TokenError):
                        error_msg = f"{e.error_code}: {e.message}"
                    return {"error": "认证失败", "message": error_msg}, 401

        return decorated_function
    return decorator


def token_only_required():
    """
    仅Token认证装饰器 - 不允许降级到传统认证
    """
    return token_required(fallback_to_session=False)


def get_current_user_info() -> Dict[str, Any]:
    """
    获取当前用户信息

    Returns:
        包含用户信息的字典，支持token认证和传统认证
    """
    if hasattr(g, 'use_token_auth') and g.use_token_auth:
        # Token认证
        return {
            'user_id': g.token_account.id,
            'tenant_id': g.token_account.current_tenant_id,
            'account': g.token_account,
            'token_user_info': g.token_user_info,
            'auth_type': 'token'
        }
    else:
        # 传统认证
        from flask_login import current_user
        return {
            'user_id': current_user.id,
            'tenant_id': current_user.current_tenant_id,
            'account': current_user,
            'token_user_info': None,
            'auth_type': 'session'
        }


# 向后兼容的别名
token_auth_required = token_required
