const translation = {
  title: '工具',
  createCustomTool: '创建自定义工具',
  customToolTip: '了解更多关于 Dify 自定义工具的信息',
  type: {
    all: '全部',
    builtIn: '工具',
    custom: '自定义',
    workflow: '工作流',
  },
  contribute: {
    line1: '我有兴趣为 ',
    line2: 'Dify 贡献工具。',
    viewGuide: '查看指南',
  },
  author: '作者',
  auth: {
    authorized: '已授权',
    setup: '要使用请先授权',
    setupModalTitle: '设置授权',
    setupModalTitleDescription: '配置凭据后，工作区中的所有成员都可以在编排应用程序时使用此工具。',
  },
  includeToolNum: '包含 {{num}} 个 {{action}}',
  addTool: '添加工具',
  addToolModal: {
    type: '类型',
    category: '类别',
    add: '添加',
    added: '已添加',
    manageInTools: '去工具列表管理',
    custom: {
      title: '没有可用的自定义工具',
      tip: '创建自定义工具',
    },
    workflow: {
      title: '没有可用的工作流工具',
      tip: '在工作室中发布工作流作为工具',
    },
    mcp: {
      title: '没有可用的 MCP 工具',
      tip: '添加 MCP 服务器',
    },
    agent: {
      title: '没有可用的 agent 策略',
    },
  },
  createTool: {
    title: '创建自定义工具',
    editAction: '编辑',
    editTitle: '编辑自定义工具',
    name: '名称',
    toolNamePlaceHolder: '输入工具名称',
    nameForToolCall: '工具调用名称',
    nameForToolCallPlaceHolder: '用于机器识别，如 getCurrentWeather, list_pets',
    nameForToolCallTip: '仅支持数字、字母、下划线。',
    description: '工具描述',
    descriptionPlaceholder: '工具用途的简要描述，例如获取特定位置的温度。',
    schema: 'Schema',
    schemaPlaceHolder: '在此处输入您的 OpenAPI schema',
    viewSchemaSpec: '查看 OpenAPI-Swagger 规范',
    importFromUrl: '从 URL 中导入',
    importFromUrlPlaceHolder: 'https://...',
    urlError: '请输入有效的 URL',
    examples: '例子',
    exampleOptions: {
      json: '天气 (JSON)',
      yaml: '宠物商店 (YAML)',
      blankTemplate: '空白模版',
    },
    availableTools: {
      title: '可用工具',
      name: '名称',
      description: '描述',
      method: '方法',
      path: '路径',
      action: '操作',
      test: '测试',
    },
    authMethod: {
      title: '鉴权方法',
      type: '鉴权类型',
      keyTooltip: 'HTTP 头部名称，如果你不知道是什么，可以将其保留为 Authorization 或设置为自定义值',
      types: {
        none: '无',
        api_key: 'API Key',
        apiKeyPlaceholder: 'HTTP 头部名称，用于传递 API Key',
        apiValuePlaceholder: '输入 API Key',
      },
      key: '键',
      value: '值',
    },
    authHeaderPrefix: {
      title: '鉴权头部前缀',
      types: {
        basic: 'Basic',
        bearer: 'Bearer',
        custom: 'Custom',
      },
    },
    privacyPolicy: '隐私协议',
    privacyPolicyPlaceholder: '请输入隐私协议',
    toolInput: {
      title: '工具入参',
      name: '名称',
      required: '必须',
      method: '方式',
      methodSetting: '用户输入',
      methodSettingTip: '用户在工具配置中填写',
      methodParameter: 'LLM 填入',
      methodParameterTip: 'LLM 在推理过程中填写',
      label: '标签',
      labelPlaceholder: '选择标签 (可选)',
      description: '描述',
      descriptionPlaceholder: '参数意义的描述',
    },
    customDisclaimer: '自定义免责声明',
    customDisclaimerPlaceholder: '请输入自定义免责声明',
    confirmTitle: '确认保存？',
    confirmTip: '发布新的工具版本可能会影响该工具已关联的应用',
    deleteToolConfirmTitle: '删除这个工具？',
    deleteToolConfirmContent: '删除工具是不可逆的。用户将无法再访问您的工具。',
  },
  test: {
    title: '测试',
    parametersValue: '参数和值',
    parameters: '参数',
    value: '值',
    testResult: '测试结果',
    testResultPlaceholder: '测试结果将显示在这里',
  },
  thought: {
    using: '正在使用',
    used: '已使用',
    requestTitle: '请求',
    responseTitle: '响应',
  },
  setBuiltInTools: {
    info: '信息',
    setting: '设置',
    toolDescription: '工具描述',
    parameters: '参数',
    string: '字符串',
    number: '数字',
    file: '文件',
    required: '必填',
    infoAndSetting: '信息和设置',
  },
  noCustomTool: {
    title: '没有自定义工具！',
    content: '在此统一添加和管理你的自定义工具，方便构建应用时使用。',
    createTool: '创建工具',
  },
  noSearchRes: {
    title: '抱歉，没有结果！',
    content: '我们找不到任何与您的搜索相匹配的工具。',
    reset: '重置搜索',
  },
  builtInPromptTitle: '提示词',
  toolRemoved: '工具已被移除',
  notAuthorized: '工具未授权',
  howToGet: '如何获取',
  openInStudio: '在工作室中打开',
  toolNameUsageTip: '工具调用名称，用于 Agent 推理和提示词',
  copyToolName: '复制名称',
  noTools: '没有工具',
  mcp: {
    create: {
      cardTitle: '添加 MCP 服务 (HTTP)',
      cardLink: '了解更多关于 MCP 服务集成的信息',
    },
    noConfigured: '未配置',
    updateTime: '更新于',
    toolsCount: '{{count}} 个工具',
    noTools: '没有可用的工具',
    modal: {
      title: '添加 MCP 服务 (HTTP)',
      editTitle: '修改 MCP 服务 (HTTP)',
      name: '名称和图标',
      namePlaceholder: '命名你的 MCP 服务',
      serverUrl: '服务端点 URL',
      serverUrlPlaceholder: '服务端点的 URL',
      serverUrlWarning: '修改服务端点 URL 可能会影响使用当前 MCP 的应用。',
      serverIdentifier: '服务器标识符',
      serverIdentifierTip: '工作空间内服务器的唯一标识。支持小写字母、数字、下划线和连字符，最多 24 个字符。',
      serverIdentifierPlaceholder: '服务器唯一标识，例如 my-mcp-server',
      serverIdentifierWarning: '更改服务器标识符后，现有应用将无法识别此服务器',
      cancel: '取消',
      save: '保存',
      confirm: '添加并授权',
    },
    delete: '删除 MCP 服务',
    deleteConfirmTitle: '你想要删除 {{mcp}} 吗？',
    operation: {
      edit: '修改',
      remove: '删除',
    },
    authorize: '授权',
    authorizing: '授权中...',
    authorizingRequired: '需要授权',
    authorizeTip: '授权后，工具将显示在这里。',
    update: '更新',
    updating: '更新中',
    gettingTools: '获取工具中...',
    updateTools: '更新工具中...',
    toolsEmpty: '工具未加载',
    getTools: '获取工具',
    toolUpdateConfirmTitle: '更新工具列表',
    toolUpdateConfirmContent: '更新工具列表可能影响现有应用。您想继续吗？',
    toolsNum: '包含 {{count}} 个工具',
    onlyTool: '包含 1 个工具',
    identifier: '服务器标识符 (点击复制)',
    server: {
      title: 'MCP 服务',
      url: '服务端点 URL',
      reGen: '你想要重新生成服务端点 URL 吗？',
      addDescription: '添加描述',
      edit: '编辑描述',
      modal: {
        addTitle: '添加描述以启用 MCP 服务',
        editTitle: '编辑 MCP 服务描述',
        description: '描述',
        descriptionPlaceholder: '解释此工具的功能以及 LLM 应如何使用它',
        parameters: '参数',
        parametersTip: '为每个参数添加描述，以帮助 LLM 理解其目的和约束条件。',
        parametersPlaceholder: '参数的用途和约束条件',
        confirm: '启用 MCP 服务',
      },
      publishTip: '应用未发布。请先发布应用。',
    },
  },
}

export default translation
