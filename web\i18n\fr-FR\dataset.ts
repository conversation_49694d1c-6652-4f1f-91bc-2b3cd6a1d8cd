const translation = {
  knowledge: 'Connaissance',
  documentCount: ' documents',
  wordCount: ' k mots',
  appCount: ' applications liées',
  createDataset: 'Créer des Connaissances',
  createDatasetIntro: 'Importez vos propres données textuelles ou écrivez des données en temps réel via Webhook pour l\'amélioration du contexte LLM.',
  deleteDatasetConfirmTitle: 'Supprimer cette Connaissance ?',
  deleteDatasetConfirmContent:
    'La suppression de la Connaissance est irréversible. Les utilisateurs ne pourront plus accéder à votre Savoir, et toutes les configurations de prompt et les journaux seront supprimés de façon permanente.',
  datasetUsedByApp: 'La connaissance est utilisée par certaines applications. Les applications ne pourront plus utiliser cette Connaissance, et toutes les configurations de prompts et les journaux seront définitivement supprimés.',
  datasetDeleted: 'Connaissance supprimée',
  datasetDeleteFailed: 'Échec de la suppression de la Connaissance',
  didYouKnow: 'Saviez-vous ?',
  intro1: 'La Connaissance peut être intégrée dans l\'application Dify',
  intro2: 'comme un contexte',
  intro3: ',',
  intro4: 'ou ça ',
  intro5: 'peut être créé',
  intro6: 'comme un plug-in d\'index ChatGPT autonome à publier',
  unavailable: 'Indisponible',
  unavailableTip: 'Le modèle d\'embedding n\'est pas disponible, le modèle d\'embedding par défaut doit être configuré',
  datasets: 'CONNAISSANCE',
  datasetsApi: 'API',
  retrieval: {
    semantic_search: {
      title: 'Recherche Vectorielle',
      description: 'Générez des embeddings de requête et recherchez le morceau de texte le plus similaire à sa représentation vectorielle.',
    },
    full_text_search: {
      title: 'Recherche en Texte Intégral',
      description: 'Indexez tous les termes dans le document, permettant aux utilisateurs de rechercher n\'importe quel terme et de récupérer le fragment de texte pertinent contenant ces termes.',
    },
    hybrid_search: {
      title: 'Recherche Hybride',
      description: 'Exécutez une recherche en texte intégral et des recherches vectorielles en même temps, réorganisez pour sélectionner la meilleure correspondance pour la requête de l\'utilisateur. La configuration de l\'API du modèle de réorganisation est nécessaire.',
      recommend: 'Recommander',
    },
    invertedIndex: {
      title: 'Index inversé',
      description: 'L\'Index inversé est une structure utilisée pour une récupération efficace. Organisé par termes, chaque terme pointe vers des documents ou des pages web le contenant.',
    },
    change: 'Changer',
    changeRetrievalMethod: 'Changer la méthode de récupération',
  },
  docsFailedNotice: 'Les documents n\'ont pas pu être indexés',
  retry: 'Réessayer',
  indexingTechnique: {
    high_quality: 'HQ',
    economy: 'ÉCO',
  },
  indexingMethod: {
    semantic_search: 'VECTEUR',
    full_text_search: 'TEXTE INTÉGRAL',
    hybrid_search: 'HYBRIDE',
    invertedIndex: 'INVERSÉ',
  },
  mixtureHighQualityAndEconomicTip: 'Le modèle de reclassement est nécessaire pour le mélange de bases de connaissances de haute qualité et économiques.',
  inconsistentEmbeddingModelTip: 'Le modèle de reclassement est nécessaire si les modèles d\'incorporation des bases de connaissances sélectionnées sont incohérents.',
  retrievalSettings: 'Paramètres de récupération',
  rerankSettings: 'Paramètres de reclassement',
  weightedScore: {
    title: 'Score pondéré',
    description: 'En ajustant les poids attribués, cette stratégie de reclassement détermine s\'il faut prioriser la correspondance sémantique ou par mots-clés.',
    semanticFirst: 'Sémantique d\'abord',
    keywordFirst: 'Mot-clé d\'abord',
    customized: 'Personnalisé',
    semantic: 'Sémantique',
    keyword: 'Mot-clé',
  },
  nTo1RetrievalLegacy: 'La récupération N-à-1 sera officiellement obsolète à partir de septembre. Il est recommandé d\'utiliser la dernière récupération multi-chemins pour obtenir de meilleurs résultats.',
  nTo1RetrievalLegacyLink: 'En savoir plus',
  nTo1RetrievalLegacyLinkText: 'La récupération N-à-1 sera officiellement obsolète en septembre.',
  defaultRetrievalTip: 'La récupération à chemins multiples est utilisée par défaut. Les connaissances sont extraites de plusieurs bases de connaissances, puis reclassées.',
  editExternalAPIConfirmWarningContent: {
    front: 'Cette API de connaissances externes est liée à',
    end: 'connaissances externes, et cette modification sera appliquée à tous. Êtes-vous sûr de vouloir enregistrer cette modification ?',
  },
  editExternalAPIFormWarning: {
    end: 'Connaissances externes',
    front: 'Cette API externe est liée à',
  },
  deleteExternalAPIConfirmWarningContent: {
    title: {
      end: '?',
      front: 'Supprimer',
    },
    content: {
      front: 'Cette API de connaissances externes est liée à',
      end: 'connaissances externes. La suppression de cette API les invalidera toutes. Êtes-vous sûr de vouloir supprimer cette API ?',
    },
    noConnectionContent: 'Êtes-vous sûr de supprimer cette API ?',
  },
  selectExternalKnowledgeAPI: {
    placeholder: 'Choisir une API de connaissances externe',
  },
  connectDatasetIntro: {
    content: {
      link: 'Découvrez comment créer une API externe',
      end: '. Trouvez ensuite l’ID de connaissances correspondant et remplissez-le dans le formulaire sur la gauche. Si toutes les informations sont correctes, il passera automatiquement au test de récupération dans la base de connaissances après avoir cliqué sur le bouton de connexion.',
      front: 'Pour vous connecter à une base de connaissances externe, vous devez d’abord créer une API externe. Veuillez lire attentivement et vous référer à',
    },
    learnMore: 'Pour en savoir plus',
    title: 'Comment se connecter à une base de connaissances externe',
  },
  connectHelper: {
    helper2: 'Seule la fonctionnalité de récupération est prise en charge',
    helper3: '. Nous vous recommandons vivement de',
    helper4: 'Lire la documentation d’aide',
    helper5: 'soigneusement avant d’utiliser cette fonctionnalité.',
    helper1: 'Connectez-vous à des bases de connaissances externes via l’API et l’ID de base de connaissances.',
  },
  externalKnowledgeForm: {
    cancel: 'Annuler',
    connect: 'Relier',
  },
  externalAPIForm: {
    encrypted: {
      end: 'Technologie.',
      front: 'Votre jeton API sera chiffré et stocké à l’aide de',
    },
    name: 'Nom',
    apiKey: 'Clé API',
    save: 'Sauvegarder',
    cancel: 'Annuler',
    edit: 'Éditer',
    endpoint: 'Point de terminaison de l’API',
  },
  externalKnowledgeName: 'Nom de la connaissance externe',
  mixtureInternalAndExternalTip: 'Le modèle Rerank est nécessaire pour mélanger les connaissances internes et externes.',
  externalKnowledgeIdPlaceholder: 'Entrez l’ID de connaissances',
  createExternalAPI: 'Ajouter une API de connaissances externe',
  externalKnowledgeNamePlaceholder: 'Entrez le nom de la base de connaissances',
  allExternalTip: 'Lorsqu’il utilise uniquement des connaissances externes, l’utilisateur peut choisir d’activer ou non le modèle Rerank. S’il n’est pas activé, les morceaux récupérés seront triés en fonction des scores. Lorsque les stratégies de récupération des différentes bases de connaissances sont incohérentes, elles seront inexactes.',
  externalAPI: 'API externe',
  editExternalAPIFormTitle: 'Modifier l’API de connaissances externes',
  externalTag: 'Externe',
  editExternalAPITooltipTitle: 'CONNAISSANCES ASSOCIÉES',
  connectDataset: 'Se connecter à une base de connaissances externe',
  externalKnowledgeDescription: 'Description des connaissances',
  externalAPIPanelDocumentation: 'Découvrez comment créer une API de connaissances externe',
  createNewExternalAPI: 'Créer une API de connaissances externe',
  externalKnowledgeDescriptionPlaceholder: 'Décrivez le contenu de cette base de connaissances (facultatif)',
  externalAPIPanelDescription: 'L’API de connaissances externe est utilisée pour se connecter à une base de connaissances en dehors de Dify et récupérer des connaissances de cette base de connaissances.',
  externalKnowledgeId: 'Identification des connaissances externes',
  externalAPIPanelTitle: 'API de connaissances externes',
  noExternalKnowledge: 'Il n’y a pas encore d’API de connaissances externes, cliquez ici pour créer',
  learnHowToWriteGoodKnowledgeDescription: 'Apprenez à rédiger une bonne description des connaissances',
  chunkingMode: {
    general: 'Généralités',
    parentChild: 'Parent-enfant',
  },
  parentMode: {
    paragraph: 'Paragraphe',
    fullDoc: 'Doc complet',
  },
  batchAction: {
    archive: 'Archiver',
    disable: 'Désactiver',
    delete: 'Supprimer',
    cancel: 'Annuler',
    enable: 'Activer',
    selected: 'Sélectionné',
  },
  preprocessDocument: '{{num}} Prétraiter les documents',
  documentsDisabled: '{{num}} documents désactivés - inactifs depuis plus de 30 jours',
  localDocs: 'Docs locaux',
  enable: 'Activer',
  allKnowledge: 'Toutes les connaissances',
  allKnowledgeDescription: 'Sélectionnez cette option pour afficher toutes les connaissances dans cet espace de travail. Seul le propriétaire de l’espace de travail peut gérer toutes les connaissances.',
  metadata: {
    createMetadata: {
      name: 'Nom',
      title: 'Nouveaux Métadonnées',
      namePlaceholder: 'Ajouter le nom des métadonnées',
      type: 'Type',
      back: 'Retour',
    },
    checkName: {
      empty: 'Le nom des métadonnées ne peut pas être vide',
      invalid: 'Le nom des métadonnées ne peut contenir que des lettres minuscules, des chiffres et des tirets bas et doit commencer par une lettre minuscule.',
      tooLong: 'Le nom des métadonnées ne peut pas dépasser {{max}} caractères',
    },
    batchEditMetadata: {
      editMetadata: 'Modifier les métadonnées',
      applyToAllSelectDocumentTip: 'Créez automatiquement toutes les métadonnées modifiées et nouvelles pour tous les documents sélectionnés, sinon l\'édition des métadonnées ne s\'appliquera qu\'aux documents qui en ont.',
      applyToAllSelectDocument: 'Appliquer à tous les documents sélectionnés',
      multipleValue: 'Valeur multiple',
      editDocumentsNum: 'Édition de {{num}} documents',
    },
    selectMetadata: {
      search: 'Rechercher des métadonnées',
      newAction: 'Nouveaux métadonnées',
      manageAction: 'Gérer',
    },
    datasetMetadata: {
      description: 'Vous pouvez gérer toutes les métadonnées dans cette connaissance ici. Les modifications seront synchronisées avec chaque document.',
      rename: 'Renommer',
      builtIn: 'Intégré',
      addMetaData: 'Ajouter des métadonnées',
      namePlaceholder: 'Nom de métadonnées',
      builtInDescription: 'Les métadonnées intégrées sont automatiquement extraites et générées. Elles doivent être activées avant utilisation et ne peuvent pas être modifiées.',
      deleteTitle: 'Confirmer la suppression',
      values: '{{num}} Valeurs',
      deleteContent: 'Êtes-vous sûr de vouloir supprimer les métadonnées "{{name}}" ?',
      name: 'Nom',
      disabled: 'handicapés',
    },
    documentMetadata: {
      technicalParameters: 'Paramètres techniques',
      metadataToolTip: 'Les métadonnées servent de filtre essentiel qui améliore l\'exactitude et la pertinence de la recherche d\'informations. Vous pouvez modifier et ajouter des métadonnées pour ce document ici.',
      documentInformation: 'Informations du document',
      startLabeling: 'Commencer l\'étiquetage',
    },
    addMetadata: 'Ajouter des métadonnées',
    metadata: 'Métadonnées',
    chooseTime: 'Choisissez un moment...',
  },
  embeddingModelNotAvailable: 'Le modèle d\'embedding n\'est pas disponible.',
}

export default translation
