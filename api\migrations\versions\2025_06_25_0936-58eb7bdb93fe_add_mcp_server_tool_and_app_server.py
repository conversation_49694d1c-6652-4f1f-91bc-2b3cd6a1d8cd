"""add mcp server tool and app server

Revision ID: 58eb7bdb93fe
Revises: 0ab65e1cc7fa
Create Date: 2025-06-25 09:36:07.510570

"""
from alembic import op
import models as models
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '58eb7bdb93fe'
down_revision = '0ab65e1cc7fa'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('app_mcp_servers',
    sa.Column('id', models.types.StringUUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', models.types.StringUUID(), nullable=False),
    sa.Column('app_id', models.types.StringUUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=False),
    sa.Column('server_code', sa.String(length=255), nullable=False),
    sa.Column('status', sa.String(length=255), server_default=sa.text("'normal'::character varying"), nullable=False),
    sa.Column('parameters', sa.Text(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='app_mcp_server_pkey'),
    sa.UniqueConstraint('tenant_id', 'app_id', name='unique_app_mcp_server_tenant_app_id'),
    sa.UniqueConstraint('server_code', name='unique_app_mcp_server_server_code')
    )
    op.create_table('tool_mcp_providers',
    sa.Column('id', models.types.StringUUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('name', sa.String(length=40), nullable=False),
    sa.Column('server_identifier', sa.String(length=24), nullable=False),
    sa.Column('server_url', sa.Text(), nullable=False),
    sa.Column('server_url_hash', sa.String(length=64), nullable=False),
    sa.Column('icon', sa.String(length=255), nullable=True),
    sa.Column('tenant_id', models.types.StringUUID(), nullable=False),
    sa.Column('user_id', models.types.StringUUID(), nullable=False),
    sa.Column('encrypted_credentials', sa.Text(), nullable=True),
    sa.Column('authed', sa.Boolean(), nullable=False),
    sa.Column('tools', sa.Text(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='tool_mcp_provider_pkey'),
    sa.UniqueConstraint('tenant_id', 'name', name='unique_mcp_provider_name'),
    sa.UniqueConstraint('tenant_id', 'server_identifier', name='unique_mcp_provider_server_identifier'),
    sa.UniqueConstraint('tenant_id', 'server_url_hash', name='unique_mcp_provider_server_url')
    )

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('tool_mcp_providers')
    op.drop_table('app_mcp_servers')
    # ### end Alembic commands ###
