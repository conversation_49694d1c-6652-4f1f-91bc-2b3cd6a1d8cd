"""add tool labels to agent thought

Revision ID: 380c6aa5a70d
Revises: dfb3b7f477da
Create Date: 2024-01-24 10:58:15.644445

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '380c6aa5a70d'
down_revision = 'dfb3b7f477da'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('message_agent_thoughts', schema=None) as batch_op:
        batch_op.add_column(sa.Column('tool_labels_str', sa.Text(), server_default=sa.text("'{}'::text"), nullable=False))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('message_agent_thoughts', schema=None) as batch_op:
        batch_op.drop_column('tool_labels_str')

    # ### end Alembic commands ###
