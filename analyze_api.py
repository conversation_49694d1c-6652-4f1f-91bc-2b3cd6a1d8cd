import os
import json
import re

from openai import OpenAI
import pandas as pd
from tqdm import tqdm
import tiktoken
from multiprocessing import Pool, Manager, cpu_count

# Set your OpenAI API key
API_KEY = 'sk-c7a9cb40668d41a2aa4c67abec033641'
BASE_URL = 'https://api.deepseek.com/v1'

def load_csv(csv_path, limit=40):
    df = pd.read_csv(csv_path, encoding='utf-8')
    return df.head(limit).to_dict(orient='records')

def load_python_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

def construct_prompt(api_call, file_content):
    prompt = f"""
    你是一个经验丰富的Python开发者。根据以下开源框架dify的代码片段和API调用，分析该接口的作用。
    备注：
        Dify是一个开源的AI应用开发平台,可以帮助开发者快速搭建基于大语言模型的应用。它的主要特点包括:     
        提供可视化的界面,让开发者能够通过拖拽方式构建AI应用
        支持多种大语言模型(如ChatGPT, Claude等)的接入
        支持RAG
        内置多种应用模板,如智能客服、知识库问答等
        支持多语言,便于国际化使用
        提供API接口,方便与其他系统集成

    代码文件内容：
    ```python
    {file_content}
    ```

    API调用：
    ```python
    {api_call}
    ```
    请根据api调用对应的python的方法，提供该接口的定义、输入参数、返回参数，并以JSON格式输出。
    注意同一个api接口可能会有不同的变体（例如同一个接口可能会有GET\POST\DELETE\PUSH\PATCH等等不同的请求方法），分析时务必不能遗漏，确保该接口用到的所有方法都被包含在内。
    JSON结构应包含以下字段：
    - "接口定义": 接口的功能描述
    - "请求方式": 请求的HTTP方法
    - "入参": 输入参数的详细信息，入参的字段必须严格和代码中的一样，不要自行修改
    - "返回参数": 返回参数的详细信息，返回参数的字段必须严格和代码中的一样，不要自行修改
    - 只输出一个完整正确的JSON对象，不要生成错误的多个json对象
    示例输出：
    ```json
    {{
        "接口URL": "/api/example",
        "接口描述": "这个接口的总体功能描述",
        "接口变体": [
            {{
                "请求方式": "GET",
                "功能描述": "GET方法的具体功能描述",
                "入参": {{
                    "参数1": {{
                        "类型": "string",
                        "描述": "参数1的说明",
                        "是否必填": true
                    }},
                    "参数2": {{
                        "类型": "integer",
                        "描述": "参数2的说明",
                        "是否必填": false
                    }}
                }},
                "返回值": {{
                    "字段1": {{
                        "类型": "string",
                        "描述": "返回字段1的说明"
                    }},
                    "字段2": {{
                        "类型": "object",
                        "描述": "返回字段2的说明"
                    }}
                }}
            }},
            {{
                "请求方式": "POST",
                "功能描述": "POST方法的具体功能描述",
                "入参": {{
                    "参数3": {{
                        "类型": "object",
                        "描述": "参数3的说明",
                        "是否必填": true
                    }}
                }},
                "返回值": {{
                    "字段3": {{
                        "类型": "boolean",
                        "描述": "返回字段3的说明"
                    }}
                }}
            }}
        ]
    }}
    ```
    """
    return prompt

def num_tokens_from_messages(messages, model="deepseek-chat"):
    """计算消息中的token数量"""
    try:
        encoding = tiktoken.encoding_for_model(model)
    except KeyError:
        encoding = tiktoken.get_encoding("cl100k_base")

    num_tokens = 0
    for message in messages:
        num_tokens += 4  # 每条消息的基础token
        for key, value in message.items():
            num_tokens += len(encoding.encode(value))
            if key == "name":
                num_tokens += -1
    num_tokens += 2
    return num_tokens

def call_openai(prompt):
    client = OpenAI(
        api_key=API_KEY,
        base_url=BASE_URL
    )
    messages = [
        {"role": "system", "content": "你是一个帮助分析Python API调用的助手。"},
        {"role": "user", "content": prompt}
    ]
    input_tokens = num_tokens_from_messages(messages)
    print(f"输入token数量: {input_tokens}")

    response = client.chat.completions.create(
        model="deepseek-chat",
        messages=messages,
        max_tokens=2048,
        temperature=0.0,
    )
    # print("OpenAI API Response:", response.choices[0].message.content)
    return response.choices[0].message.content.strip(), input_tokens

def parse_json_response(response_text):
    try:
        # 打印原始响应，用于调试
        print("原始响应文本:")
        print(response_text)
        print("-" * 50)

        # 1. 首先尝试在 Markdown 代码块中查找 JSON
        json_start = response_text.find('```json')
        json_end = response_text.rfind('```')

        if json_start != -1 and json_end != -1:
            json_str = response_text[json_start + 7:json_end].strip()
            # print("从代码块提取的 JSON:")
            # print(json_str)
        else:
            # 2. 如果没有找到代码块，尝试直接查找 JSON 对象
            json_start = response_text.find('{')
            json_end = response_text.rfind('}')

            if json_start == -1 or json_end == -1:
                print("未找到 JSON 内容")
                return {
                    "接口URL": "",
                    "接口描述": "解析失败",
                    "接口变体": []
                }

            json_str = response_text[json_start:json_end + 1]
            print("直接提取的 JSON:")
            print(json_str)

        # 3. 清理和格式化 JSON 字符串
        json_str = json_str.strip()
        if not json_str:
            print("提取的 JSON 字符串为空")
            return None

        # 4. 尝试解析 JSON
        try:
            parsed_json = json.loads(json_str)
            return parsed_json
        except json.JSONDecodeError as je:
            print(f"JSON 解析失败: {str(je)}")
            # 尝试修复常见的 JSON 格式问题
            json_str = json_str.replace("'", '"')  # 替换单引号为双引号
            json_str = re.sub(r',\s*}', '}', json_str)  # 移除对象末尾多余的逗号
            print("尝试修复后的 JSON:")
            print(json_str)
            return json.loads(json_str)

    except Exception as e:
        print(f"发生错误: {str(e)}")
        # 返回默认值而不是 None
        return {
            "接口URL": "",
            "接口描述": "解析失败",
            "接口变体": []
        }

def process_row(row):
    repo_path = os.path.abspath('.')
    file_path = os.path.join(repo_path, row['file_path'])
    api_call = row['api_add_resource_call']

    if not os.path.isfile(file_path):
        print(f"文件不存在: {file_path}")
        return None, 0

    file_content = load_python_file(file_path)
    prompt = construct_prompt(api_call, file_content)

    response_text, input_token = call_openai(prompt)
    parsed_response = parse_json_response(response_text)

    if parsed_response:
        # 创建一个列表来存储所有变体的信息
        variants_info = []
        for variant in parsed_response.get('接口变体', []):
            variant_info = {
                '请求方式': variant.get('请求方式'),
                '功能描述': variant.get('功能描述'),
                '入参': json.dumps(variant.get('入参', {}), ensure_ascii=False),
                '返回值': json.dumps(variant.get('返回值', {}), ensure_ascii=False)
            }
            variants_info.append(variant_info)

        result = {
            'repository_path': row.get('repository_path', ''),
            'file_path': row.get('file_path', ''),
            'api_add_resource_call': api_call,
            '接口URL': parsed_response.get('接口URL', ''),
            '接口描述': parsed_response.get('接口描述', ''),
            '接口变体': variants_info,
            '解析错误': None
        }
    else:
        print("解析失败")
        result = {
            'repository_path': row.get('repository_path', ''),
            'file_path': row.get('file_path', ''),
            'api_add_resource_call': api_call,
            '接口URL': '',
            '接口描述': '',
            '接口变体': [],
            '解析错误': response_text  # 或其他错误信息
        }
    return result, input_token

def main(limit,csv_path = 'api_add_resource_calls.csv',output_csv = 'api_add_resource_analysis.csv'):
    # csv_path = 'api_add_resource_calls.csv'
    # csv_path = 'api_add_resource_error.csv'
    csv_rows = load_csv(csv_path, limit=limit)

    manager = Manager()
    results = manager.list()
    total_tokens = manager.Value('i', 0)

    with Pool(processes=8) as pool:
        with tqdm(total=len(csv_rows)) as pbar:
            for result, tokens in pool.imap(process_row, csv_rows):
                if result:
                    results.append(result)
                total_tokens.value += tokens
                pbar.update(1)

    print("总耗费的token:", total_tokens.value)

    # 将结果写入新的JSON文件
    # output_json = 'api_add_resource_analysis.json'
    # with open(output_json, 'w', encoding='utf-8-sig') as f:
    #     json.dump(list(results), f, ensure_ascii=False, indent=4)

    # 将结果写入CSV文件
    # output_csv = 'api_add_resource_analysis.csv'
    # output_csv = 'api_add_resource_analysis_error.csv'
    df = pd.DataFrame(list(results))
    df.to_csv(output_csv, index=False, encoding='utf-8-sig')

def expand_api_variants(input_file, output_file):
    """
    读取原始API分析结果，展开接口变体数据并保存到新的CSV文件

    Args:
        input_file (str): 输入文件路径 (api_add_resource_analysis.csv)
        output_file (str): 输出文件路径 (api_variants_analysis.csv)
    """
    try:
        # 读取原始CSV文件
        df = pd.read_csv(input_file)

        # 创建一个存储展开后数据的列表
        expanded_rows = []

        # 遍历每一行
        for index, row in df.iterrows():
            # 获取基础信息
            base_info = {
                'repository_path': row['repository_path'],
                'file_path': row['file_path'],
                'api_add_resource_call': row['api_add_resource_call'],
                '接口URL': row['接口URL'],
                '接口描述': row['接口描述']
            }

            try:
                # 解析接口变体字符串为Python对象
                variants = eval(row['接口变体']) if isinstance(row['接口变体'], str) else row['接口变体']

                # 如果没有变体或解析失败，创建一个包含基础信息的空行
                if not variants:
                    base_info.update({
                        '请求方式': None,
                        '功能描述': None,
                        '入参': None,
                        '返回值': None,
                        '解析错误': row.get('解析错误', None)
                    })
                    expanded_rows.append(base_info)
                else:
                    # 为每个变体创建一个新行
                    for variant in variants:
                        variant_row = base_info.copy()
                        variant_row.update({
                            '请求方式': variant.get('请求方式'),
                            '功能描述': variant.get('功能描述'),
                            '入参': variant.get('入参'),
                            '返回值': variant.get('返回值'),
                            '解析错误': row.get('解析错误', None)
                        })
                        expanded_rows.append(variant_row)

            except Exception as e:
                # 如果处理变体时出错，创建一个包含错误信息的行
                base_info.update({
                    '请求方式': None,
                    '功能描述': None,
                    '入参': None,
                    '返回值': None,
                    '解析错误': f"变体解析错误: {str(e)}"
                })
                expanded_rows.append(base_info)

        # 创建新的DataFrame
        expanded_df = pd.DataFrame(expanded_rows)

        # 定义列的顺序
        columns_order = [
            'repository_path',
            'file_path',
            'api_add_resource_call',
            '接口URL',
            '接口描述',
            '请求方式',
            '功能描述',
            '入参',
            '返回值',
            '解析错误'
        ]

        # 重排列顺序
        expanded_df = expanded_df[columns_order]

        # 保存到新的CSV文件
        expanded_df.to_csv(output_file, index=False, encoding='utf-8-sig')

        print(f"处理完成！共处理 {len(df)} 个接口，展开为 {len(expanded_df)} 条记录")
        print(f"结果已保存到: {output_file}")

        return expanded_df

    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        return None

def get_error_lines(origin_file, output_file):
    # 读取原始CSV文件
    df = pd.read_csv(origin_file)
    failed_parsing_df = df[df['接口描述'].str.contains('解析失败', na=False)]
    failed_parsing_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"筛选完成！共找到 {len(failed_parsing_df)} 条包含'解析失败'的记录")
    print(f"结果已保存到: {output_file}")

def merge_results(original_file, error_analysis_file, output_file):
    # 读取原始分析结果
    original_df = pd.read_csv(original_file)
    # 读取重新分析的结果
    error_analysis_df = pd.read_csv(error_analysis_file)

    # 合并结果：用重新分析的结果替换原始结果中对应的行
    for index, row in error_analysis_df.iterrows():
        # 找到原始结果中对应的行
        original_index = original_df[(original_df['repository_path'] == row['repository_path']) &
                                     (original_df['file_path'] == row['file_path']) &
                                     (original_df['api_add_resource_call'] == row['api_add_resource_call'])].index
        if not original_index.empty:
            # 替换原始结果中的行
            original_df.loc[original_index[0]] = row

    # 保存合并后的结果
    original_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"合并完成！结果已保存到: {output_file}")

if __name__ == "__main__":
    # main(600)
    # get_error_lines("api_add_resource_analysis.csv",  "api_add_resource_calls_error.csv")
    # main(600, "api_add_resource_calls_error.csv", "api_add_resource_analysis_error.csv")
    merge_results("api_add_resource_analysis.csv", "api_add_resource_analysis_error.csv", "api_add_resource_analysis_correct.csv")
    expand_api_variants("api_add_resource_analysis_correct.csv","api_add_resource_analysis_final.csv")
