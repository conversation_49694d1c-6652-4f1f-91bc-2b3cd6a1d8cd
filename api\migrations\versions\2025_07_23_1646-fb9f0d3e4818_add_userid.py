"""add userid

Revision ID: fb9f0d3e4818
Revises: 726cd67558bb
Create Date: 2025-07-23 16:46:52.878328

"""
from alembic import op
import models as models
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fb9f0d3e4818'
down_revision = '726cd67558bb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('accounts', schema=None) as batch_op:
        batch_op.add_column(sa.Column('user_id', sa.String(length=64), nullable=True, comment='客户端自定义用户ID'))
        batch_op.create_unique_constraint(batch_op.f('accounts_user_id_key'), ['user_id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('accounts', schema=None) as batch_op:
        batch_op.drop_constraint(batch_op.f('accounts_user_id_key'), type_='unique')
        batch_op.drop_column('user_id')

    # ### end Alembic commands ###
