const tools = [
  {
    author: '<PERSON><PERSON>',
    name: 'NOTION_ADD_PAGE_CONTENT',
    label: {
      en_US: 'NOTION_ADD_PAGE_CONTENT',
      zh_<PERSON>: 'NOTION_ADD_PAGE_CONTENT',
      pt_BR: 'NOTION_ADD_PAGE_CONTENT',
      ja_<PERSON>: 'NOTION_ADD_PAGE_CONTENT',
    },
    description: {
      en_US: 'Adds a single content block to a notion page. multiple calls needed for multiple blocks. note: only supports adding to notion pages. blocks that can contain children: - page (any block type) - toggle (any nested content) - to-do (nested to-dos/blocks) - bulleted list (nested lists/blocks) - numbered list (nested lists/blocks) - callout (child blocks) - quote (nested blocks)',
      zh_<PERSON>: 'Adds a single content block to a notion page. multiple calls needed for multiple blocks. note: only supports adding to notion pages. blocks that can contain children: - page (any block type) - toggle (any nested content) - to-do (nested to-dos/blocks) - bulleted list (nested lists/blocks) - numbered list (nested lists/blocks) - callout (child blocks) - quote (nested blocks)',
      pt_BR: 'Adds a single content block to a notion page. multiple calls needed for multiple blocks. note: only supports adding to notion pages. blocks that can contain children: - page (any block type) - toggle (any nested content) - to-do (nested to-dos/blocks) - bulleted list (nested lists/blocks) - numbered list (nested lists/blocks) - callout (child blocks) - quote (nested blocks)',
      ja_JP: 'Adds a single content block to a notion page. multiple calls needed for multiple blocks. note: only supports adding to notion pages. blocks that can contain children: - page (any block type) - toggle (any nested content) - to-do (nested to-dos/blocks) - bulleted list (nested lists/blocks) - numbered list (nested lists/blocks) - callout (child blocks) - quote (nested blocks)',
    },
    parameters: [
      {
        name: 'after',
        label: {
          en_US: 'after',
          zh_Hans: 'after',
          pt_BR: 'after',
          ja_JP: 'after',
        },
        placeholder: null,
        scope: null,
        auto_generate: null,
        template: null,
        required: false,
        default: null,
        min: null,
        max: null,
        precision: null,
        options: [],
        type: 'string',
        human_description: {
          en_US: 'The ID of the existing block that the new block should be appended after. If not provided, content will be appended at the end of the page.',
          zh_Hans: 'The ID of the existing block that the new block should be appended after. If not provided, content will be appended at the end of the page.',
          pt_BR: 'The ID of the existing block that the new block should be appended after. If not provided, content will be appended at the end of the page.',
          ja_JP: 'The ID of the existing block that the new block should be appended after. If not provided, content will be appended at the end of the page.',
        },
        form: 'llm',
        llm_description: 'The ID of the existing block that the new block should be appended after. If not provided, content will be appended at the end of the page.',
      },
      {
        name: 'content_block',
        label: {
          en_US: 'content_block',
          zh_Hans: 'content_block',
          pt_BR: 'content_block',
          ja_JP: 'content_block',
        },
        placeholder: null,
        scope: null,
        auto_generate: null,
        template: null,
        required: false,
        default: null,
        min: null,
        max: null,
        precision: null,
        options: [],
        type: 'string',
        human_description: {
          en_US: 'Child content to append to a page.',
          zh_Hans: 'Child content to append to a page.',
          pt_BR: 'Child content to append to a page.',
          ja_JP: 'Child content to append to a page.',
        },
        form: 'llm',
        llm_description: 'Child content to append to a page.',
      },
      {
        name: 'parent_block_id',
        label: {
          en_US: 'parent_block_id',
          zh_Hans: 'parent_block_id',
          pt_BR: 'parent_block_id',
          ja_JP: 'parent_block_id',
        },
        placeholder: null,
        scope: null,
        auto_generate: null,
        template: null,
        required: false,
        default: null,
        min: null,
        max: null,
        precision: null,
        options: [],
        type: 'string',
        human_description: {
          en_US: 'The ID of the page which the children will be added.',
          zh_Hans: 'The ID of the page which the children will be added.',
          pt_BR: 'The ID of the page which the children will be added.',
          ja_JP: 'The ID of the page which the children will be added.',
        },
        form: 'llm',
        llm_description: 'The ID of the page which the children will be added.',
      },
    ],
    labels: [],
    output_schema: null,
  },
]

export const listData = [
  {
    id: 'fdjklajfkljadslf111',
    author: 'KVOJJJin',
    name: 'GOGOGO',
    icon: 'https://cloud.dify.dev/console/api/workspaces/694cc430-fa36-4458-86a0-4a98c09c4684/model-providers/langgenius/openai/openai/icon_small/en_US',
    server_url: 'https://mcp.composio.dev/notion/****/abc',
    type: 'mcp',
    is_team_authorization: true,
    tools,
    update_elapsed_time: **********,
    label: {
      en_US: 'GOGOGO',
      zh_Hans: 'GOGOGO',
    },
  },
  {
    id: 'fdjklajfkljadslf222',
    author: 'KVOJJJin',
    name: 'GOGOGO2',
    icon: 'https://cloud.dify.dev/console/api/workspaces/694cc430-fa36-4458-86a0-4a98c09c4684/model-providers/langgenius/openai/openai/icon_small/en_US',
    server_url: 'https://mcp.composio.dev/notion/****/abc',
    type: 'mcp',
    is_team_authorization: false,
    tools: [],
    update_elapsed_time: **********,
    label: {
      en_US: 'GOGOGO2',
      zh_Hans: 'GOGOGO2',
    },
  },
  {
    id: 'fdjklajfkljadslf333',
    author: 'KVOJJJin',
    name: 'GOGOGO3',
    icon: 'https://cloud.dify.dev/console/api/workspaces/694cc430-fa36-4458-86a0-4a98c09c4684/model-providers/langgenius/openai/openai/icon_small/en_US',
    server_url: 'https://mcp.composio.dev/notion/****/abc',
    type: 'mcp',
    is_team_authorization: true,
    tools,
    update_elapsed_time: **********,
    label: {
      en_US: 'GOGOGO3',
      zh_Hans: 'GOGOGO3',
    },
  },
]
