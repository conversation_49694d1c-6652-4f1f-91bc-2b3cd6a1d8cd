const translation = {
  pageTitle: 'Hey, let\'s get started!',
  welcome: '👋 Welcome to Dify, please log in to continue.',
  email: 'Email address',
  emailPlaceholder: 'Your email',
  password: 'Password',
  passwordPlaceholder: 'Your password',
  name: '<PERSON><PERSON><PERSON>',
  namePlaceholder: 'Your username',
  forget: 'Forgot your password?',
  signBtn: 'Sign in',
  continueWithCode: 'Continue With Code',
  sendVerificationCode: 'Send Verification Code',
  usePassword: 'Use Password',
  useVerificationCode: 'Use Verification Code',
  or: 'OR',
  installBtn: 'Set up',
  setAdminAccount: 'Setting up an admin account',
  setAdminAccountDesc: 'Maximum privileges for admin account, which can be used to create applications and manage LLM providers, etc.',
  createAndSignIn: 'Create and sign in',
  oneMoreStep: 'One more step',
  createSample: 'Based on this information, we\'ll create sample application for you',
  invitationCode: 'Invitation Code',
  invitationCodePlaceholder: 'Your invitation code',
  interfaceLanguage: 'Interface Language',
  timezone: 'Time zone',
  go: 'Go to Dify',
  sendUsMail: 'Email us your introduction, and we\'ll handle the invitation request.',
  acceptPP: 'I have read and accept the privacy policy',
  reset: 'Please run following command to reset your password',
  withGitHub: 'Continue with GitHub',
  withGoogle: 'Continue with Google',
  withSSO: 'Continue with SSO',
  rightTitle: 'Unlock the full potential of LLM',
  rightDesc: 'Effortlessly build visually captivating, operable, and improvable AI applications.',
  tos: 'Terms of Service',
  pp: 'Privacy Policy',
  tosDesc: 'By signing up, you agree to our',
  goToInit: 'If you have not initialized the account, please go to the initialization page',
  dontHave: 'Don\'t have?',
  invalidInvitationCode: 'Invalid invitation code',
  accountAlreadyInited: 'Account already initialized',
  forgotPassword: 'Forgot your password?',
  resetLinkSent: 'Reset link sent',
  sendResetLink: 'Send reset link',
  backToSignIn: 'Return to sign in',
  forgotPasswordDesc: 'Please enter your email address to reset your password. We will send you an email with instructions on how to reset your password.',
  checkEmailForResetLink: 'Please check your email for a link to reset your password. If it doesn\'t appear within a few minutes, make sure to check your spam folder.',
  passwordChanged: 'Sign in now',
  changePassword: 'Set a password',
  changePasswordTip: 'Please enter a new password for your account',
  changePasswordBtn: 'Set a password',
  invalidToken: 'Invalid or expired token',
  confirmPassword: 'Confirm Password',
  confirmPasswordPlaceholder: 'Confirm your new password',
  passwordChangedTip: 'Your password has been successfully changed',
  error: {
    emailEmpty: 'Email address is required',
    emailInValid: 'Please enter a valid email address',
    nameEmpty: 'Name is required',
    passwordEmpty: 'Password is required',
    passwordLengthInValid: 'Password must be at least 8 characters',
    passwordInvalid: 'Password must contain letters and numbers, and the length must be greater than 8',
    registrationNotAllowed: 'Account not found. Please contact the system admin to register.',
  },
  license: {
    tip: 'Before starting Dify Community Edition, read the GitHub',
    link: 'Open-source License',
  },
  join: 'Join ',
  joinTipStart: 'Invite you join ',
  joinTipEnd: ' team on Dify',
  invalid: 'The link has expired',
  explore: 'Explore Dify',
  activatedTipStart: 'You have joined the',
  activatedTipEnd: 'team',
  activated: 'Sign in now',
  adminInitPassword: 'Admin initialization password',
  validate: 'Validate',
  checkCode: {
    checkYourEmail: 'Check your email',
    tips: 'We send a verification code to <strong>{{email}}</strong>',
    validTime: 'Bear in mind that the code is valid for 5 minutes',
    verificationCode: 'Verification code',
    verificationCodePlaceholder: 'Enter 6-digit code',
    verify: 'Verify',
    didNotReceiveCode: 'Didn\'t receive the code? ',
    resend: 'Resend',
    useAnotherMethod: 'Use another method',
    emptyCode: 'Code is required',
    invalidCode: 'Invalid code',
  },
  resetPassword: 'Reset Password',
  resetPasswordDesc: 'Type the email you used to sign up on Dify and we will send you a password reset email.',
  backToLogin: 'Back to login',
  setYourAccount: 'Set Your Account',
  enterYourName: 'Please enter your username',
  back: 'Back',
  noLoginMethod: 'Authentication method not configured',
  noLoginMethodTip: 'Please contact the system admin to add an authentication method.',
  licenseExpired: 'License Expired',
  licenseExpiredTip: 'The Dify Enterprise license for your workspace has expired. Please contact your administrator to continue using Dify.',
  licenseLost: 'License Lost',
  licenseLostTip: 'Failed to connect Dify license server. Please contact your administrator to continue using Dify.',
  licenseInactive: 'License Inactive',
  licenseInactiveTip: 'The Dify Enterprise license for your workspace is inactive. Please contact your administrator to continue using Dify.',
  webapp: {
    noLoginMethod: 'Authentication method not configured for web app',
    noLoginMethodTip: 'Please contact the system admin to add an authentication method.',
    disabled: 'Webapp authentication is disabled. Please contact the system admin to enable it. You can try to use the app directly.',
  },
}

export default translation
