import { TracingProvider } from './type'

export const docURL = {
  [TracingProvider.arize]: 'https://docs.arize.com/arize',
  [TracingProvider.phoenix]: 'https://docs.arize.com/phoenix',
  [TracingProvider.langSmith]: 'https://docs.smith.langchain.com/',
  [TracingProvider.langfuse]: 'https://docs.langfuse.com',
  [TracingProvider.opik]: 'https://www.comet.com/docs/opik/tracing/integrations/dify#setup-instructions',
  [TracingProvider.weave]: 'https://weave-docs.wandb.ai/',
  [TracingProvider.aliyun]: 'https://help.aliyun.com/zh/arms/tracing-analysis/untitled-document-1750672984680',
}
