"""add_tenant_id_db_index

Revision ID: a8f9b3c45e4a
Revises: 16830a790f0f
Create Date: 2024-03-18 05:07:35.588473

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = 'a8f9b3c45e4a'
down_revision = '16830a790f0f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('document_segments', schema=None) as batch_op:
        batch_op.create_index('document_segment_tenant_idx', ['tenant_id'], unique=False)

    with op.batch_alter_table('documents', schema=None) as batch_op:
        batch_op.create_index('document_tenant_idx', ['tenant_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('documents', schema=None) as batch_op:
        batch_op.drop_index('document_tenant_idx')

    with op.batch_alter_table('document_segments', schema=None) as batch_op:
        batch_op.drop_index('document_segment_tenant_idx')

    # ### end Alembic commands ###
