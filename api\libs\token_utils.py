import base64
import json
import logging
from typing import Optional, Dict, Any, Tuple
from datetime import datetime


class TokenError(Exception):
    """Token解析错误基类"""
    def __init__(self, message: str, error_code: str, details: str = None):
        self.message = message
        self.error_code = error_code
        self.details = details
        super().__init__(self.message)


class TokenExpiredError(TokenError):
    """Token过期错误"""
    pass


class TokenInvalidFormatError(TokenError):
    """Token格式无效错误"""
    pass


class TokenMissingFieldsError(TokenError):
    """Token缺少必要字段错误"""
    pass


class TokenUtils:
    """Token处理工具类，适配新的token格式"""

    # 错误码定义
    ERROR_CODES = {
        'TOKEN_EMPTY': 'TOKEN_001',
        'TOKEN_EXPIRED': 'TOKEN_002',
        'TOKEN_INVALID_FORMAT': 'TOKEN_003',
        'TOKEN_MISSING_FIELDS': 'TOKEN_004',
        'TOKEN_PARSE_ERROR': 'TOKEN_005',
        'TOKEN_BASE64_ERROR': 'TOKEN_006',
        'TOKEN_JSON_ERROR': 'TOKEN_007'
    }

    @staticmethod
    def get_token(request) -> Optional[str]:
        """
        从请求中获取access-token
        优先从header获取，然后从cookies，最后从参数获取
        """
        # 从header获取
        token = request.headers.get('access-token')

        if token:
            return token

        # 从cookies获取
        token = request.cookies.get('access-token')
        if token:
            return token

        # 从参数获取
        token = request.args.get('access_token')
        if token:
            return token

        return None

    @staticmethod
    def parse_token(token: str) -> Optional[Dict[str, Any]]:
        """
        解析token并返回用户信息
        适配新的token格式：包含user_claims字段
        """
        if not token or token == "null":
            raise TokenError(
                "Token为空或null",
                TokenUtils.ERROR_CODES['TOKEN_EMPTY'],
                "提供的token为空或null值"
            )

        try:
            claims = TokenUtils._parse_token_without_key(token)
            if not claims:
                raise TokenInvalidFormatError(
                    "Token格式无效",
                    TokenUtils.ERROR_CODES['TOKEN_INVALID_FORMAT'],
                    "无法解析token的payload部分"
                )

            # 检查token是否过期
            # TokenUtils._check_token_expiration(claims)

            # 检查必要字段
            TokenUtils._validate_required_fields(claims)

            # 构建用户信息对象 - 适配新的token格式
            user_info = TokenUtils._build_user_info(claims, token)

            return user_info

        except TokenError:
            # 重新抛出TokenError
            raise
        except Exception as e:
            logging.error(f"解析token发生未知错误: {token}, 错误: {str(e)}")
            raise TokenError(
                f"Token解析失败: {str(e)}",
                TokenUtils.ERROR_CODES['TOKEN_PARSE_ERROR'],
                f"解析过程中发生异常: {str(e)}"
            )

    @staticmethod
    def _check_token_expiration(claims: Dict[str, Any]) -> None:
        """检查token是否过期"""
        exp = claims.get('exp')
        if exp:
            current_timestamp = datetime.now().timestamp()
            if current_timestamp > exp:
                raise TokenExpiredError(
                    "Token已过期",
                    TokenUtils.ERROR_CODES['TOKEN_EXPIRED'],
                    f"Token过期时间: {datetime.fromtimestamp(exp)}, 当前时间: {datetime.fromtimestamp(current_timestamp)}"
                )

    @staticmethod
    def _validate_required_fields(claims: Dict[str, Any]) -> None:
        """验证token中的必要字段"""
        # 检查是否有user_claims字段
        user_claims = claims.get('user_claims')
        if not user_claims:
            raise TokenMissingFieldsError(
                "Token缺少user_claims字段",
                TokenUtils.ERROR_CODES['TOKEN_MISSING_FIELDS'],
                "token中必须包含user_claims字段"
            )

        # 检查user_claims中的必要字段
        required_fields = ['userid', 'username']
        missing_fields = []

        for field in required_fields:
            value = user_claims.get(field)
            if value is None or value == "":
                missing_fields.append(field)

        if missing_fields:
            raise TokenMissingFieldsError(
                f"Token缺少必要字段: {', '.join(missing_fields)}",
                TokenUtils.ERROR_CODES['TOKEN_MISSING_FIELDS'],
                f"user_claims中缺少字段: {missing_fields}"
            )

    @staticmethod
    def _build_user_info(claims: Dict[str, Any], token: str) -> Dict[str, Any]:
        """构建用户信息对象 - 适配新的token格式"""
        user_claims = claims.get('user_claims', {})

        # 确保userid是字符串类型
        userid = str(user_claims.get('userid', '')) if user_claims.get('userid') is not None else ''

        # 构建用户信息对象，兼容原有字段和新字段
        user_info = {
            # 原有字段（兼容性）
            'employeeId': userid,  # 使用userid作为employeeId
            'employeeName': user_claims.get('username'),  # 使用username作为employeeName
            'account': user_claims.get('username'),  # 使用username作为account
            'userId': userid,  # 使用userid作为userId

            # 新token格式的字段
            'userid': userid,
            'username': user_claims.get('username'),
            'level': user_claims.get('level'),
            'user_nickname': user_claims.get('user_nickname'),
            'user_icon': user_claims.get('user_icon'),
            'user_center_id': user_claims.get('user_center_id'),

            # token元数据
            'iat': claims.get('iat'),  # 签发时间
            'exp': claims.get('exp'),  # 过期时间
            'nbf': claims.get('nbf'),  # 生效时间
            'jti': claims.get('jti'),  # JWT ID
            'identity': claims.get('identity'),
            'fresh': claims.get('fresh'),
            'type': claims.get('type'),

            # 原始token
            'token': token
        }

        return user_info

    @staticmethod
    def _parse_token_without_key(token: str) -> Optional[Dict[str, Any]]:
        """
        解析token字符串，不需要密钥
        """
        if not token or token == "null":
            return None

        try:
            # 分割token获取claims部分
            parts = token.split('.')
            if len(parts) < 2:
                raise TokenInvalidFormatError(
                    "Token格式错误",
                    TokenUtils.ERROR_CODES['TOKEN_INVALID_FORMAT'],
                    f"Token应该包含3个部分，实际包含{len(parts)}个部分"
                )

            # 获取claims部分（通常是第二部分）
            claims_part = parts[1]

            # Base64解码
            try:
                # 处理URL安全的Base64字符
                claims_part = claims_part.replace('-', '+').replace('_', '/')

                # 确保padding
                padding = 4 - len(claims_part) % 4
                if padding != 4:
                    claims_part += '=' * padding

                # Base64解码
                decoded_bytes = base64.b64decode(claims_part)
                decoded_str = decoded_bytes.decode('utf-8')
            except Exception as e:
                raise TokenError(
                    "Base64解码失败",
                    TokenUtils.ERROR_CODES['TOKEN_BASE64_ERROR'],
                    f"无法解码token的payload部分: {str(e)}"
                )

            # JSON解析
            try:
                claims = json.loads(decoded_str)
                return claims
            except json.JSONDecodeError as e:
                raise TokenError(
                    "JSON解析失败",
                    TokenUtils.ERROR_CODES['TOKEN_JSON_ERROR'],
                    f"无法解析token的JSON内容: {str(e)}"
                )

        except TokenError:
            # 重新抛出TokenError
            raise
        except Exception as e:
            logging.error(f"解析token发生错误: {token}, 错误: {str(e)}")
            raise TokenError(
                f"Token解析失败: {str(e)}",
                TokenUtils.ERROR_CODES['TOKEN_PARSE_ERROR'],
                f"解析过程中发生异常: {str(e)}"
            )

    @staticmethod
    def validate_token(token: str) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """
        验证token并返回结果
        返回: (是否有效, 用户信息, 错误信息)
        """
        try:
            user_info = TokenUtils.parse_token(token)
            return True, user_info, None
        except TokenError as e:
            return False, None, f"{e.error_code}: {e.message}"
        except Exception as e:
            return False, None, f"未知错误: {str(e)}"


class Base64Util:
    """Base64工具类，基于Java代码转换"""

    @staticmethod
    def decode_to_string(data: str) -> str:
        """Base64解码为字符串"""
        try:
            # 处理URL安全的Base64字符
            data = data.replace('-', '+').replace('_', '/')
            # 确保padding
            data = Base64Util._ensure_padding(data)
            # 解码
            decoded_bytes = base64.b64decode(data)
            return decoded_bytes.decode('utf-8')
        except Exception as e:
            logging.error(f"Base64解码错误: {str(e)}")
            return ""

    @staticmethod
    def decode(data: str) -> bytes:
        """Base64解码为字节"""
        try:
            # 处理URL安全的Base64字符
            data = data.replace('-', '+').replace('_', '/')
            # 确保padding
            data = Base64Util._ensure_padding(data)
            # 解码
            return base64.b64decode(data)
        except Exception as e:
            logging.error(f"Base64解码错误: {str(e)}")
            return b""

    @staticmethod
    def _ensure_padding(data: str) -> str:
        """确保Base64字符串有正确的padding"""
        padding = 4 - len(data) % 4
        if padding != 4:
            data += '=' * padding
        return data


if __name__ == '__main__':
    # 测试新的token格式
    test_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTQwMzE1MjUsIm5iZiI6MTc1NDAzMTUyNSwianRpIjoiMTUwYTE0YzAtMTk3MS00NmVmLTliMjQtZDlmMTQ1MzdhYjM0IiwiZXhwIjoxNzU0NjM2MzI1LCJpZGVudGl0eSI6WyJ6aGlQdV9razA4OCIsbnVsbF0sImZyZXNoIjpmYWxzZSwidHlwZSI6ImFjY2VzcyIsInVzZXJfY2xhaW1zIjp7InVzZXJpZCI6MjMsInVzZXJuYW1lIjoiemhpUHVfa2swODgiLCJsZXZlbCI6Ilx1NjY2ZVx1OTAxYVx1NzUyOFx1NjIzNyIsInVzZXJfbmlja25hbWUiOiJ6aGlQdV9razA4OCIsInVzZXJfaWNvbiI6Ii4vYXNzZXRzL2ltYWdlcy9hdmF0YXIuc3ZnIiwidXNlcl9jZW50ZXJfaWQiOjE0fX0.qOwAuRHbIZ-mewKZKmcMdVZx6ktqNTY2sJxuG1rMNDU"

    try:
        token_info = TokenUtils.parse_token(test_token)
        print("✅ Token解析成功:")
        print(json.dumps(token_info, indent=2, ensure_ascii=False))
    except TokenError as e:
        print(f"❌ Token解析失败: {e.error_code} - {e.message}")
        if e.details:
            print(f"详细信息: {e.details}")
    except Exception as e:
        print(f"❌ 未知错误: {str(e)}")
