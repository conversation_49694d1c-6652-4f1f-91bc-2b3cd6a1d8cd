"""add workflow tool

Revision ID: 7bdef072e63a
Revises: 5fda94355fce
Create Date: 2024-05-04 09:47:19.366961

"""
import sqlalchemy as sa
from alembic import op

import models.types

# revision identifiers, used by Alembic.
revision = '7bdef072e63a'
down_revision = '5fda94355fce'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tool_workflow_providers',
    sa.Column('id', models.types.StringUUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('name', sa.String(length=40), nullable=False),
    sa.Column('icon', sa.String(length=255), nullable=False),
    sa.Column('app_id', models.types.StringUUID(), nullable=False),
    sa.Column('user_id', models.types.StringUUID(), nullable=False),
    sa.Column('tenant_id', models.types.StringUUID(), nullable=False),
    sa.Column('description', sa.Text(), nullable=False),
    sa.Column('parameter_configuration', sa.Text(), server_default='[]', nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='tool_workflow_provider_pkey'),
    sa.UniqueConstraint('name', 'tenant_id', name='unique_workflow_tool_provider'),
    sa.UniqueConstraint('tenant_id', 'app_id', name='unique_workflow_tool_provider_app_id')
    )
    # ### end Alembic commands ###


def downgrade():
    op.drop_table('tool_workflow_providers')
    # ### end Alembic commands ###
