from flask import Flask, request, redirect, url_for, jsonify, render_template_string, session
import uuid
import time

# 初始化 Flask 应用
app = Flask(__name__)
# session需要一个密钥
app.secret_key = 'a_super_secret_key_for_sso_mock'

# --- 模拟数据库和配置 ---

# 1. 模拟已注册的客户端（您的业务系统）
VALID_CLIENTS = {
    'oauth2DemoTest': {
        'secret': 'your_client_secret_123',
        'redirect_uri': 'http://127.0.0.1:3000' # 您的业务系统的回调地址
    }
}

# 2. 模拟用户信息
MOCK_USER = {
    'userId': 'mock-user-001',
    'loginName': '<EMAIL>',
    'realName': '模拟用户张三',
    'mobile': '13800138000',
    'email': '<EMAIL>',
    'userStatus': '1' # 1 正常 0 禁用
}

# 3. 模拟内存存储 (在真实场景中应使用数据库或Redis)
auth_codes = {}
access_tokens = {}
refresh_tokens = {}

# --- 辅助函数 ---

def generate_token():
    """生成一个唯一的令牌"""
    return str(uuid.uuid4())

# --- 接口实现 ---

@app.route('/')
def index():
    return "<h1>SSO Mock Server is running.</h1>"

# 1. 授权接口 (/idp/authCenter/authenticate)
@app.route('/idp/authCenter/authenticate', methods=['GET'])
def authenticate():
    client_id = request.args.get('client_id')
    redirect_uri = request.args.get('redirect_uri')
    state = request.args.get('state')

    # 简单验证客户端
    if client_id not in VALID_CLIENTS or redirect_uri != VALID_CLIENTS[client_id]['redirect_uri']:
        return jsonify({"error": "invalid_client or redirect_uri mismatch"}), 400

    # 将参数存入session，以便登录后使用
    session['client_id'] = client_id
    session['redirect_uri'] = redirect_uri
    session['state'] = state

    # 渲染一个模拟的登录页面
    login_form_html = """
    <!DOCTYPE html>
    <html>
    <head><title>Mock SSO Login</title></head>
    <body>
        <h2>Mock Digital Identity Platform Login</h2>
        <form action="/login" method="post">
            <p>Username: <input type="text" name="username" value="<EMAIL>"></p>
            <p>Password: <input type="password" name="password" value="password"></p>
            <p><input type="submit" value="Login"></p>
        </form>
        <p><i>(Use any username/password, it will succeed)</i></p>
    </body>
    </html>
    """
    return render_template_string(login_form_html)


# 模拟登录处理 (这是authenticate接口的辅助路由)
@app.route('/login', methods=['POST'])
def login():
    # 验证成功后，生成授权码
    code = generate_token()

    # 存储授权码，关联用户信息和客户端ID，并设置有效期
    auth_codes[code] = {
        'client_id': session.get('client_id'),
        'user': MOCK_USER,
        'expiry': time.time() + 300 # 5分钟有效期
    }

    print(f"Generated auth_code: {code} for user: {MOCK_USER['loginName']}")

    # 构建重定向URL
    redirect_url = f"{session.get('redirect_uri')}?code={code}&state={session.get('state')}"
    return redirect(redirect_url)


# 2. 获取令牌接口 (/am-gateway/.../oauth2/getToken)
# 为了简化URL，我们使用一个短路径
@app.route('/oauth2/getToken', methods=['POST'])
def get_token():
    grant_type = request.form.get('grant_type')

    if grant_type == 'authorization_code':
        code = request.form.get('code')
        client_id = request.form.get('client_id')
        client_secret = request.form.get('client_secret')

        # 验证code
        if code not in auth_codes or auth_codes[code]['expiry'] < time.time():
            return jsonify({"error": "invalid_code", "msg": "Authorization code is invalid or expired."}), 400

        # 验证客户端
        if client_id != auth_codes[code]['client_id'] or client_secret != VALID_CLIENTS[client_id]['secret']:
            return jsonify({"error": "invalid_client", "msg": "Client ID or secret is incorrect."}), 400

        user_info = auth_codes.pop(code)['user'] # Code只能使用一次

        # 生成令牌
        new_access_token = generate_token()
        new_refresh_token = generate_token()

        access_tokens[new_access_token] = {'user_id': user_info['userId'], 'expiry': time.time() + 3600} # 1小时有效期
        refresh_tokens[new_refresh_token] = {'user_id': user_info['userId'], 'expiry': time.time() + 86400 * 7} # 7天有效期

        print(f"Issued access_token: {new_access_token}")

        return jsonify({
            "access_token": new_access_token,
            "refresh_token": new_refresh_token,
            "expires_in": 3600,
            "uid": user_info['userId']
        })

    elif grant_type == 'refresh_token':
        refresh_token = request.form.get('refresh_token')

        if refresh_token not in refresh_tokens or refresh_tokens[refresh_token]['expiry'] < time.time():
            return jsonify({"error": "invalid_refresh_token", "msg": "Refresh token is invalid or expired."}), 400

        user_id = refresh_tokens[refresh_token]['user_id']

        # 颁发新令牌
        new_access_token = generate_token()
        access_tokens[new_access_token] = {'user_id': user_id, 'expiry': time.time() + 3600}

        return jsonify({
            "access_token": new_access_token,
            "expires_in": 3600,
            "uid": user_id
        })

    return jsonify({"error": "unsupported_grant_type"}), 400


# 3. 获取用户信息接口 (/am-gateway/.../oauth2/getUserInfo)
@app.route('/oauth2/getUserInfo', methods=['GET'])
def get_user_info():
    auth_header = request.headers.get('Authorization')
    access_token = request.args.get('access_token')

    if auth_header:
        parts = auth_header.split()
        if len(parts) == 2 and parts[0].lower() == 'bearer':
            token_from_header = parts[1]
            if access_token and access_token != token_from_header:
                return jsonify({"error": "token_mismatch"}), 400
            access_token = token_from_header

    if not access_token:
        return jsonify({"error": "missing_token"}), 401

    if access_token not in access_tokens or access_tokens[access_token]['expiry'] < time.time():
        return jsonify({"error": "invalid_token", "msg": "Access token is invalid or expired."}), 401

    return jsonify(MOCK_USER)


# 4. 统一注销接口 (/idp/authCenter/GLO)
@app.route('/idp/authCenter/GLO', methods=['GET'])
def global_logout():
    redirect_to_url = request.args.get('redirectToUrl')
    # 清理session
    session.clear()

    # 实际应用中，这里会吊销所有相关令牌
    print("User logged out globally.")

    if redirect_to_url:
        return redirect(redirect_to_url)

    return "<h1>You have been logged out.</h1>"


if __name__ == '__main__':
    # 注意: 生产环境不要用 debug=True
    # 使用 5001 端口避免与常用端口冲突
    app.run(host='0.0.0.0', port=5009, debug=True)
