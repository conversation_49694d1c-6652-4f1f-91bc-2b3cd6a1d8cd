const translation = {
  title: 'Testarea Recuperării',
  desc: 'Testați efectul de atingere al Cunoștințelor pe baza textului interogat dat.',
  dateTimeFormat: 'DD/MM/YYYY hh:mm A',
  recents: 'Recente',
  table: {
    header: {
      source: 'Sursă',
      text: 'Text',
      time: 'Timp',
    },
  },
  input: {
    title: 'Text sursă',
    placeholder: 'Vă rugăm să introduceți un text, se recomandă o propoziție declarativă scurtă.',
    countWarning: 'Până la 200 de caractere.',
    indexWarning: 'Doar Cunoștințe de înaltă calitate.',
    testing: 'Testare',
  },
  hit: {
    title: 'PARAGRAFE DE RECUPERARE',
    emptyTip: 'Rezultatele testării de recuperare vor apărea aici',
  },
  noRecentTip: 'Nu există rezultate de interogare recente aici',
  viewChart: 'Vizualizați GRAFICUL VECTORIAL',
  settingTitle: 'Setare de recuperare',
  viewDetail: 'Vezi detalii',
  keyword: 'Cuvinte cheie',
  chunkDetail: 'Detalii bucăți',
  open: 'Deschide',
  hitChunks: 'Accesează {{num}} bucăți copil',
  records: 'Înregistrări',
}

export default translation
