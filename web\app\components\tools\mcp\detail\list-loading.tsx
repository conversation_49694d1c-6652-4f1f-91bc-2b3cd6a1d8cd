'use client'
import React from 'react'
import cn from '@/utils/classnames'

const ListLoading = () => {
  return (
    <div className={cn('space-y-2')}>
      <div className='space-y-3 rounded-xl bg-components-panel-on-panel-item-bg-hover p-4'>
        <div className='h-2 w-[180px] rounded-sm bg-text-quaternary opacity-20'></div>
        <div className='h-2 rounded-sm bg-text-quaternary opacity-10'></div>
        <div className='mr-10 h-2 rounded-sm bg-text-quaternary opacity-10'></div>
      </div>
      <div className='space-y-3 rounded-xl bg-components-panel-on-panel-item-bg-hover p-4'>
        <div className='h-2 w-[148px] rounded-sm bg-text-quaternary opacity-20'></div>
        <div className='h-2 rounded-sm bg-text-quaternary opacity-10'></div>
        <div className='mr-10 h-2 rounded-sm bg-text-quaternary opacity-10'></div>
      </div>
      <div className='space-y-3 rounded-xl bg-components-panel-on-panel-item-bg-hover p-4'>
        <div className='h-2 w-[196px] rounded-sm bg-text-quaternary opacity-20'></div>
        <div className='h-2 rounded-sm bg-text-quaternary opacity-10'></div>
        <div className='mr-10 h-2 rounded-sm bg-text-quaternary opacity-10'></div>
      </div>
      <div className='space-y-3 rounded-xl bg-components-panel-on-panel-item-bg-hover p-4'>
        <div className='h-2 w-[148px] rounded-sm bg-text-quaternary opacity-20'></div>
        <div className='h-2 rounded-sm bg-text-quaternary opacity-10'></div>
        <div className='mr-10 h-2 rounded-sm bg-text-quaternary opacity-10'></div>
      </div>
      <div className='space-y-3 rounded-xl bg-components-panel-on-panel-item-bg-hover p-4'>
        <div className='h-2 w-[180px] rounded-sm bg-text-quaternary opacity-20'></div>
        <div className='h-2 rounded-sm bg-text-quaternary opacity-10'></div>
        <div className='mr-10 h-2 rounded-sm bg-text-quaternary opacity-10'></div>
      </div>
    </div>
  )
}

export default ListLoading
