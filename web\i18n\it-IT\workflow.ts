const translation = {
  common: {
    undo: '<PERSON><PERSON>a',
    redo: '<PERSON><PERSON><PERSON>',
    editing: 'Modifica in corso',
    autoSaved: 'Salvataggio automatico',
    unpublished: 'Non pubblicato',
    published: 'Pubblicato',
    publish: 'Pubblica',
    update: 'Aggiorna',
    run: 'Esegui',
    running: 'In esecuzione',
    inRunMode: 'In modalità di esecuzione',
    inPreview: 'In anteprima',
    inPreviewMode: 'In modalità anteprima',
    preview: 'Anteprima',
    viewRunHistory: 'Visualizza cronologia esecuzioni',
    runHistory: 'Cronologia esecuzioni',
    goBackToEdit: 'Torna all\'editor',
    conversationLog: 'Registro conversazioni',
    features: 'Caratteristiche',
    debugAndPreview: 'Anteprima',
    restart: 'Riavvia',
    currentDraft: 'Bozza corrente',
    currentDraftUnpublished: 'Bozza corrente non pubblicata',
    latestPublished: 'Ultimo pubblicato',
    publishedAt: 'Pubblicato',
    restore: '<PERSON><PERSON><PERSON><PERSON>',
    runApp: 'Esegui App',
    batchRunApp: 'Esegui App in Batch',
    accessAPIReference: 'Accedi alla Riferimento API',
    embedIntoSite: 'Incorpora nel Sito',
    addTitle: 'Aggiungi titolo...',
    addDescription: 'Aggiungi descrizione...',
    noVar: 'Nessuna variabile',
    searchVar: 'Cerca variabile',
    variableNamePlaceholder: 'Nome variabile',
    setVarValuePlaceholder: 'Imposta variabile',
    needConnectTip: 'Questo passaggio non è collegato a nulla',
    maxTreeDepth: 'Limite massimo di {{depth}} nodi per ramo',
    workflowProcess: 'Processo di flusso di lavoro',
    notRunning: 'Non ancora in esecuzione',
    previewPlaceholder:
      'Inserisci contenuto nella casella sottostante per avviare il debug del Chatbot',
    effectVarConfirm: {
      title: 'Rimuovi Variabile',
      content:
        'La variabile è utilizzata in altri nodi. Vuoi comunque rimuoverla?',
    },
    insertVarTip: 'Premi il tasto \'/\' per inserire rapidamente',
    processData: 'Elabora Dati',
    input: 'Input',
    output: 'Output',
    jinjaEditorPlaceholder: 'Digita \'/\' o \'{\' per inserire variabile',
    viewOnly: 'Solo visualizzazione',
    showRunHistory: 'Mostra cronologia esecuzioni',
    enableJinja: 'Abilita supporto template Jinja',
    learnMore: 'Scopri di più',
    copy: 'Copia',
    duplicate: 'Duplica',
    pasteHere: 'Incolla Qui',
    pointerMode: 'Modalità Puntatore',
    handMode: 'Modalità Mano',
    model: 'Modello',
    workflowAsTool: 'Flusso di lavoro come Strumento',
    configureRequired: 'Configurazione Richiesta',
    configure: 'Configura',
    manageInTools: 'Gestisci in Strumenti',
    workflowAsToolTip:
      'È richiesta una nuova configurazione dello strumento dopo l\'aggiornamento del flusso di lavoro.',
    viewDetailInTracingPanel: 'Visualizza dettagli',
    syncingData: 'Sincronizzazione dei dati in corso, solo pochi secondi.',
    importDSL: 'Importa DSL',
    importDSLTip:
      'La bozza corrente verrà sovrascritta. Esporta il flusso di lavoro come backup prima di importare.',
    backupCurrentDraft: 'Backup Bozza Corrente',
    chooseDSL: 'Scegli file DSL(yml)',
    overwriteAndImport: 'Sovrascrivi e Importa',
    importFailure: 'Importazione fallita',
    importSuccess: 'Importazione riuscita',
    parallelTip: {
      click: {
        title: 'Clic',
        desc: 'per aggiungere',
      },
      drag: {
        title: 'Trascinare',
        desc: 'per collegare',
      },
      depthLimit: 'Limite di livelli di annidamento parallelo di {{num}} livelli',
      limit: 'Il parallelismo è limitato ai rami {{num}}.',
    },
    parallelRun: 'Corsa parallela',
    disconnect: 'Disconnettere',
    jumpToNode: 'Vai a questo nodo',
    addParallelNode: 'Aggiungi nodo parallelo',
    parallel: 'PARALLELO',
    branch: 'RAMO',
    featuresDocLink: 'Ulteriori informazioni',
    featuresDescription: 'Migliora l\'esperienza utente dell\'app Web',
    fileUploadTip: 'Le funzioni di caricamento delle immagini sono state aggiornate al caricamento dei file.',
    ImageUploadLegacyTip: 'Ora è possibile creare variabili di tipo file nel modulo iniziale. In futuro non supporteremo più la funzione di caricamento delle immagini.',
    importWarning: 'Cautela',
    importWarningDetails: 'La differenza di versione DSL può influire su alcune funzionalità',
    openInExplore: 'Apri in Esplora',
    onFailure: 'In caso di guasto',
    addFailureBranch: 'Aggiungi ramo non riuscito',
    noHistory: 'Nessuna storia',
    loadMore: 'Carica più flussi di lavoro',
    publishUpdate: 'Pubblica aggiornamento',
    versionHistory: 'Cronologia delle versioni',
    exitVersions: 'Uscita Versioni',
    referenceVar: 'Variabile di riferimento',
    exportSVG: 'Esporta come SVG',
    exportImage: 'Esporta immagine',
    exportJPEG: 'Esporta come JPEG',
    noExist: 'Nessuna variabile del genere',
    exportPNG: 'Esporta come PNG',
    needEndNode: 'Deve essere aggiunto il nodo finale',
    addBlock: 'Aggiungi nodo',
    needAnswerNode: 'Deve essere aggiunto il nodo di risposta',
  },
  env: {
    envPanelTitle: 'Variabili d\'Ambiente',
    envDescription: 'Le variabili d\'ambiente possono essere utilizzate per memorizzare informazioni private e credenziali. Sono di sola lettura e possono essere separate dal file DSL durante l\'esportazione.',
    envPanelButton: 'Aggiungi Variabile',
    modal: {
      title: 'Aggiungi Variabile d\'Ambiente',
      editTitle: 'Modifica Variabile d\'Ambiente',
      type: 'Tipo',
      name: 'Nome',
      namePlaceholder: 'nome env',
      value: 'Valore',
      valuePlaceholder: 'valore env',
      secretTip: 'Utilizzato per definire informazioni o dati sensibili, con impostazioni DSL configurate per la prevenzione delle fughe.',
      description: 'Descrizione',
      descriptionPlaceholder: 'Descrivi la variabile',
    },
    export: {
      title: 'Esportare variabili d\'ambiente segrete?',
      checkbox: 'Esporta valori segreti',
      ignore: 'Esporta DSL',
      export: 'Esporta DSL con valori segreti',
    },
  },
  chatVariable: {
    panelTitle: 'Variabili di Conversazione',
    panelDescription: 'Le Variabili di Conversazione sono utilizzate per memorizzare informazioni interattive che il LLM deve ricordare, inclusi la cronologia delle conversazioni, i file caricati e le preferenze dell\'utente. Sono in lettura e scrittura.',
    docLink: 'Visita la nostra documentazione per saperne di più.',
    button: 'Aggiungi Variabile',
    modal: {
      title: 'Aggiungi Variabile di Conversazione',
      editTitle: 'Modifica Variabile di Conversazione',
      name: 'Nome',
      namePlaceholder: 'Nome della variabile',
      type: 'Tipo',
      value: 'Valore Predefinito',
      valuePlaceholder: 'Valore predefinito, lascia vuoto per non impostare',
      description: 'Descrizione',
      descriptionPlaceholder: 'Descrivi la variabile',
      editInJSON: 'Modifica in JSON',
      oneByOne: 'Aggiungi uno alla volta',
      editInForm: 'Modifica nel Modulo',
      arrayValue: 'Valore',
      addArrayValue: 'Aggiungi Valore',
      objectKey: 'Chiave',
      objectType: 'Tipo',
      objectValue: 'Valore Predefinito',
    },
    storedContent: 'Contenuto memorizzato',
    updatedAt: 'Aggiornato il ',
  },
  changeHistory: {
    title: 'Cronologia Modifiche',
    placeholder: 'Non hai ancora modificato nulla',
    clearHistory: 'Cancella Cronologia',
    hint: 'Suggerimento',
    hintText:
      'Le tue azioni di modifica vengono tracciate in una cronologia delle modifiche, che viene memorizzata sul tuo dispositivo per tutta la durata di questa sessione. Questa cronologia verrà cancellata quando lascerai l\'editor.',
    stepBackward_one: '{{count}} passo indietro',
    stepBackward_other: '{{count}} passi indietro',
    stepForward_one: '{{count}} passo avanti',
    stepForward_other: '{{count}} passi avanti',
    sessionStart: 'Inizio sessione',
    currentState: 'Stato attuale',
    noteAdd: 'Nota aggiunta',
    noteChange: 'Nota modificata',
    noteDelete: 'Nota eliminata',
    nodeDescriptionChange: 'Descrizione del nodo cambiata',
    nodePaste: 'Nodo incollato',
    nodeChange: 'Nodo cambiato',
    nodeResize: 'Nodo ridimensionato',
    nodeDelete: 'Nodo eliminato',
    nodeTitleChange: 'Titolo del nodo cambiato',
    edgeDelete: 'Nodo disconnesso',
    nodeAdd: 'Nodo aggiunto',
    nodeDragStop: 'Nodo spostato',
    nodeConnect: 'Nodo connesso',
  },
  errorMsg: {
    fieldRequired: '{{field}} è richiesto',
    authRequired: 'È richiesta l\'autorizzazione',
    invalidJson: '{{field}} è un JSON non valido',
    fields: {
      variable: 'Nome Variabile',
      variableValue: 'Valore Variabile',
      code: 'Codice',
      model: 'Modello',
      rerankModel: 'Modello Rerank',
      visionVariable: 'Visione variabile',
    },
    invalidVariable: 'Variabile non valida',
    rerankModelRequired: 'Prima di attivare il modello di reranking, conferma che il modello è stato configurato correttamente nelle impostazioni.',
    toolParameterRequired: '{{field}}: il parametro [{{param}}] è obbligatorio',
    noValidTool: '{{field}} nessuno strumento valido selezionato',
  },
  singleRun: {
    testRun: 'Esecuzione Test ',
    startRun: 'Avvia Esecuzione',
    running: 'In esecuzione',
    testRunIteration: 'Iterazione Esecuzione Test',
    back: 'Indietro',
    iteration: 'Iterazione',
    loop: 'Anello',
  },
  tabs: {
    'tools': 'Strumenti',
    'allTool': 'Tutti',
    'builtInTool': 'Integrato',
    'customTool': 'Personalizzato',
    'workflowTool': 'Flusso di lavoro',
    'question-understand': 'Comprensione Domanda',
    'logic': 'Logica',
    'transform': 'Trasforma',
    'utilities': 'Utility',
    'noResult': 'Nessuna corrispondenza trovata',
    'searchTool': 'Strumento di ricerca',
    'agent': 'Strategia dell\'agente',
    'plugin': 'Plugin',
    'searchBlock': 'Cerca nodo',
    'blocks': 'Nodi',
  },
  blocks: {
    'start': 'Inizio',
    'end': 'Fine',
    'answer': 'Risposta',
    'llm': 'LLM',
    'knowledge-retrieval': 'Recupero Conoscenza',
    'question-classifier': 'Classificatore Domande',
    'if-else': 'SE/ALTRIMENTI',
    'code': 'Codice',
    'template-transform': 'Template',
    'http-request': 'Richiesta HTTP',
    'variable-assigner': 'Assegnatore Variabili',
    'variable-aggregator': 'Aggregatore Variabili',
    'assigner': 'Assegnatore di Variabili',
    'iteration-start': 'Inizio Iterazione',
    'iteration': 'Iterazione',
    'parameter-extractor': 'Estrattore Parametri',
    'document-extractor': 'Estrattore di documenti',
    'list-operator': 'Operatore di elenco',
    'agent': 'Agente',
    'loop-end': 'Uscire dal ciclo',
    'loop-start': 'Inizio ciclo',
    'loop': 'Anello',
  },
  blocksAbout: {
    'start': 'Definisci i parametri iniziali per l\'avvio di un flusso di lavoro',
    'end': 'Definisci la fine e il tipo di risultato di un flusso di lavoro',
    'answer': 'Definisci il contenuto della risposta di una conversazione chat',
    'llm': 'Invoca modelli di linguaggio di grandi dimensioni per rispondere a domande o elaborare il linguaggio naturale',
    'knowledge-retrieval':
      'Ti consente di interrogare il contenuto del testo relativo alle domande dell\'utente dalla Conoscenza',
    'question-classifier':
      'Definisci le condizioni di classificazione delle domande dell\'utente, LLM può definire come prosegue la conversazione in base alla descrizione della classificazione',
    'if-else':
      'Ti consente di dividere il flusso di lavoro in due rami basati su condizioni se/altrimenti',
    'code': 'Esegui un pezzo di codice Python o NodeJS per implementare la logica personalizzata',
    'template-transform':
      'Converti i dati in stringa usando la sintassi del template Jinja',
    'http-request':
      'Consenti l\'invio di richieste server tramite il protocollo HTTP',
    'variable-assigner':
      'Aggrega variabili multi-ramo in una singola variabile per la configurazione unificata dei nodi a valle.',
    'assigner': 'Il nodo di assegnazione delle variabili è utilizzato per assegnare valori a variabili scrivibili (come le variabili di conversazione).',
    'variable-aggregator':
      'Aggrega variabili multi-ramo in una singola variabile per la configurazione unificata dei nodi a valle.',
    'iteration':
      'Esegui più passaggi su un oggetto lista fino a quando tutti i risultati non sono stati prodotti.',
    'parameter-extractor':
      'Usa LLM per estrarre parametri strutturati dal linguaggio naturale per invocazioni di strumenti o richieste HTTP.',
    'list-operator': 'Utilizzato per filtrare o ordinare il contenuto della matrice.',
    'document-extractor': 'Utilizzato per analizzare i documenti caricati in contenuti di testo facilmente comprensibili da LLM.',
    'agent': 'Richiamo di modelli linguistici di grandi dimensioni per rispondere a domande o elaborare il linguaggio naturale',
    'loop-end': 'Equivalente a "break". Questo nodo non ha elementi di configurazione. Quando il corpo del ciclo raggiunge questo nodo, il ciclo termina.',
    'loop': 'Esegui un ciclo di logica fino a quando la condizione di terminazione non viene soddisfatta o il numero massimo di cicli viene raggiunto.',
  },
  operator: {
    zoomIn: 'Zoom In',
    zoomOut: 'Zoom Out',
    zoomTo50: 'Zoom al 50%',
    zoomTo100: 'Zoom al 100%',
    zoomToFit: 'Zoom per Adattare',
  },
  panel: {
    userInputField: 'Campo di Input Utente',
    helpLink: 'Link di Aiuto',
    about: 'Informazioni',
    createdBy: 'Creato da ',
    nextStep: 'Prossimo Passo',
    runThisStep: 'Esegui questo passo',
    checklist: 'Checklist',
    checklistTip:
      'Assicurati che tutti i problemi siano risolti prima di pubblicare',
    checklistResolved: 'Tutti i problemi sono risolti',
    change: 'Cambia',
    optional: '(opzionale)',
    moveToThisNode: 'Sposta a questo nodo',
    changeBlock: 'Cambia Nodo',
    selectNextStep: 'Seleziona il prossimo passo',
    organizeBlocks: 'Organizzare i nodi',
    addNextStep: 'Aggiungi il prossimo passo in questo flusso di lavoro',
    minimize: 'Esci dalla modalità schermo intero',
    maximize: 'Massimizza Canvas',
  },
  nodes: {
    common: {
      outputVars: 'Variabili di Output',
      insertVarTip: 'Inserisci Variabile',
      memory: {
        memory: 'Memoria',
        memoryTip: 'Impostazioni memoria chat',
        windowSize: 'Dimensione Finestra',
        conversationRoleName: 'Nome Ruolo Conversazione',
        user: 'Prefisso Utente',
        assistant: 'Prefisso Assistente',
      },
      memories: {
        title: 'Memorie',
        tip: 'Memoria chat',
        builtIn: 'Integrato',
      },
      errorHandle: {
        none: {
          title: 'Nessuno',
          desc: 'L\'esecuzione del nodo verrà interrotta se si verifica un\'eccezione e non viene gestita',
        },
        defaultValue: {
          title: 'Valore predefinito',
          desc: 'Quando si verifica un errore, specificare un contenuto di output statico.',
          tip: 'In caso di errore, tornerà al di sotto del valore.',
          inLog: 'Eccezione del nodo, output in base ai valori predefiniti.',
          output: 'Valore predefinito di output',
        },
        failBranch: {
          title: 'Ramo fallito',
          desc: 'Quando si verifica un errore, eseguirà il ramo dell\'eccezione',
          customize: 'Passare all\'area di disegno per personalizzare la logica del ramo di errore.',
          customizeTip: 'Quando il ramo di errore è attivato, le eccezioni generate dai nodi non termineranno il processo. Al contrario, eseguirà automaticamente il ramo di errore predefinito, consentendo di fornire in modo flessibile messaggi di errore, report, correzioni o azioni di salto.',
          inLog: 'Eccezione nodo, eseguirà automaticamente il ramo di errore. L\'output del nodo restituirà un tipo di errore e un messaggio di errore e li passerà al downstream.',
        },
        partialSucceeded: {
          tip: 'Ci sono {{num}} nodi nel processo che funzionano in modo anomalo, si prega di andare su tracing per controllare i log.',
        },
        title: 'Gestione degli errori',
        tip: 'Strategia di gestione delle eccezioni, attivata quando un nodo rileva un\'eccezione.',
      },
      retry: {
        retry: 'Ripetere',
        retryOnFailure: 'Riprova in caso di errore',
        maxRetries: 'Numero massimo di tentativi',
        retryInterval: 'Intervallo tentativi',
        retryTimes: 'Riprova {{times}} volte in caso di errore',
        retrying: 'Riprovare...',
        retryFailedTimes: '{{times}} tentativi falliti',
        times: 'tempi',
        retries: '{{num}} Tentativi',
        retrySuccessful: 'Riprova riuscito',
        retryFailed: 'Nuovo tentativo non riuscito',
        ms: 'ms',
      },
      typeSwitch: {},
    },
    start: {
      required: 'richiesto',
      inputField: 'Campo di Input',
      builtInVar: 'Variabili Integrate',
      outputVars: {
        query: 'Input Utente',
        memories: {
          des: 'Cronologia conversazioni',
          type: 'tipo di messaggio',
          content: 'contenuto del messaggio',
        },
        files: 'Elenco file',
      },
      noVarTip:
        'Imposta gli input che possono essere utilizzati nel Flusso di lavoro',
    },
    end: {
      outputs: 'Output',
      output: {
        type: 'tipo di output',
        variable: 'variabile di output',
      },
      type: {
        'none': 'Nessuno',
        'plain-text': 'Testo Semplice',
        'structured': 'Strutturato',
      },
    },
    answer: {
      answer: 'Risposta',
      outputVars: 'Variabili di Output',
    },
    llm: {
      model: 'modello',
      variables: 'variabili',
      context: 'contesto',
      contextTooltip: 'Puoi importare Conoscenza come contesto',
      notSetContextInPromptTip:
        'Per abilitare la funzionalità di contesto, compila la variabile del contesto nel PROMPT.',
      prompt: 'prompt',
      roleDescription: {
        system: 'Fornisci istruzioni di alto livello per la conversazione',
        user: 'Fornisci istruzioni, query o qualsiasi input basato su testo al modello',
        assistant: 'Le risposte del modello basate sui messaggi dell\'utente',
      },
      addMessage: 'Aggiungi Messaggio',
      vision: 'vision',
      files: 'File',
      resolution: {
        name: 'Risoluzione',
        high: 'Alta',
        low: 'Bassa',
      },
      outputVars: {
        output: 'Genera contenuto',
        usage: 'Informazioni sull\'utilizzo del modello',
      },
      singleRun: {
        variable: 'Variabile',
      },
      sysQueryInUser: 'sys.query nel messaggio utente è richiesto',
      jsonSchema: {
        warningTips: {
          saveSchema: 'Si prega di completare la modifica del campo corrente prima di salvare lo schema.',
        },
        descriptionPlaceholder: 'Aggiungi descrizione',
        generate: 'Genera',
        generateJsonSchema: 'Genera Schema JSON',
        generationTip: 'Puoi usare il linguaggio naturale per creare rapidamente uno schema JSON.',
        back: 'Indietro',
        apply: 'Applica',
        showAdvancedOptions: 'Mostra opzioni avanzate',
        stringValidations: 'Validazioni delle stringhe',
        regenerate: 'Rigenerare',
        required: 'richiesto',
        resetDefaults: 'Ripristina',
        addField: 'Aggiungi campo',
        promptPlaceholder: 'Descrivi il tuo schema JSON...',
        title: 'Schema di Output Strutturato',
        instruction: 'Istruzione',
        addChildField: 'Aggiungi campo bambino',
        fieldNamePlaceholder: 'Nome del campo',
        promptTooltip: 'Converte la descrizione del testo in una struttura JSON Schema standardizzata.',
        doc: 'Scopri di più sull\'output strutturato',
        import: 'Importa da JSON',
        resultTip: 'Ecco il risultato generato. Se non sei soddisfatto, puoi tornare indietro e modificare il tuo prompt.',
        generating: 'Generazione dello schema JSON...',
        generatedResult: 'Risultato generato',
      },
    },
    knowledgeRetrieval: {
      queryVariable: 'Variabile Query',
      knowledge: 'Conoscenza',
      outputVars: {
        output: 'Dati segmentati di recupero',
        content: 'Contenuto segmentato',
        title: 'Titolo segmentato',
        icon: 'Icona segmentata',
        url: 'URL segmentato',
        metadata: 'Altri metadati',
      },
      metadata: {
        options: {
          disabled: {
            title: 'Disabilitato',
            subTitle: 'Non abilitare il filtraggio dei metadati',
          },
          automatic: {
            subTitle: 'Genera automaticamente condizioni di filtraggio dei metadati in base alla query dell\'utente',
            desc: 'Genera automaticamente condizioni di filtraggio dei metadati basate sulla variabile di query',
          },
          manual: {
            title: 'Manuale',
            subTitle: 'Aggiungere manualmente le condizioni di filtraggio dei metadati',
          },
        },
        panel: {
          conditions: 'Condizioni',
          select: 'Seleziona variabile...',
          title: 'Condizioni di filtro dei metadati',
          add: 'Aggiungi condizione',
          datePlaceholder: 'Scegli un orario...',
          placeholder: 'Inserisci valore',
          search: 'Cerca metadati',
        },
        title: 'Filtraggio dei metadati',
      },
    },
    http: {
      inputVars: 'Variabili di Input',
      api: 'API',
      apiPlaceholder: 'Inserisci URL, digita ‘/’ per inserire variabile',
      notStartWithHttp: 'L\'API deve iniziare con http:// o https://',
      key: 'Chiave',
      value: 'Valore',
      bulkEdit: 'Modifica di massa',
      keyValueEdit: 'Modifica Chiave-Valore',
      headers: 'Intestazioni',
      params: 'Parametri',
      body: 'Corpo',
      outputVars: {
        body: 'Contenuto Risposta',
        statusCode: 'Codice Stato Risposta',
        headers: 'Elenco Intestazioni Risposta JSON',
        files: 'Elenco File',
      },
      authorization: {
        'authorization': 'Autorizzazione',
        'authorizationType': 'Tipo di Autorizzazione',
        'no-auth': 'Nessuno',
        'api-key': 'API-Key',
        'auth-type': 'Tipo Auth',
        'basic': 'Basic',
        'bearer': 'Bearer',
        'custom': 'Custom',
        'api-key-title': 'API Key',
        'header': 'Intestazione',
      },
      insertVarPlaceholder: 'digita \'/\' per inserire variabile',
      timeout: {
        title: 'Timeout',
        connectLabel: 'Timeout Connessione',
        connectPlaceholder: 'Inserisci timeout connessione in secondi',
        readLabel: 'Timeout Lettura',
        readPlaceholder: 'Inserisci timeout lettura in secondi',
        writeLabel: 'Timeout Scrittura',
        writePlaceholder: 'Inserisci timeout scrittura in secondi',
      },
      binaryFileVariable: 'Variabile file binario',
      type: 'Digitare',
      extractListPlaceholder: 'Inserisci l\'indice delle voci dell\'elenco, digita \'/\' inserisci la variabile',
      curl: {
        placeholder: 'Incolla qui la stringa cURL',
        title: 'Importazione da cURL',
      },
      verifySSL: {
        title: 'Verifica il certificato SSL',
        warningTooltip: 'Disabilitare la verifica SSL non è raccomandato per gli ambienti di produzione. Questo dovrebbe essere utilizzato solo in sviluppo o test, poiché rende la connessione vulnerabile a minacce alla sicurezza come gli attacchi man-in-the-middle.',
      },
    },
    code: {
      inputVars: 'Variabili di Input',
      outputVars: 'Variabili di Output',
      advancedDependencies: 'Dipendenze Avanzate',
      advancedDependenciesTip:
        'Aggiungi alcune dipendenze precaricate che richiedono più tempo per essere consumate o che non sono predefinite qui',
      searchDependencies: 'Cerca Dipendenze',
    },
    templateTransform: {
      inputVars: 'Variabili di Input',
      code: 'Codice',
      codeSupportTip: 'Supporta solo Jinja2',
      outputVars: {
        output: 'Contenuto trasformato',
      },
    },
    ifElse: {
      if: 'Se',
      else: 'Altrimenti',
      elseDescription:
        'Utilizzato per definire la logica che dovrebbe essere eseguita quando la condizione se non è soddisfatta.',
      and: 'e',
      or: 'o',
      operator: 'Operatore',
      notSetVariable: 'Si prega di impostare prima la variabile',
      comparisonOperator: {
        'contains': 'contiene',
        'not contains': 'non contiene',
        'start with': 'inizia con',
        'end with': 'finisce con',
        'is': 'è',
        'is not': 'non è',
        'empty': 'è vuoto',
        'not empty': 'non è vuoto',
        'null': 'è nullo',
        'not null': 'non è nullo',
        'regex match': 'Corrispondenza regex',
        'in': 'in',
        'all of': 'tutto di',
        'not in': 'non in',
        'exists': 'Esiste',
        'not exists': 'non esiste',
        'after': 'dopo',
      },
      enterValue: 'Inserisci valore',
      addCondition: 'Aggiungi Condizione',
      conditionNotSetup: 'Condizione NON impostata',
      selectVariable: 'Seleziona variabile...',
      optionName: {
        url: 'URL',
        localUpload: 'Caricamento locale',
        image: 'Immagine',
        doc: 'Dottore',
        video: 'Video',
        audio: 'Audio',
      },
      addSubVariable: 'Variabile secondaria',
      select: 'Selezionare',
      condition: 'Condizione',
    },
    variableAssigner: {
      title: 'Assegna variabili',
      outputType: 'Tipo di Output',
      varNotSet: 'Variabile non impostata',
      noVarTip: 'Aggiungi le variabili da assegnare',
      type: {
        string: 'Stringa',
        number: 'Numero',
        object: 'Oggetto',
        array: 'Array',
      },
      aggregationGroup: 'Gruppo di Aggregazione',
      aggregationGroupTip:
        'Abilitando questa funzione, l\'aggregatore di variabili potrà aggregare più set di variabili.',
      addGroup: 'Aggiungi Gruppo',
      outputVars: {
        varDescribe: 'Output {{groupName}}',
      },
      setAssignVariable: 'Imposta variabile assegnata',
    },
    assigner: {
      'assignedVariable': 'Variabile Assegnata',
      'writeMode': 'Modalità di Scrittura',
      'writeModeTip': 'Quando la VARIABILE ASSEGNATA è un array, la modalità di aggiunta inserisce alla fine.',
      'over-write': 'Sovrascrivere',
      'append': 'Aggiungere',
      'plus': 'Più',
      'clear': 'Cancellare',
      'setVariable': 'Imposta Variabile',
      'variable': 'Variabile',
      'operations': {
        '-=': '-=',
        'overwrite': 'Sovrascrivere',
        '+=': '+=',
        '*=': '*=',
        'append': 'Aggiungere',
        'set': 'Mettere',
        'title': 'Operazione',
        '/=': '/=',
        'over-write': 'Sovrascrivere',
        'extend': 'Estendere',
        'clear': 'Chiaro',
        'remove-last': 'Rimuovi ultimo',
        'remove-first': 'Rimuovi primo',
      },
      'setParameter': 'Imposta parametro...',
      'variables': 'Variabili',
      'noAssignedVars': 'Nessuna variabile assegnata disponibile',
      'assignedVarsDescription': 'Le variabili assegnate devono essere variabili scrivibili, ad esempio variabili di conversazione.',
      'varNotSet': 'Variabile NON impostata',
      'selectAssignedVariable': 'Seleziona variabile assegnata...',
      'noVarTip': 'Fare clic sul pulsante "+" per aggiungere variabili',
    },
    tool: {
      inputVars: 'Variabili di Input',
      outputVars: {
        text: 'contenuto generato dallo strumento',
        files: {
          title: 'file generati dallo strumento',
          type: 'Tipo supportato. Attualmente supporta solo immagini',
          transfer_method:
            'Metodo di trasferimento. Il valore è remote_url o local_file',
          url: 'URL immagine',
          upload_file_id: 'ID file caricato',
        },
        json: 'json generato dallo strumento',
      },
      authorize: 'Autorizza',
    },
    questionClassifiers: {
      model: 'modello',
      inputVars: 'Variabili di Input',
      outputVars: {
        className: 'Nome Classe',
        usage: 'Informazioni sull\'utilizzo del modello',
      },
      class: 'Classe',
      classNamePlaceholder: 'Scrivi il nome della tua classe',
      advancedSetting: 'Impostazione Avanzata',
      topicName: 'Nome Argomento',
      topicPlaceholder: 'Scrivi il nome del tuo argomento',
      addClass: 'Aggiungi Classe',
      instruction: 'Istruzione',
      instructionTip:
        'Inserisci istruzioni aggiuntive per aiutare il classificatore di domande a capire meglio come categorizzare le domande.',
      instructionPlaceholder: 'Scrivi la tua istruzione',
    },
    parameterExtractor: {
      inputVar: 'Variabile di Input',
      outputVars: {
        isSuccess: 'È successo. In caso di successo il valore è 1, in caso di fallimento il valore è 0.',
        errorReason: 'Motivo dell\'errore',
        usage: 'Informazioni sull\'utilizzo del modello',
      },
      extractParameters: 'Estrai Parametri',
      importFromTool: 'Importa dagli strumenti',
      addExtractParameter: 'Aggiungi Parametro Estratto',
      addExtractParameterContent: {
        name: 'Nome',
        namePlaceholder: 'Nome Parametro Estratto',
        type: 'Tipo',
        typePlaceholder: 'Tipo Parametro Estratto',
        description: 'Descrizione',
        descriptionPlaceholder: 'Descrizione Parametro Estratto',
        required: 'Richiesto',
        requiredContent:
          'Richiesto viene utilizzato solo come riferimento per l\'inferenza del modello, e non per la convalida obbligatoria dell\'output del parametro.',
      },
      extractParametersNotSet: 'Parametri Estratti non impostati',
      instruction: 'Istruzione',
      instructionTip:
        'Inserisci istruzioni aggiuntive per aiutare l\'estrattore di parametri a capire come estrarre i parametri.',
      advancedSetting: 'Impostazione Avanzata',
      reasoningMode: 'Modalità di ragionamento',
      reasoningModeTip:
        'Puoi scegliere la modalità di ragionamento appropriata in base alla capacità del modello di rispondere alle istruzioni per la chiamata delle funzioni o i prompt.',
    },
    iteration: {
      deleteTitle: 'Eliminare Nodo Iterazione?',
      deleteDesc:
        'Eliminando il nodo iterazione verranno eliminati tutti i nodi figlio',
      input: 'Input',
      output: 'Variabili di Output',
      iteration_one: '{{count}} Iterazione',
      iteration_other: '{{count}} Iterazioni',
      currentIteration: 'Iterazione Corrente',
      ErrorMethod: {
        operationTerminated: 'Terminato',
        continueOnError: 'continua sull\'errore',
        removeAbnormalOutput: 'rimuovi-output-anomalo',
      },
      error_one: '{{conteggio}} Errore',
      parallelMode: 'Modalità parallela',
      MaxParallelismTitle: 'Parallelismo massimo',
      error_other: '{{conteggio}} Errori',
      parallelModeEnableDesc: 'In modalità parallela, le attività all\'interno delle iterazioni supportano l\'esecuzione parallela. È possibile configurare questa opzione nel pannello delle proprietà a destra.',
      MaxParallelismDesc: 'Il parallelismo massimo viene utilizzato per controllare il numero di attività eseguite contemporaneamente in una singola iterazione.',
      errorResponseMethod: 'Metodo di risposta all\'errore',
      parallelModeEnableTitle: 'Modalità parallela abilitata',
      parallelModeUpper: 'MODALITÀ PARALLELA',
      comma: ',',
      parallelPanelDesc: 'In modalità parallela, le attività nell\'iterazione supportano l\'esecuzione parallela.',
      answerNodeWarningDesc: 'Avviso in modalità parallela: i nodi di risposta, le assegnazioni di variabili di conversazione e le operazioni di lettura/scrittura persistenti all\'interno delle iterazioni possono causare eccezioni.',
    },
    note: {
      addNote: 'Aggiungi Nota',
      editor: {
        placeholder: 'Scrivi la tua nota...',
        small: 'Piccolo',
        medium: 'Medio',
        large: 'Grande',
        bold: 'Grassetto',
        italic: 'Corsivo',
        strikethrough: 'Barrato',
        link: 'Link',
        openLink: 'Apri',
        unlink: 'Rimuovi link',
        enterUrl: 'Inserisci URL...',
        invalidUrl: 'URL non valido',
        bulletList: 'Elenco puntato',
        showAuthor: 'Mostra Autore',
      },
    },
    docExtractor: {
      outputVars: {
        text: 'Testo estratto',
      },
      learnMore: 'Ulteriori informazioni',
      inputVar: 'Variabile di input',
      supportFileTypes: 'Tipi di file supportati: {{types}}.',
    },
    listFilter: {
      outputVars: {
        last_record: 'Ultimo record',
        result: 'Filtra risultato',
        first_record: 'Primo record',
      },
      asc: 'ASC',
      limit: 'Primi N',
      inputVar: 'Variabile di input',
      selectVariableKeyPlaceholder: 'Seleziona la chiave della variabile secondaria',
      filterConditionComparisonOperator: 'Operatore di confronto delle condizioni di filtro',
      filterCondition: 'Condizione del filtro',
      filterConditionKey: 'Chiave condizione filtro',
      desc: 'DESC',
      filterConditionComparisonValue: 'Valore della condizione di filtro',
      orderBy: 'Ordina per',
      extractsCondition: 'Estrai l\'elemento N',
    },
    agent: {
      strategy: {
        selectTip: 'Seleziona la strategia agentica',
        searchPlaceholder: 'Strategia agente di ricerca',
        label: 'Strategia agentica',
        configureTipDesc: 'Dopo aver configurato la strategia agentic, questo nodo caricherà automaticamente le configurazioni rimanenti. La strategia influenzerà il meccanismo del ragionamento con strumenti a più fasi.',
        tooltip: 'Diverse strategie agentiche determinano il modo in cui il sistema pianifica ed esegue le chiamate agli strumenti in più fasi',
        shortLabel: 'Strategia',
        configureTip: 'Configurare la strategia agentic.',
      },
      pluginInstaller: {
        installing: 'Installazione',
        install: 'Installare',
      },
      modelNotInMarketplace: {
        manageInPlugins: 'Gestisci nei plugin',
        desc: 'Questo modello viene installato dal repository locale o GitHub. Si prega di utilizzare dopo l\'installazione.',
        title: 'Modello non installato',
      },
      modelNotSupport: {
        descForVersionSwitch: 'La versione del plug-in installata non fornisce questo modello. Fare clic per cambiare versione.',
        title: 'Modello non supportato',
        desc: 'La versione del plug-in installata non fornisce questo modello.',
      },
      modelSelectorTooltips: {
        deprecated: 'Questo modello è deprecato',
      },
      outputVars: {
        files: {
          type: 'Tipo di supporto. Ora supporta solo l\'immagine',
          title: 'File generati dall\'agente',
          transfer_method: 'Metodo di trasferimento. Il valore è remote_url o local_file',
          url: 'URL immagine',
          upload_file_id: 'Carica l\'ID del file',
        },
        text: 'Contenuto generato dall\'agente',
        json: 'JSON generato dall\'agente',
      },
      checkList: {
        strategyNotSelected: 'Strategia non selezionata',
      },
      installPlugin: {
        cancel: 'Annulla',
        title: 'Installa il plugin',
        install: 'Installare',
        changelog: 'Registro delle modifiche',
        desc: 'Sto per installare il seguente plugin',
      },
      toolNotInstallTooltip: '{{tool}} non è installato',
      modelNotSelected: 'Modello non selezionato',
      modelNotInstallTooltip: 'Questo modello non è installato',
      notAuthorized: 'Non autorizzato',
      learnMore: 'Ulteriori informazioni',
      pluginNotInstalledDesc: 'Questo plugin viene installato da GitHub. Vai su Plugin per reinstallare',
      model: 'modello',
      configureModel: 'Configura modello',
      linkToPlugin: 'Collegamento ai plug-in',
      tools: 'Utensileria',
      unsupportedStrategy: 'Strategia non supportata',
      toolNotAuthorizedTooltip: '{{strumento}} Non autorizzato',
      strategyNotSet: 'Strategia agentica non impostata',
      toolbox: 'cassetta degli attrezzi',
      maxIterations: 'Numero massimo di iterazioni',
      strategyNotInstallTooltip: '{{strategy}} non è installato',
      strategyNotFoundDesc: 'La versione del plugin installata non fornisce questa strategia.',
      strategyNotFoundDescAndSwitchVersion: 'La versione del plugin installata non fornisce questa strategia. Fare clic per cambiare versione.',
      pluginNotInstalled: 'Questo plugin non è installato',
      pluginNotFoundDesc: 'Questo plugin viene installato da GitHub. Vai su Plugin per reinstallare',
    },
    loop: {
      ErrorMethod: {
        operationTerminated: 'Terminato',
        removeAbnormalOutput: 'Rimuovi l\'output anormale',
        continueOnError: 'Continua con l\'errore',
      },
      currentLoop: 'Anello Corrente',
      breakConditionTip: 'Solo le variabili all\'interno dei cicli con condizioni di terminazione e le variabili di conversazione possono essere riferite.',
      loopVariables: 'Variabili di ciclo',
      inputMode: 'Modalità di input',
      errorResponseMethod: 'Metodo di risposta all\'errore',
      error_one: '{{count}} Errore',
      loop_one: '{{count}} Ciclo',
      loopMaxCount: 'Conteggio massimo dei loop',
      breakCondition: 'Condizione di terminazione del ciclo',
      comma: ',',
      loopNode: 'Nodo Ciclico',
      finalLoopVariables: 'Variabili del ciclo finale',
      loopMaxCountError: 'Si prega di inserire un conteggio massimo di cicli valido, compreso tra 1 e {{maxCount}}',
      currentLoopCount: 'Conteggio attuale del ciclo: {{count}}',
      input: 'Input',
      setLoopVariables: 'Imposta le variabili all\'interno dell\'ambito del ciclo',
      deleteTitle: 'Elimina nodo ciclo?',
      output: 'Variabile di Output',
      initialLoopVariables: 'Variabili di loop iniziali',
      deleteDesc: 'Cancellare il nodo di ciclo rimuoverà tutti i nodi figli',
      loop_other: '{{count}} anelli',
      variableName: 'Nome Variabile',
      totalLoopCount: 'Conteggio totale dei cicli: {{count}}',
      exitConditionTip: 'Un nodo di ciclo ha bisogno di almeno una condizione di uscita.',
      error_other: '{{count}} Errori',
    },
  },
  tracing: {
    stopBy: 'Interrotto da {{user}}',
  },
  variableReference: {
    noAvailableVars: 'Nessuna variabile disponibile',
    noAssignedVars: 'Nessuna variabile assegnata disponibile',
    noVarsForOperation: 'Non ci sono variabili disponibili per l\'assegnazione con l\'operazione selezionata.',
    assignedVarsDescription: 'Le variabili assegnate devono essere variabili scrivibili, ad esempio',
    conversationVars: 'Variabili di conversazione',
  },
  versionHistory: {
    filter: {
      reset: 'Ripristina filtro',
      all: 'Tutto',
      onlyYours: 'Solo tuo',
      empty: 'Nessuna cronologia delle versioni corrispondente trovata',
      onlyShowNamedVersions: 'Mostra solo le versioni con nome',
    },
    editField: {
      titleLengthLimit: 'Il titolo non può superare {{limit}} caratteri',
      releaseNotes: 'Note di rilascio',
      title: 'Titolo',
      releaseNotesLengthLimit: 'Le note di rilascio non possono superare i {{limit}} caratteri',
    },
    action: {
      restoreSuccess: 'Versione ripristinata',
      restoreFailure: 'Impossibile ripristinare la versione',
      deleteSuccess: 'Versione eliminata',
      updateSuccess: 'Versione aggiornata',
      deleteFailure: 'Impossibile eliminare la versione',
      updateFailure: 'Impossibile aggiornare la versione',
    },
    latest: 'Ultimo',
    defaultName: 'Versione senza titolo',
    deletionTip: 'La cancellazione è irreversibile, si prega di confermare.',
    nameThisVersion: 'Chiamare questa versione',
    editVersionInfo: 'Modifica le informazioni sulla versione',
    releaseNotesPlaceholder: 'Descrivi cosa è cambiato',
    currentDraft: 'Bozza attuale',
    restorationTip: 'Dopo il ripristino della versione, la bozza attuale verrà sovrascritta.',
    title: 'Versioni',
  },
  debug: {
    noData: {
      runThisNode: 'Esegui questo nodo',
      description: 'I risultati dell\'ultima esecuzione verranno visualizzati qui',
    },
    variableInspect: {
      trigger: {
        cached: 'Visualizza le variabili memorizzate nella cache',
        clear: 'Chiaro',
        running: 'Caching stato di esecuzione',
        normal: 'Ispezione Variabile',
        stop: 'Ferma la corsa',
      },
      chatNode: 'Conversazione',
      clearNode: 'Svuota la variabile cached',
      envNode: 'Ambiente',
      systemNode: 'Sistema',
      title: 'Ispezione delle variabili',
      edited: 'Modificato',
      emptyLink: 'Scopri di più',
      resetConversationVar: 'Reimposta la variabile della conversazione al valore predefinito',
      view: 'Visualizza log',
      clearAll: 'Ripristina tutto',
      reset: 'Ripristina il valore dell\'ultima esecuzione',
      emptyTip: 'Dopo aver eseguito un nodo sulla tela o eseguendo un nodo passo dopo passo, puoi visualizzare il valore attuale della variabile nodo in Ispeziona Variabile.',
    },
    settingsTab: 'Impostazioni',
    lastRunTab: 'Ultima corsa',
  },
}

export default translation
