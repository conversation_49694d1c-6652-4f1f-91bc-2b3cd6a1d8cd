import json
import uuid
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional

import pytz
from flask import jsonify
from flask_login import current_user
from flask_restful import Resource, reqparse
from sqlalchemy import func, text
from sqlalchemy.orm import Session

from controllers.console import api
from controllers.console.app.wraps import get_app_model
from controllers.console.wraps import account_initialization_required, setup_required
from extensions.ext_database import db
from fields.agent_record_fields import agent_record_fields
from libs.helper import DatetimeString
from libs.login import login_required
from models import Account, App, Conversation, Message, Workflow, WorkflowRun
from models.model import AppMode


class AgentRecordApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    @get_app_model(mode=[AppMode.AGENT_CHAT, AppMode.CHAT, AppMode.ADVANCED_CHAT])
    def get(self, app_model):
        """
        获取智能体记录信息
        包括：创建信息、发布信息、运行信息、统计信息、调用记录
        """
        parser = reqparse.RequestParser()
        parser.add_argument("start", type=DatetimeString("%Y-%m-%d %H:%M"), location="args")
        parser.add_argument("end", type=DatetimeString("%Y-%m-%d %H:%M"), location="args")
        parser.add_argument("limit", type=int, default=50, location="args")
        args = parser.parse_args()

        # 获取时间范围
        timezone = pytz.timezone(current_user.timezone)
        utc_timezone = pytz.utc
        
        start_datetime = None
        end_datetime = None
        
        if args["start"]:
            start_datetime = datetime.strptime(args["start"], "%Y-%m-%d %H:%M")
            start_datetime = start_datetime.replace(second=0)
            start_datetime_timezone = timezone.localize(start_datetime)
            start_datetime = start_datetime_timezone.astimezone(utc_timezone)

        if args["end"]:
            end_datetime = datetime.strptime(args["end"], "%Y-%m-%d %H:%M")
            end_datetime = end_datetime.replace(second=0)
            end_datetime_timezone = timezone.localize(end_datetime)
            end_datetime = end_datetime_timezone.astimezone(utc_timezone)

        # 1. 获取创建信息
        creation_info = self._get_creation_info(app_model)
        
        # 2. 获取发布信息
        publish_info = self._get_publish_info(app_model)
        
        # 3. 获取运行信息
        runtime_info = self._get_runtime_info(app_model, start_datetime, end_datetime)
        
        # 4. 获取统计信息
        statistics = self._get_statistics(app_model, start_datetime, end_datetime)
        
        # 5. 获取调用记录
        call_records = self._get_call_records(app_model, start_datetime, end_datetime, args["limit"])

        result = {
            "creation_info": creation_info,
            "publish_info": publish_info,
            "runtime_info": runtime_info,
            "statistics": statistics,
            "call_records": call_records,
        }

        return jsonify({"data": result})

    def _get_creation_info(self, app_model: App) -> Dict:
        """获取创建信息"""
        creator = db.session.query(Account).filter(Account.id == app_model.created_by).first()
        
        return {
            "created_at": app_model.created_at,
            "created_by": app_model.created_by,
            "creator_name": creator.name if creator else "Unknown",
            "initial_config": {
                "mode": app_model.mode,
                "description": app_model.description,
                "enable_site": app_model.enable_site,
                "enable_api": app_model.enable_api,
            }
        }

    def _get_publish_info(self, app_model: App) -> Dict:
        """获取发布信息"""
        # 获取最新的工作流版本
        latest_workflow = db.session.query(Workflow).filter(
            Workflow.app_id == app_model.id,
            Workflow.version != Workflow.VERSION_DRAFT
        ).order_by(Workflow.updated_at.desc()).first()
        
        if not latest_workflow:
            return {
                "last_published_at": None,
                "published_by": None,
                "publisher_name": None,
                "version": None,
                "workflow_id": None,
            }
        
        publisher = db.session.query(Account).filter(Account.id == latest_workflow.updated_by).first()
        
        return {
            "last_published_at": latest_workflow.updated_at,
            "published_by": latest_workflow.updated_by,
            "publisher_name": publisher.name if publisher else "Unknown",
            "version": latest_workflow.version,
            "workflow_id": latest_workflow.id,
        }

    def _get_runtime_info(self, app_model: App, start_datetime: Optional[datetime], end_datetime: Optional[datetime]) -> Dict:
        """获取运行信息"""
        # 构建查询条件
        where_conditions = ["app_id = :app_id"]
        params = {"app_id": app_model.id}
        
        if start_datetime:
            where_conditions.append("created_at >= :start_datetime")
            params["start_datetime"] = start_datetime
            
        if end_datetime:
            where_conditions.append("created_at < :end_datetime")
            params["end_datetime"] = end_datetime

        where_clause = " AND ".join(where_conditions)
        
        # 查询工作流运行统计
        sql_query = f"""
        SELECT 
            COUNT(*) as total_runs,
            COUNT(CASE WHEN status = 'succeeded' THEN 1 END) as successful_runs,
            COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_runs,
            AVG(elapsed_time) as average_response_time,
            COALESCE(SUM(total_tokens), 0) as total_tokens_used,
            COALESCE(SUM(total_price), 0) as total_cost
        FROM workflow_runs 
        WHERE {where_clause}
        """
        
        with db.engine.begin() as conn:
            result = conn.execute(text(sql_query), params).first()
            
        return {
            "total_runs": result.total_runs or 0,
            "successful_runs": result.successful_runs or 0,
            "failed_runs": result.failed_runs or 0,
            "average_response_time": float(result.average_response_time) if result.average_response_time else 0.0,
            "total_tokens_used": result.total_tokens_used or 0,
            "total_cost": float(result.total_cost) if result.total_cost else 0.0,
            "currency": "USD",  # 默认货币
        }

    def _get_statistics(self, app_model: App, start_datetime: Optional[datetime], end_datetime: Optional[datetime]) -> Dict:
        """获取统计信息"""
        # 构建查询条件
        where_conditions = ["app_id = :app_id"]
        params = {"app_id": app_model.id, "tz": current_user.timezone}
        
        if start_datetime:
            where_conditions.append("created_at >= :start_datetime")
            params["start_datetime"] = start_datetime
            
        if end_datetime:
            where_conditions.append("created_at < :end_datetime")
            params["end_datetime"] = end_datetime

        where_clause = " AND ".join(where_conditions)
        
        # 查询对话和消息统计
        sql_query = f"""
        SELECT 
            COUNT(DISTINCT conversation_id) as total_conversations,
            COUNT(*) as total_messages
        FROM messages 
        WHERE {where_clause}
        """
        
        with db.engine.begin() as conn:
            result = conn.execute(text(sql_query), params).first()
        
        # 查询用户满意度
        satisfaction_sql = f"""
        SELECT 
            COUNT(*) as total_feedback,
            COUNT(CASE WHEN rating = 'like' THEN 1 END) as positive_feedback
        FROM message_feedbacks 
        WHERE {where_clause}
        """
        
        with db.engine.begin() as conn:
            satisfaction_result = conn.execute(text(satisfaction_sql), params).first()
        
        satisfaction_rate = 0.0
        if satisfaction_result.total_feedback and satisfaction_result.total_feedback > 0:
            satisfaction_rate = satisfaction_result.positive_feedback / satisfaction_result.total_feedback
        
        # 查询每日使用量
        daily_usage_sql = f"""
        SELECT 
            DATE(DATE_TRUNC('day', created_at AT TIME ZONE 'UTC' AT TIME ZONE :tz)) AS date,
            COUNT(*) AS message_count
        FROM messages 
        WHERE {where_clause}
        GROUP BY date 
        ORDER BY date DESC 
        LIMIT 30
        """
        
        daily_usage = []
        with db.engine.begin() as conn:
            daily_results = conn.execute(text(daily_usage_sql), params)
            for row in daily_results:
                daily_usage.append({
                    "date": str(row.date),
                    "message_count": row.message_count
                })
        
        return {
            "total_conversations": result.total_conversations or 0,
            "total_messages": result.total_messages or 0,
            "user_satisfaction_rate": satisfaction_rate,
            "daily_usage": daily_usage,
            "monthly_usage": [],  # 可以后续扩展
        }

    def _get_call_records(self, app_model: App, start_datetime: Optional[datetime], end_datetime: Optional[datetime], limit: int) -> List[Dict]:
        """获取调用记录"""
        # 构建查询条件
        where_conditions = ["app_id = :app_id"]
        params = {"app_id": app_model.id}
        
        if start_datetime:
            where_conditions.append("created_at >= :start_datetime")
            params["start_datetime"] = start_datetime
            
        if end_datetime:
            where_conditions.append("created_at < :end_datetime")
            params["end_datetime"] = end_datetime

        where_clause = " AND ".join(where_conditions)
        
        # 查询最近的调用记录
        sql_query = f"""
        SELECT 
            m.id,
            m.created_at,
            m.from_end_user_id,
            m.from_account_id,
            m.query,
            m.answer,
            m.status,
            m.provider_response_latency,
            m.message_tokens + m.answer_tokens as tokens_used,
            m.conversation_id,
            m.id as message_id
        FROM messages m
        WHERE {where_clause}
        ORDER BY m.created_at DESC
        LIMIT :limit
        """
        
        params["limit"] = limit
        
        call_records = []
        with db.engine.begin() as conn:
            results = conn.execute(text(sql_query), params)
            for row in results:
                # 获取用户信息
                user_name = "Unknown"
                if row.from_account_id:
                    account = db.session.query(Account).filter(Account.id == row.from_account_id).first()
                    if account:
                        user_name = account.name
                elif row.from_end_user_id:
                    user_name = f"End User ({row.from_end_user_id[:8]})"
                
                call_records.append({
                    "id": row.id,
                    "timestamp": row.created_at,
                    "user_id": row.from_account_id or row.from_end_user_id,
                    "user_name": user_name,
                    "input": row.query,
                    "output": row.answer,
                    "status": row.status,
                    "response_time": float(row.provider_response_latency) if row.provider_response_latency else 0.0,
                    "tokens_used": row.tokens_used or 0,
                    "conversation_id": row.conversation_id,
                    "message_id": row.message_id,
                })
        
        return call_records


class AgentRecordBatchApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def post(self):
        """
        批量获取多个应用的智能体记录信息
        支持传入多个app_id进行批量查询
        """
        parser = reqparse.RequestParser()
        parser.add_argument("app_ids", type=list, required=True, location="json", 
                          help="应用ID列表，例如: [app_id1, app_id2, app_id3]")
        parser.add_argument("start", type=DatetimeString("%Y-%m-%d %H:%M"), location="json")
        parser.add_argument("end", type=DatetimeString("%Y-%m-%d %H:%M"), location="json")
        parser.add_argument("limit", type=int, default=50, location="json")
        parser.add_argument("total_limit", type=int, location="json")
        args = parser.parse_args()

        # 验证app_ids参数
        try:
            app_ids = args["app_ids"]
            if not app_ids or not isinstance(app_ids, list):
                return {"error": "app_ids参数必须是非空列表"}, 400
            
            # 验证每个app_id是否为有效的UUID格式
            for app_id in app_ids:
                if not isinstance(app_id, str):
                    return {"error": f"app_id必须是字符串类型: {app_id}"}, 400
                try:
                    uuid.UUID(app_id)
                except ValueError:
                    return {"error": f"无效的app_id格式: {app_id}"}, 400
                    
        except Exception as e:
            return {"error": f"app_ids参数格式错误: {str(e)}"}, 400

        # 验证用户是否有权限访问这些应用
        apps = db.session.query(App).filter(
            App.id.in_(app_ids),
            App.tenant_id == current_user.current_tenant_id
        ).all()
        
        if len(apps) != len(app_ids):
            return {"error": "部分应用不存在或无权限访问"}, 403

        # 获取时间范围
        timezone = pytz.timezone(current_user.timezone)
        utc_timezone = pytz.utc
        
        start_datetime = None
        end_datetime = None
        
        if args["start"]:
            start_datetime = datetime.strptime(args["start"], "%Y-%m-%d %H:%M")
            start_datetime = start_datetime.replace(second=0)
            start_datetime_timezone = timezone.localize(start_datetime)
            start_datetime = start_datetime_timezone.astimezone(utc_timezone)

        if args["end"]:
            end_datetime = datetime.strptime(args["end"], "%Y-%m-%d %H:%M")
            end_datetime = end_datetime.replace(second=0)
            end_datetime_timezone = timezone.localize(end_datetime)
            end_datetime = end_datetime_timezone.astimezone(utc_timezone)

        # 批量获取每个应用的数据
        results = {}
        total_records_requested = len(apps) * args["limit"]
        total_records_returned = 0
        apps_with_records = 0
        
        for app in apps:
            try:
                # 1. 获取创建信息
                creation_info = self._get_creation_info(app)
                
                # 2. 获取发布信息
                publish_info = self._get_publish_info(app)
                
                # 3. 获取运行信息
                runtime_info = self._get_runtime_info(app, start_datetime, end_datetime)
                
                # 4. 获取统计信息
                statistics = self._get_statistics(app, start_datetime, end_datetime)
                
                # 5. 获取调用记录
                call_records = self._get_call_records(app, start_datetime, end_datetime, args["limit"])
                
                # 应用total_limit限制
                if args["total_limit"] is not None:
                    remaining_limit = args["total_limit"] - total_records_returned
                    if remaining_limit <= 0:
                        # 已达到总限制，停止添加更多记录
                        break
                    elif len(call_records) > remaining_limit:
                        # 截取记录以满足总限制
                        call_records = call_records[:remaining_limit]
                
                total_records_returned += len(call_records)
                if len(call_records) > 0:
                    apps_with_records += 1

                results[str(app.id)] = {
                    "app_id": str(app.id),
                    "app_name": app.name,
                    "app_mode": app.mode,
                    "creation_info": creation_info,
                    "publish_info": publish_info,
                    "runtime_info": runtime_info,
                    "statistics": statistics,
                    "call_records": call_records,
                }
            except Exception as e:
                # 如果某个应用处理失败，记录错误但继续处理其他应用
                results[str(app.id)] = {
                    "app_id": str(app.id),
                    "app_name": app.name,
                    "error": f"处理失败: {str(e)}"
                }

        # 构建元信息
        meta = {
            "total_apps_requested": len(apps),
            "total_apps_returned": len(results),
            "total_records_requested": total_records_requested,
            "total_records_returned": total_records_returned,
            "limit_per_app": args["limit"],
        }
        
        if args["total_limit"] is not None:
            meta["total_limit"] = args["total_limit"]

        return jsonify({"data": results, "meta": meta})

    def _get_creation_info(self, app_model: App) -> Dict:
        """获取创建信息"""
        creator = db.session.query(Account).filter(Account.id == app_model.created_by).first()
        
        return {
            "created_at": app_model.created_at,
            "created_by": app_model.created_by,
            "creator_name": creator.name if creator else "Unknown",
            "initial_config": {
                "mode": app_model.mode,
                "description": app_model.description,
                "enable_site": app_model.enable_site,
                "enable_api": app_model.enable_api,
            }
        }

    def _get_publish_info(self, app_model: App) -> Dict:
        """获取发布信息"""
        # 获取最新的工作流版本
        latest_workflow = db.session.query(Workflow).filter(
            Workflow.app_id == app_model.id,
            Workflow.version != Workflow.VERSION_DRAFT
        ).order_by(Workflow.updated_at.desc()).first()
        
        if not latest_workflow:
            return {
                "last_published_at": None,
                "published_by": None,
                "publisher_name": None,
                "version": None,
                "workflow_id": None,
            }
        
        publisher = db.session.query(Account).filter(Account.id == latest_workflow.updated_by).first()
        
        return {
            "last_published_at": latest_workflow.updated_at,
            "published_by": latest_workflow.updated_by,
            "publisher_name": publisher.name if publisher else "Unknown",
            "version": latest_workflow.version,
            "workflow_id": latest_workflow.id,
        }

    def _get_runtime_info(self, app_model: App, start_datetime: Optional[datetime], end_datetime: Optional[datetime]) -> Dict:
        """获取运行信息"""
        # 构建查询条件
        where_conditions = ["app_id = :app_id"]
        params = {"app_id": app_model.id}
        
        if start_datetime:
            where_conditions.append("created_at >= :start_datetime")
            params["start_datetime"] = start_datetime
            
        if end_datetime:
            where_conditions.append("created_at < :end_datetime")
            params["end_datetime"] = end_datetime

        where_clause = " AND ".join(where_conditions)
        
        # 查询工作流运行统计
        sql_query = f"""
        SELECT 
            COUNT(*) as total_runs,
            COUNT(CASE WHEN status = 'succeeded' THEN 1 END) as successful_runs,
            COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_runs,
            AVG(elapsed_time) as average_response_time,
            COALESCE(SUM(total_tokens), 0) as total_tokens_used,
            COALESCE(SUM(total_price), 0) as total_cost
        FROM workflow_runs 
        WHERE {where_clause}
        """
        
        with db.engine.begin() as conn:
            result = conn.execute(text(sql_query), params).first()
            
        return {
            "total_runs": result.total_runs or 0,
            "successful_runs": result.successful_runs or 0,
            "failed_runs": result.failed_runs or 0,
            "average_response_time": float(result.average_response_time) if result.average_response_time else 0.0,
            "total_tokens_used": result.total_tokens_used or 0,
            "total_cost": float(result.total_cost) if result.total_cost else 0.0,
            "currency": "USD",  # 默认货币
        }

    def _get_statistics(self, app_model: App, start_datetime: Optional[datetime], end_datetime: Optional[datetime]) -> Dict:
        """获取统计信息"""
        # 构建查询条件
        where_conditions = ["app_id = :app_id"]
        params = {"app_id": app_model.id, "tz": current_user.timezone}
        
        if start_datetime:
            where_conditions.append("created_at >= :start_datetime")
            params["start_datetime"] = start_datetime
            
        if end_datetime:
            where_conditions.append("created_at < :end_datetime")
            params["end_datetime"] = end_datetime

        where_clause = " AND ".join(where_conditions)
        
        # 查询对话和消息统计
        sql_query = f"""
        SELECT 
            COUNT(DISTINCT conversation_id) as total_conversations,
            COUNT(*) as total_messages
        FROM messages 
        WHERE {where_clause}
        """
        
        with db.engine.begin() as conn:
            result = conn.execute(text(sql_query), params).first()
        
        # 查询用户满意度
        satisfaction_sql = f"""
        SELECT 
            COUNT(*) as total_feedback,
            COUNT(CASE WHEN rating = 'like' THEN 1 END) as positive_feedback
        FROM message_feedbacks 
        WHERE {where_clause}
        """
        
        with db.engine.begin() as conn:
            satisfaction_result = conn.execute(text(satisfaction_sql), params).first()
        
        satisfaction_rate = 0.0
        if satisfaction_result.total_feedback and satisfaction_result.total_feedback > 0:
            satisfaction_rate = satisfaction_result.positive_feedback / satisfaction_result.total_feedback
        
        # 查询每日使用量
        daily_usage_sql = f"""
        SELECT 
            DATE(DATE_TRUNC('day', created_at AT TIME ZONE 'UTC' AT TIME ZONE :tz)) AS date,
            COUNT(*) AS message_count
        FROM messages 
        WHERE {where_clause}
        GROUP BY date 
        ORDER BY date DESC 
        LIMIT 30
        """
        
        daily_usage = []
        with db.engine.begin() as conn:
            daily_results = conn.execute(text(daily_usage_sql), params)
            for row in daily_results:
                daily_usage.append({
                    "date": str(row.date),
                    "message_count": row.message_count
                })
        
        return {
            "total_conversations": result.total_conversations or 0,
            "total_messages": result.total_messages or 0,
            "user_satisfaction_rate": satisfaction_rate,
            "daily_usage": daily_usage,
            "monthly_usage": [],  # 可以后续扩展
        }

    def _get_call_records(self, app_model: App, start_datetime: Optional[datetime], end_datetime: Optional[datetime], limit: int) -> List[Dict]:
        """获取调用记录"""
        # 构建查询条件
        where_conditions = ["app_id = :app_id"]
        params = {"app_id": app_model.id}
        
        if start_datetime:
            where_conditions.append("created_at >= :start_datetime")
            params["start_datetime"] = start_datetime
            
        if end_datetime:
            where_conditions.append("created_at < :end_datetime")
            params["end_datetime"] = end_datetime

        where_clause = " AND ".join(where_conditions)
        
        # 查询最近的调用记录
        sql_query = f"""
        SELECT 
            m.id,
            m.created_at,
            m.from_end_user_id,
            m.from_account_id,
            m.query,
            m.answer,
            m.status,
            m.provider_response_latency,
            m.message_tokens + m.answer_tokens as tokens_used,
            m.conversation_id,
            m.id as message_id
        FROM messages m
        WHERE {where_clause}
        ORDER BY m.created_at DESC
        LIMIT :limit
        """
        
        params["limit"] = limit
        
        call_records = []
        with db.engine.begin() as conn:
            results = conn.execute(text(sql_query), params)
            for row in results:
                # 获取用户信息
                user_name = "Unknown"
                if row.from_account_id:
                    account = db.session.query(Account).filter(Account.id == row.from_account_id).first()
                    if account:
                        user_name = account.name
                elif row.from_end_user_id:
                    user_name = f"End User ({row.from_end_user_id[:8]})"
                
                call_records.append({
                    "id": row.id,
                    "timestamp": row.created_at,
                    "user_id": row.from_account_id or row.from_end_user_id,
                    "user_name": user_name,
                    "input": row.query,
                    "output": row.answer,
                    "status": row.status,
                    "response_time": float(row.provider_response_latency) if row.provider_response_latency else 0.0,
                    "tokens_used": row.tokens_used or 0,
                    "conversation_id": row.conversation_id,
                    "message_id": row.message_id,
                })
        
        return call_records


# 注册API路由
api.add_resource(AgentRecordApi, "/apps/<uuid:app_id>/agent-records")
api.add_resource(AgentRecordBatchApi, "/console/api/agent-records/batch") 