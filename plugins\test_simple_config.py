#!/usr/bin/env python3
"""
简单测试脚本：验证本地插件配置是否能正确从.env文件读取
"""

import os
import sys

# 动态添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..')

# 检查当前工作目录
cwd = os.getcwd()
cwd_basename = os.path.basename(cwd)

# 根据运行环境添加路径
if cwd_basename == 'api':
    # 在 api 目录下运行
    api_parent = os.path.dirname(cwd)
    if api_parent not in sys.path:
        sys.path.insert(0, api_parent)
    # 同时添加 api 目录
    if cwd not in sys.path:
        sys.path.insert(0, cwd)
else:
    # 在其他目录运行，添加项目根目录
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    # 同时添加 api 目录
    api_dir = os.path.join(project_root, 'api')
    if api_dir not in sys.path:
        sys.path.insert(0, api_dir)

def test_config_reading():
    """测试配置读取"""
    print("=== 测试本地插件配置读取 ===")
    
    try:
        # 尝试导入配置
        from api.configs import dify_config
        print("✓ 成功导入 dify_config")

        # 测试配置项
        print(f"✓ LOCAL_PLUGIN_AUTO_INSTALL_ENABLED: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_ENABLED}")
        print(f"✓ LOCAL_PLUGIN_AUTO_INSTALL_DIR: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_DIR}")
        print(f"✓ LOCAL_PLUGIN_AUTO_INSTALL_VERIFY_SIGNATURE: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_VERIFY_SIGNATURE}")
        print(f"✓ LOCAL_PLUGIN_AUTO_INSTALL_ON_STARTUP: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_ON_STARTUP}")
        print(f"✓ LOCAL_PLUGIN_AUTO_INSTALL_FOR_NEW_USERS: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_FOR_NEW_USERS}")
        print(f"✓ LOCAL_PLUGIN_AUTO_INSTALL_LOG_LEVEL: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_LOG_LEVEL}")
        print(f"✓ LOCAL_PLUGIN_AUTO_INSTALL_MAX_RETRIES: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_MAX_RETRIES}")
        print(f"✓ LOCAL_PLUGIN_AUTO_INSTALL_EXCLUDE_PLUGINS: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_EXCLUDE_PLUGINS}")
        print(f"✓ LOCAL_PLUGIN_AUTO_INSTALL_INCLUDE_PLUGINS: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_INCLUDE_PLUGINS}")
        print(f"✓ LOCAL_PLUGIN_AUTO_INSTALL_FORCE_REINSTALL: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_FORCE_REINSTALL}")

        # 测试计算字段
        print(f"✓ LOCAL_PLUGIN_AUTO_INSTALL_EXCLUDE_PLUGINS_LIST: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_EXCLUDE_PLUGINS_LIST}")
        print(f"✓ LOCAL_PLUGIN_AUTO_INSTALL_INCLUDE_PLUGINS_LIST: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_INCLUDE_PLUGINS_LIST}")

        print("\n✅ 配置读取测试通过！")
        return True

    except Exception as e:
        print(f"❌ 配置读取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_plugin_manager():
    """测试插件管理器"""
    print("\n=== 测试插件管理器 ===")
    
    try:
        from plugins.local_plugin_manager import get_local_plugin_manager
        
        manager = get_local_plugin_manager()
        print("✓ 成功创建插件管理器实例")
        print(f"✓ 插件目录: {manager.plugins_dir}")
        
        # 测试扫描插件文件（不依赖数据库）
        plugin_files = manager.scan_plugin_files()
        print(f"✓ 扫描到 {len(plugin_files)} 个插件文件")
        
        print("\n✅ 插件管理器测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 插件管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("本地插件配置简单测试")
    print("=" * 40)
    
    # 测试配置读取
    if not test_config_reading():
        return
    
    # 测试插件管理器
    if not test_plugin_manager():
        return
    
    print("\n" + "=" * 40)
    print("🎉 所有测试通过！配置系统正常工作。")

if __name__ == "__main__":
    main() 