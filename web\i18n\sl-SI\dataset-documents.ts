const translation = {
  list: {
    title: 'Do<PERSON><PERSON><PERSON>',
    desc: 'V<PERSON> datoteke znanja so prikazane tukaj, celo<PERSON>no znanje pa je mogoče povezati s citati Dify ali indeksirati prek vtičnika Chat.',
    addFile: '<PERSON><PERSON><PERSON> datoteko',
    addPages: '<PERSON><PERSON><PERSON> strani',
    addUrl: 'Dodaj URL',
    table: {
      header: {
        fileName: 'IME DATOTEKE',
        words: 'BESEDE',
        hitCount: 'ŠTEVILO PRIDOBITEV',
        uploadTime: 'ČAS NALAGANJA',
        status: 'STATUS',
        action: 'DEJANJE',
        chunkingMode: 'NAČIN KOŠČENJA',
      },
      rename: 'Preimenu<PERSON>',
      name: 'I<PERSON>',
    },
    action: {
      uploadFile: 'Naloži novo datoteko',
      settings: 'Nastavitve segmenta',
      addButton: 'Dodaj del',
      add: 'Dodaj del',
      batchAdd: '<PERSON><PERSON>j<PERSON> dodajanje',
      archive: 'Arhivir<PERSON>',
      unarchive: '<PERSON><PERSON><PERSON><PERSON><PERSON> arhiviranje',
      delete: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      enableWarning: 'Arhi<PERSON><PERSON> datoteke ni mogoče omogočiti',
      sync: 'Sinhroniziraj',
      pause: 'Zaustavi',
      resume: 'Nadaljuj',
    },
    index: {
      enable: 'Omogoči',
      disable: 'Onemogoči',
      all: 'Vse',
      enableTip: 'Datoteka je lahko indeksirana',
      disableTip: 'Datoteka ne more biti indeksirana',
    },
    status: {
      queuing: 'V čakalni vrsti',
      indexing: 'Indeksiranje',
      paused: 'Zaustavljeno',
      error: 'Napaka',
      available: 'Na voljo',
      enabled: 'Omogočeno',
      disabled: 'Onemogočeno',
      archived: 'Arhivirano',
    },
    empty: {
      title: 'Dokumentacije še ni',
      upload: {
        tip: 'Lahko naložite datoteke, sinhronizirate z spletno stranjo ali aplikacijami, kot so Notion, GitHub itd.',
      },
      sync: {
        tip: 'Dify bo občasno prenesel datoteke iz Notion in dokončal obdelavo.',
      },
    },
    delete: {
      title: 'Ali ste prepričani, da želite izbrisati?',
      content: 'Če boste nadaljevali obdelavo kasneje, boste nadaljevali tam, kjer ste končali.',
    },
    batchModal: {
      title: 'Serijsko dodajanje delov',
      csvUploadTitle: 'Povlecite in spustite svojo CSV datoteko tukaj ali ',
      browse: 'brskajte',
      tip: 'CSV datoteka mora ustrezati naslednji strukturi:',
      question: 'vprašanje',
      answer: 'odgovor',
      contentTitle: 'vsebina dela',
      content: 'vsebina',
      template: 'Prenesite predlogo tukaj',
      cancel: 'Prekliči',
      run: 'Zaženi serijo',
      runError: 'Serijsko dodajanje ni uspelo',
      processing: 'V obdelavi serije',
      completed: 'Uvoz zaključen',
      error: 'Napaka pri uvozu',
      ok: 'V redu',
    },
    learnMore: 'Izvedi več',
  },
  metadata: {
    title: 'Metapodatki',
    desc: 'Označevanje metapodatkov za dokumente omogoča, da AI pravočasno dostopa do njih in prikaže vir referenc uporabnikom.',
    dateTimeFormat: 'MMMM D, YYYY hh:mm A',
    docTypeSelectTitle: 'Izberite vrsto dokumenta',
    docTypeChangeTitle: 'Spremeni vrsto dokumenta',
    docTypeSelectWarning:
      'Če se vrsta dokumenta spremeni, trenutni vneseni metapodatki ne bodo ohranjeni',
    firstMetaAction: 'Začni',
    placeholder: {
      add: 'Dodaj ',
      select: 'Izberi ',
    },
    source: {
      upload_file: 'Naloži datoteko',
      notion: 'Sinhroniziraj iz Notion',
      github: 'Sinhroniziraj iz Github',
    },
    type: {
      book: 'Knjiga',
      webPage: 'Spletna stran',
      paper: 'Znanstveni članek',
      socialMediaPost: 'Objava na družbenih omrežjih',
      personalDocument: 'Osebni dokument',
      businessDocument: 'Poslovni dokument',
      IMChat: 'Klepet',
      wikipediaEntry: 'Vnos iz Wikipedije',
      notion: 'Sinhroniziraj iz Notion',
      github: 'Sinhroniziraj iz Github',
      technicalParameters: 'Tehnični parametri',
    },
    field: {
      processRule: {
        processDoc: 'Obdelaj dokument',
        segmentRule: 'Pravilo segmenta',
        segmentLength: 'Dolžina segmentov',
        processClean: 'Čiščenje besedila',
      },
      book: {
        title: 'Naslov',
        language: 'Jezik',
        author: 'Avtor',
        publisher: 'Založnik',
        publicationDate: 'Datum objave',
        ISBN: 'ISBN',
        category: 'Kategorija',
      },
      webPage: {
        title: 'Naslov',
        url: 'URL',
        language: 'Jezik',
        authorPublisher: 'Avtor/Založnik',
        publishDate: 'Datum objave',
        topicKeywords: 'Teme/Ključne besede',
        description: 'Opis',
      },
      paper: {
        title: 'Naslov',
        language: 'Jezik',
        author: 'Avtor',
        publishDate: 'Datum objave',
        journalConferenceName: 'Ime revije/konference',
        volumeIssuePage: 'Letnik/Številka/Stran',
        DOI: 'DOI',
        topicsKeywords: 'Teme/Ključne besede',
        abstract: 'Povzetek',
      },
      socialMediaPost: {
        platform: 'Platforma',
        authorUsername: 'Avtor/Uporabniško ime',
        publishDate: 'Datum objave',
        postURL: 'URL objave',
        topicsTags: 'Teme/Oznake',
      },
      personalDocument: {
        title: 'Naslov',
        author: 'Avtor',
        creationDate: 'Datum nastanka',
        lastModifiedDate: 'Datum zadnje spremembe',
        documentType: 'Vrsta dokumenta',
        tagsCategory: 'Oznake/Kategorija',
      },
      businessDocument: {
        title: 'Naslov',
        author: 'Avtor',
        creationDate: 'Datum nastanka',
        lastModifiedDate: 'Datum zadnje spremembe',
        documentType: 'Vrsta dokumenta',
        departmentTeam: 'Oddelek/Ekipa',
      },
      IMChat: {
        chatPlatform: 'Platforma za klepet',
        chatPartiesGroupName: 'Udeleženci klepeta/Skupina',
        participants: 'Udeleženci',
        startDate: 'Datum začetka',
        endDate: 'Datum konca',
        topicsKeywords: 'Teme/Ključne besede',
        fileType: 'Vrsta datoteke',
      },
      wikipediaEntry: {
        title: 'Naslov',
        language: 'Jezik',
        webpageURL: 'URL spletne strani',
        editorContributor: 'Urednik/Sodelavec',
        lastEditDate: 'Datum zadnje spremembe',
        summaryIntroduction: 'Povzetek/Uvod',
      },
      notion: {
        title: 'Naslov',
        language: 'Jezik',
        author: 'Avtor',
        createdTime: 'Čas nastanka',
        lastModifiedTime: 'Čas zadnje spremembe',
        url: 'URL',
        tag: 'Oznaka',
        description: 'Opis',
      },
      github: {
        repoName: 'Ime repozitorija',
        repoDesc: 'Opis repozitorija',
        repoOwner: 'Lastnik repozitorija',
        fileName: 'Ime datoteke',
        filePath: 'Pot do datoteke',
        programmingLang: 'Programski jezik',
        url: 'URL',
        license: 'Licenca',
        lastCommitTime: 'Čas zadnje spremembe',
        lastCommitAuthor: 'Avtor zadnje spremembe',
      },
      originInfo: {
        originalFilename: 'Izvirno ime datoteke',
        originalFileSize: 'Izvirna velikost datoteke',
        uploadDate: 'Datum nalaganja',
        lastUpdateDate: 'Datum zadnje spremembe',
        source: 'Vir',
      },
      technicalParameters: {
        segmentSpecification: 'Specifikacija segmentov',
        segmentLength: 'Dolžina segmentov',
        avgParagraphLength: 'Povprečna dolžina odstavka',
        paragraphs: 'Odstavki',
        hitCount: 'Število pridobitev',
        embeddingTime: 'Čas vdelave',
        embeddedSpend: 'Stroški vdelave',
      },
    },
    languageMap: {
      zh: 'Kitajščina',
      en: 'Angleščina',
      es: 'Španščina',
      fr: 'Francoščina',
      de: 'Nemščina',
      ja: 'Japonščina',
      ko: 'Korejščina',
      ru: 'Ruščina',
      ar: 'Arabščina',
      pt: 'Portugalščina',
      it: 'Italijanščina',
      nl: 'Nizozemščina',
      pl: 'Poljščina',
      sv: 'Švedščina',
      tr: 'Turščina',
      he: 'Hebrejščina',
      hi: 'Hindujščina',
      da: 'Danščina',
      fi: 'Finščina',
      no: 'Norveščina',
      hu: 'Madžarščina',
      el: 'Grščina',
      cs: 'Češčina',
      th: 'Tajščina',
      id: 'Indonezijščina',
    },
    categoryMap: {
      book: {
        fiction: 'Leposlovje',
        biography: 'Biografija',
        history: 'Zgodovina',
        science: 'Znanost',
        technology: 'Tehnologija',
        education: 'Izobraževanje',
        philosophy: 'Filozofija',
        religion: 'Religija',
        socialSciences: 'Družboslovje',
        art: 'Umetnost',
        travel: 'Potovanja',
        health: 'Zdravje',
        selfHelp: 'Samopomoč',
        businessEconomics: 'Poslovanje in ekonomija',
        cooking: 'Kuhanje',
        childrenYoungAdults: 'Otroci in mladi odrasli',
        comicsGraphicNovels: 'Stripi in grafični romani',
        poetry: 'Poezija',
        drama: 'Drama',
        other: 'Drugo',
      },
      personalDoc: {
        notes: 'Zapiski',
        blogDraft: 'Osnutek bloga',
        diary: 'Dnevnik',
        researchReport: 'Raziskovalno poročilo',
        bookExcerpt: 'Odlomek iz knjige',
        schedule: 'Urnik',
        list: 'Seznam',
        projectOverview: 'Pregled projekta',
        photoCollection: 'Fotografska zbirka',
        creativeWriting: 'Ustvarjalno pisanje',
        codeSnippet: 'Koda',
        designDraft: 'Oblikovalski osnutek',
        personalResume: 'Osebni življenjepis',
        other: 'Drugo',
      },
      businessDoc: {
        meetingMinutes: 'Zapisniki sestankov',
        researchReport: 'Raziskovalno poročilo',
        proposal: 'Predlog',
        employeeHandbook: 'Priročnik za zaposlene',
        trainingMaterials: 'Izobraževalni materiali',
        requirementsDocument: 'Dokumentacija zahtev',
        designDocument: 'Oblikovalska dokumentacija',
        productSpecification: 'Specifikacija izdelka',
        financialReport: 'Finančno poročilo',
        marketAnalysis: 'Tržna analiza',
        projectPlan: 'Načrt projekta',
        teamStructure: 'Struktura ekipe',
        policiesProcedures: 'Pravila in postopki',
        contractsAgreements: 'Pogodbe in dogovori',
        emailCorrespondence: 'E-poštna korespondenca',
        other: 'Drugo',
      },
    },
  },
  embedding: {
    processing: 'Proces vdelave...',
    paused: 'Vdelava zaustavljena',
    completed: 'Vdelava zaključena',
    error: 'Napaka pri vdelavi',
    docName: 'Predobdelava dokumenta',
    mode: 'Pravilo segmentacije',
    segmentLength: 'Dolžina segmentov',
    textCleaning: 'Predobdelava in čiščenje besedila',
    segments: 'Odstavki',
    highQuality: 'Način visoke kakovosti',
    economy: 'Ekonomski način',
    estimate: 'Ocenjena poraba',
    stop: 'Ustavi obdelavo',
    resume: 'Nadaljuj obdelavo',
    automatic: 'Samodejno',
    custom: 'Po meri',
    previewTip: 'Predogled odstavkov bo na voljo po zaključku vdelave',
    hierarchical: 'Starš-otrok',
    childMaxTokens: 'Otrok',
    pause: 'Zaustavi',
    parentMaxTokens: 'Starš',
  },
  segment: {
    paragraphs: 'Odstavki',
    keywords: 'Ključne besede',
    addKeyWord: 'Dodaj ključno besedo',
    keywordError: 'Največja dolžina ključne besede je 20',
    characters: 'znakov',
    hitCount: 'Število pridobitev',
    vectorHash: 'Vektorski hash: ',
    questionPlaceholder: 'dodajte vprašanje tukaj',
    questionEmpty: 'Vprašanje ne sme biti prazno',
    answerPlaceholder: 'dodajte odgovor tukaj',
    answerEmpty: 'Odgovor ne sme biti prazen',
    contentPlaceholder: 'dodajte vsebino tukaj',
    contentEmpty: 'Vsebina ne sme biti prazna',
    newTextSegment: 'Nov besedilni segment',
    newQaSegment: 'Nov Q&A segment',
    delete: 'Izbriši ta del?',
    regenerationSuccessTitle: 'Regeneracija končana',
    expandChunks: 'Razširitev kosov',
    childChunk: 'Otroški kos',
    editedAt: 'Urejeno na',
    edited: 'UREJATI',
    addAnother: 'Dodajanje še enega',
    childChunks_one: 'OTROŠKI KOS',
    chunkDetail: 'Detajl koščka',
    chunkAdded: 'Dodan 1 kos',
    editChildChunk: 'Urejanje podrejenega kosa',
    regenerationConfirmTitle: 'Ali želite regenerirati otroške koščke?',
    empty: 'Ni najdenega koščka',
    searchResults_other: 'REZULTATI',
    childChunks_other: 'OTROŠKI KOŠČKI',
    addChildChunk: 'Dodajanje podrejenega kosa',
    editParentChunk: 'Urejanje nadrejenega kosa',
    regenerationConfirmMessage: 'Obnavljanje podrejenih kosov bo prepisalo trenutne podrejene koščke, vključno z urejenimi koščki in na novo dodanimi kosi. Regeneracije ni mogoče razveljaviti.',
    editChunk: 'Uredi kos',
    chunks_one: 'KOS',
    searchResults_one: 'REZULTAT',
    parentChunks_one: 'STARŠEVSKI KOS',
    characters_other: 'Znakov',
    chunks_other: 'KOSE',
    clearFilter: 'Počisti filter',
    newChildChunk: 'Nov podzakonski kos',
    characters_one: 'znak',
    regeneratingTitle: 'Regeneracija otroških kosov',
    regeneratingMessage: 'To lahko traja trenutek, prosim počakajte ...',
    parentChunks_other: 'STARŠEVSKI KOSI',
    collapseChunks: 'Strniti koščke',
    parentChunk: 'Starševski kos',
    regenerationSuccessMessage: 'To okno lahko zaprete.',
    newChunk: 'Nov kos',
    searchResults_zero: 'REZULTAT',
    chunk: 'Kos',
    addChunk: 'Dodajanje kosa',
    childChunkAdded: 'Dodan je 1 kos otroka',
    keywordDuplicate: 'Ključna beseda že obstaja',
    keywordEmpty: 'Ključna beseda ne more biti prazna',
  },
}

export default translation
