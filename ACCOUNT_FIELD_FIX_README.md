# Account Field Fix - 账户字段修复说明

## 问题描述

在 `account` 表中添加了新的必填字段（特别是 `login_name`）后，原有的注册流程会出现错误，因为 `AccountService.create_account` 方法没有为新字段提供默认值。

## 解决方案

我们修改了 `api/services/account_service.py` 文件中的 `create_account` 方法，在创建账户时自动为新字段提供默认值。

### 修改内容

在 `create_account` 方法中添加了以下代码：

```python
# Set default values for new required fields
account.login_name = email  # Use email as login_name by default
account.user_type = UserType.EMPLOYEE.value  # Default to employee
account.status = AccountStatus.ACTIVE.value
account.initialized_at = db.func.current_timestamp()

# Set optional fields with reasonable defaults
account.base_org_id = None  # Will be set later if needed
account.start_date = None
account.end_date = None
account.gender = None
account.identity_no = None
account.birth_day = None
account.mobile = None
account.office_phone = None
account.employ_no = None
account.base_post_id = None
account.job = None
account.duty = None
account.degree_code = None
account.comments = None
account.sap_hr_user_id = None
```

### 默认值设置

#### 必填字段
- `login_name`: 使用邮箱地址作为默认值
- `user_type`: 设置为 "EMPLOYEE"（内部用户）
- `status`: 设置为 "active"

#### 可选字段
所有可选字段都设置为 `None`，可以在后续通过用户界面或API更新。

## 优势

1. **无需修改前端**: 前端代码保持不变，所有修改都在后端完成
2. **向后兼容**: 不影响现有的注册流程
3. **自动处理**: 所有新字段都会自动设置合适的默认值
4. **安全可靠**: 遵循原有的安全策略和验证逻辑

## 测试

使用提供的测试脚本验证修改是否有效：

```bash
python test_account_creation.py
```

## 影响范围

这个修改会影响以下注册场景：

1. **邮箱验证码注册** (`EmailCodeLoginApi`)
2. **OAuth 注册** (`OAuthCallback`)
3. **系统初始化** (`RegisterService.setup`)
4. **邀请注册** (`RegisterService.invite_new_member`)
5. **忘记密码时创建新账户**

所有这些场景现在都会自动为新字段提供默认值。

## 后续步骤

1. **测试注册流程**: 确保所有注册方式都能正常工作
2. **用户界面更新**: 考虑在用户界面中添加字段，允许用户更新这些默认值
3. **API 更新**: 创建更新用户信息的API，允许用户修改这些字段

## 文件修改

- `api/services/account_service.py`: 修改 `create_account` 方法
- `test_account_creation.py`: 测试脚本（新增）
- `ACCOUNT_FIELD_FIX_README.md`: 本文档（新增）

## 注意事项

- 这个修改是向后兼容的，不会影响现有用户
- 新字段的默认值是为了确保兼容性，后续可以通过用户界面更新
- 如果需要在注册时收集更多信息，可以考虑扩展注册接口的参数 