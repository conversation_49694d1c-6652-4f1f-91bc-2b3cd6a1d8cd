import json

import flask_login  # type: ignore
from flask import Response, request
from flask_login import user_loaded_from_request, user_logged_in
from werkzeug.exceptions import NotFound, Unauthorized

from configs import dify_config
from dify_app import DifyApp
from extensions.ext_database import db
from libs.passport import PassportService
from models.account import Account, Tenant, TenantAccountJoin
from models.model import AppMCPServer, EndUser
from services.account_service import AccountService

login_manager = flask_login.LoginManager()


# Flask-Login configuration
@login_manager.request_loader
def load_user_from_request(request_from_flask_login):
    """Load user based on the request."""
    auth_header = request.headers.get("Authorization", "")
    auth_token: str | None = None
    if auth_header:
        if " " not in auth_header:
            raise Unauthorized("Invalid Authorization header format. Expected 'Bearer <api-key>' format.")
        auth_scheme, auth_token = auth_header.split(maxsplit=1)
        auth_scheme = auth_scheme.lower()
        if auth_scheme != "bearer":
            raise Unauthorized("Invalid Authorization header format. Expected 'Bearer <api-key>' format.")
    else:
        auth_token = request.args.get("_token")

    # Check for admin API key authentication first
    if dify_config.ADMIN_API_KEY_ENABLE and auth_header:
        admin_api_key = dify_config.ADMIN_API_KEY
        if admin_api_key and admin_api_key == auth_token:
            workspace_id = request.headers.get("X-WORKSPACE-ID")
            if workspace_id:
                tenant_account_join = (
                    db.session.query(Tenant, TenantAccountJoin)
                    .filter(Tenant.id == workspace_id)
                    .filter(TenantAccountJoin.tenant_id == Tenant.id)
                    .filter(TenantAccountJoin.role == "owner")
                    .one_or_none()
                )
                if tenant_account_join:
                    tenant, ta = tenant_account_join
                    account = db.session.query(Account).filter_by(id=ta.account_id).first()
                    if account:
                        account.current_tenant = tenant
                        return account

    if request.blueprint in {"console", "inner_api"}:
        if not auth_token:
            raise Unauthorized("Invalid Authorization token.")
        
        # ===== 修改：使用统一的token验证方法 =====
        try:
            # 使用AccountService的通用验证方法，自动识别JWT或IAM token
            account = AccountService.verify_token(auth_token)
            if account:
                return account
            else:
                raise Unauthorized("Invalid Authorization token.")
        except Exception as e:
            # 如果统一验证失败，回退到原有的IAM验证逻辑（保持兼容性）
            iam_configured = _is_iam_configured()
            if iam_configured and _is_iam_token(auth_token):
                try:
                    return _verify_iam_token(auth_token)
                except Exception as iam_error:
                    import logging
                    logging.warning(f"IAM token validation failed, falling back to JWT: {str(iam_error)}")
                    try:
                        return _verify_jwt_token_for_console(auth_token)
                    except Exception as jwt_error:
                        raise Unauthorized(f"Token validation failed. IAM error: {str(iam_error)}. JWT error: {str(jwt_error)}")
            
            # 如果不是IAM token，尝试JWT验证
            try:
                return _verify_jwt_token_for_console(auth_token)
            except Exception as jwt_error:
                raise Unauthorized(f"Token validation failed: {str(e)}. JWT error: {str(jwt_error)}")
        
    elif request.blueprint == "web":
        decoded = PassportService().verify(auth_token)
        end_user_id = decoded.get("end_user_id")
        if not end_user_id:
            raise Unauthorized("Invalid Authorization token.")
        end_user = db.session.query(EndUser).filter(EndUser.id == decoded["end_user_id"]).first()
        if not end_user:
            raise NotFound("End user not found.")
        return end_user
    elif request.blueprint == "mcp":
        server_code = request.view_args.get("server_code") if request.view_args else None
        if not server_code:
            raise Unauthorized("Invalid Authorization token.")
        app_mcp_server = db.session.query(AppMCPServer).filter(AppMCPServer.server_code == server_code).first()
        if not app_mcp_server:
            raise NotFound("App MCP server not found.")
        end_user = (
            db.session.query(EndUser)
            .filter(EndUser.external_user_id == app_mcp_server.id, EndUser.type == "mcp")
            .first()
        )
        if not end_user:
            raise NotFound("End user not found.")
        return end_user


@user_logged_in.connect
@user_loaded_from_request.connect
def on_user_logged_in(_sender, user):
    """Called when a user logged in.

    Note: AccountService.load_logged_in_account will populate user.current_tenant_id
    through the load_user method, which calls account.set_tenant_id().
    """
    # tenant_id context variable removed - using current_user.current_tenant_id directly
    pass


@login_manager.unauthorized_handler
def unauthorized_handler():
    """Handle unauthorized requests."""
    return Response(
        json.dumps({"code": "unauthorized", "message": "Unauthorized."}),
        status=401,
        content_type="application/json",
    )


def init_app(app: DifyApp):
    login_manager.init_app(app)


# ===== 新增：IAM验证相关函数 =====

def _is_iam_configured():
    """检查IAM是否已配置"""
    return all([
        dify_config.IAM_HOST,
        dify_config.IAM_NAMESPACE, 
        dify_config.IAM_CLIENT_ID
    ])

def _is_iam_token(token: str) -> bool:
    """检查token是否像IAM token（简单启发式检查）"""
    # IAM access_token通常比JWT token更长，且不包含'.'
    # JWT token格式: header.payload.signature (包含两个'.')
    # IAM token通常是随机字符串，长度较长
    
    if not token or len(token) < 10:
        return False
        
    # JWT token包含两个'.'，IAM token通常不包含
    if token.count('.') == 2:
        return False
        
    # IAM token通常更长（> 50字符）
    if len(token) > 50:
        return True
        
    return False

def _verify_iam_token(access_token: str) -> Account:
    """验证IAM访问令牌并返回用户信息"""
    import requests
    
    try:
        # 构建IAM getUserInfo请求URL
        user_info_url = f"http://{dify_config.IAM_HOST}/am-gateway/{dify_config.IAM_NAMESPACE}/am-protocol-service/oauth2/getUserInfo"
        params = {
            "access_token": access_token,
            "client_id": dify_config.IAM_CLIENT_ID
        }
        
        # 调用IAM接口验证令牌并获取用户信息
        response = requests.get(user_info_url, params=params, timeout=10)
        
        if response.status_code != 200:
            raise Unauthorized(f"IAM token validation failed: HTTP {response.status_code}")
        
        iam_user_info = response.json()
        
        # 检查IAM返回的用户信息
        if "userName" not in iam_user_info:
            raise Unauthorized("Invalid IAM response: missing userName")
        
        # 根据IAM用户信息查找Dify用户
        account = _find_dify_user_by_iam_info(iam_user_info)
        
        return account
        
    except requests.RequestException as e:
        raise Unauthorized(f"IAM service unavailable: {str(e)}")
    except Exception as e:
        raise Unauthorized(f"IAM token validation failed: {str(e)}")

def _find_dify_user_by_iam_info(iam_user_info) -> Account:
    """根据IAM用户信息查找Dify用户"""
    user_name = iam_user_info.get("userName")
    
    if not user_name:
        raise Unauthorized("Invalid IAM user info: missing userName")
    
    # 首先尝试通过login_name查找用户   TODO 后续查询条件根据对接情况而定
    account = db.session.query(Account).filter_by(login_name=user_name).first()
    
    if account:
        # 检查账户状态
        if account.status != "active":
            raise Unauthorized(f"Account {user_name} is not active")
            
        # 加载用户的租户信息
        account = AccountService.load_user(account.id)
        if not account:
            raise Unauthorized("Failed to load user account")
            
        return account
    
    # 如果通过login_name找不到，尝试通过email查找
    user_email = iam_user_info.get("email")
    if user_email:
        account = db.session.query(Account).filter_by(email=user_email).first()
        if account:
            # 更新login_name为IAM的userName
            account.login_name = user_name
            db.session.commit()
            
            # 重新加载用户信息
            account = AccountService.load_user(account.id)
            if not account:
                raise Unauthorized("Failed to load user account")
                
            return account
    
    # 如果用户不存在，返回错误（要求用户必须先通过用户创建接口创建）
    raise Unauthorized(f"User {user_name} not found in Dify system. Please create user first.")

def _verify_jwt_token_for_console(auth_token: str) -> Account:
    """验证JWT token并返回Account用户（Console API专用）"""
    decoded = PassportService().verify(auth_token)
    user_id = decoded.get("user_id")
    source = decoded.get("token_source")
    if source:
        raise Unauthorized("Invalid Authorization token.")
    if not user_id:
        raise Unauthorized("Invalid Authorization token.")

    logged_in_account = AccountService.load_logged_in_account(account_id=user_id)
    return logged_in_account
