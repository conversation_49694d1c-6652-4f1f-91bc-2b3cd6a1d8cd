const translation = {
  input: 'EINGABE',
  result: 'ERGEBNIS',
  detail: 'DETAILS',
  tracing: 'NACHVERFOLGUNG',
  resultPanel: {
    status: 'STATUS',
    time: 'VERSTRICHENE ZEIT',
    tokens: 'GESAMTZEICHEN',
  },
  meta: {
    title: 'METADATEN',
    status: 'Status',
    version: 'Version',
    executor: '<PERSON>s<PERSON><PERSON>hren<PERSON>',
    startTime: 'Startzeit',
    time: 'Verstrichene Zeit',
    tokens: 'Gesamtzei<PERSON>',
    steps: 'Ausführungsschritte',
  },
  resultEmpty: {
    title: 'Dieser Lauf gibt nur das JSON-Format aus',
    tipLeft: 'Bitte gehen Sie zum ',
    Link: 'Detailpanel',
    tipRight: 'ansehen.',
    link: 'Gruppe Detail',
  },
  actionLogs: 'Aktionsprotokolle',
  circularInvocationTip: 'Es gibt einen zirkulären Aufruf von Werkzeugen/Knoten im aktuellen Workflow.',
}

export default translation
