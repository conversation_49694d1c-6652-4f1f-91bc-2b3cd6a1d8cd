const translation = {
  common: {
    undo: '<PERSON><PERSON><PERSON><PERSON>',
    redo: '<PERSON>ée<PERSON>écuter',
    editing: 'Édition',
    autoSaved: 'Sauvegardé automatiquement',
    unpublished: 'Non publié',
    published: 'Publié',
    publish: 'Publier',
    update: 'Mettre à jour',
    run: 'Exécuter',
    running: 'En cours d\'exécution',
    inRunMode: 'En mode exécution',
    inPreview: 'En aperçu',
    inPreviewMode: 'En mode aperçu',
    preview: 'Aperçu',
    viewRunHistory: 'Voir l\'historique des exécutions',
    runHistory: 'Historique des exécutions',
    goBackToEdit: 'Retour à l\'éditeur',
    conversationLog: 'Journal de conversation',
    features: 'Fonctionnalités',
    debugAndPreview: 'Aperçu',
    restart: 'Redémarrer',
    currentDraft: 'Brouillon actuel',
    currentDraftUnpublished: 'Brouillon actuel non publié',
    latestPublished: 'Dernière publication',
    publishedAt: 'Publi<PERSON> le',
    restore: 'Restaurer',
    runApp: 'Exécuter l\'application',
    batchRunApp: 'Exécuter l\'application en lot',
    accessAPIReference: 'Accéder à la référence API',
    embedIntoSite: 'Intégrer au site',
    addTitle: 'Ajouter un titre...',
    addDescription: 'Ajouter une description...',
    noVar: 'Pas de variable',
    searchVar: 'Rechercher une variable',
    variableNamePlaceholder: 'Nom de la variable',
    setVarValuePlaceholder: 'Définir la valeur de la variable',
    needConnectTip: 'Cette étape n\'est connectée à rien',
    maxTreeDepth: 'Limite maximale de {{depth}} nœuds par branche',
    workflowProcess: 'Processus de flux de travail',
    notRunning: 'Pas encore en cours d\'exécution',
    previewPlaceholder: 'Entrez le contenu dans la boîte ci-dessous pour commencer à déboguer le Chatbot',
    effectVarConfirm: {
      title: 'Supprimer la variable',
      content: 'La variable est utilisée dans d\'autres nœuds. Voulez-vous toujours la supprimer?',
    },
    insertVarTip: 'Appuyez sur la touche \'/\' pour insérer rapidement',
    processData: 'Traiter les données',
    input: 'Entrée',
    output: 'Sortie',
    jinjaEditorPlaceholder: 'Tapez \'/\' ou \'{\' pour insérer une variable',
    viewOnly: 'Affichage seulement',
    showRunHistory: 'Afficher l\'historique des exécutions',
    enableJinja: 'Activer le support des templates Jinja',
    learnMore: 'En savoir plus',
    copy: 'Copier',
    duplicate: 'Dupliquer',
    pasteHere: 'Coller ici',
    pointerMode: 'Mode pointeur',
    handMode: 'Mode main',
    model: 'Modèle',
    workflowAsTool: 'Flux de travail en tant qu\'outil',
    configureRequired: 'Configuration requise',
    configure: 'Configurer',
    manageInTools: 'Gérer dans les outils',
    workflowAsToolTip: 'Reconfiguration de l\'outil requise après la mise à jour du flux de travail.',
    viewDetailInTracingPanel: 'Voir les détails',
    syncingData: 'Synchroniser des données en quelques secondes.',
    importDSL: 'Importe DSL',
    importDSLTip: 'Le projet actuel sera écrasé. Exporter le flux de travail en tant que sauvegarde avant d\'importer.',
    backupCurrentDraft: 'Sauvegarder le projet actuel',
    chooseDSL: 'Choisir le fichier DSL(yml)',
    overwriteAndImport: 'Écraser et importer',
    importFailure: 'Echec de l\'importation',
    importSuccess: 'Import avec succès',
    parallelTip: {
      click: {
        title: 'Cliquer',
        desc: 'à ajouter',
      },
      drag: {
        title: 'Traîner',
        desc: 'pour se connecter',
      },
      limit: 'Le parallélisme est limité aux branches {{num}}.',
      depthLimit: 'Limite de couches d’imbrication parallèle de {{num}} couches',
    },
    parallelRun: 'Exécution parallèle',
    disconnect: 'Déconnecter',
    jumpToNode: 'Aller à ce nœud',
    addParallelNode: 'Ajouter un nœud parallèle',
    parallel: 'PARALLÈLE',
    branch: 'BRANCHE',
    featuresDocLink: 'Pour en savoir plus',
    ImageUploadLegacyTip: 'Vous pouvez désormais créer des variables de type de fichier dans le formulaire de démarrage. À l’avenir, nous ne prendrons plus en charge la fonctionnalité de téléchargement d’images.',
    fileUploadTip: 'Les fonctionnalités de téléchargement d’images ont été mises à niveau vers le téléchargement de fichiers.',
    featuresDescription: 'Améliorer l’expérience utilisateur de l’application web',
    importWarning: 'Prudence',
    importWarningDetails: 'La différence de version DSL peut affecter certaines fonctionnalités',
    openInExplore: 'Ouvrir dans Explorer',
    onFailure: 'Sur l’échec',
    addFailureBranch: 'Ajouter une branche d’échec',
    loadMore: 'Charger plus de flux de travail',
    noHistory: 'Pas d’histoire',
    exportPNG: 'Exporter en PNG',
    exitVersions: 'Versions de sortie',
    exportSVG: 'Exporter en SVG',
    publishUpdate: 'Publier une mise à jour',
    noExist: 'Aucune variable de ce type',
    versionHistory: 'Historique des versions',
    referenceVar: 'Variable de référence',
    exportImage: 'Exporter l\'image',
    exportJPEG: 'Exporter en JPEG',
    needEndNode: 'Le nœud de fin doit être ajouté',
    needAnswerNode: 'Le nœud de réponse doit être ajouté.',
    addBlock: 'Ajouter un nœud',
  },
  env: {
    envPanelTitle: 'Variables d\'Environnement',
    envDescription: 'Les variables d\'environnement peuvent être utilisées pour stocker des informations privées et des informations d\'identification. Elles sont en lecture seule et peuvent être séparées du fichier DSL lors de l\'exportation.',
    envPanelButton: 'Ajouter Variable',
    modal: {
      title: 'Ajouter Variables d\'Environnement',
      editTitle: 'Editer titre',
      type: 'Type',
      name: 'Nom',
      namePlaceholder: 'Nom de l\'env',
      value: 'valeur',
      valuePlaceholder: 'Valeur de l\'env',
      secretTip: 'Utilisé pour définir des informations ou des données sensibles, avec des paramètres DSL configurés pour la prévention des fuites.',
      description: 'Description',
      descriptionPlaceholder: 'Décrivez la variable',
    },
    export: {
      title: 'Exporter des variables d\'environnement secrètes?',
      checkbox: 'Exporter les valeurs secrètes',
      ignore: 'Exporter DSL',
      export: 'Exporter les DSL avec des valeurs secrètes',
    },
  },
  chatVariable: {
    panelTitle: 'Variables de Conversation',
    panelDescription: 'Les Variables de Conversation sont utilisées pour stocker des informations interactives dont le LLM a besoin de se souvenir, y compris l\'historique des conversations, les fichiers téléchargés et les préférences de l\'utilisateur. Elles sont en lecture-écriture.',
    docLink: 'Consultez notre documentation pour en savoir plus.',
    button: 'Ajouter une Variable',
    modal: {
      title: 'Ajouter une Variable de Conversation',
      editTitle: 'Modifier une Variable de Conversation',
      name: 'Nom',
      namePlaceholder: 'Nom de la variable',
      type: 'Type',
      value: 'Valeur par Défaut',
      valuePlaceholder: 'Valeur par défaut, laisser vide pour ne pas définir',
      description: 'Description',
      descriptionPlaceholder: 'Décrivez la variable',
      editInJSON: 'Éditer en JSON',
      oneByOne: 'Ajouter un par un',
      editInForm: 'Éditer dans le Formulaire',
      arrayValue: 'Valeur',
      addArrayValue: 'Ajouter une Valeur',
      objectKey: 'Clé',
      objectType: 'Type',
      objectValue: 'Valeur par Défaut',
    },
    storedContent: 'Contenu stocké',
    updatedAt: 'Mis à jour le ',
  },
  changeHistory: {
    title: 'Historique des modifications',
    placeholder: 'Vous n\'avez encore rien modifié',
    clearHistory: 'Effacer l\'historique',
    hint: 'Conseil',
    hintText: 'Vos actions d\'édition sont suivies dans un historique des modifications, qui est stocké sur votre appareil pour la durée de cette session. Cet historique sera effacé lorsque vous quitterez l\'éditeur.',
    stepBackward_one: '{{count}} pas en arrière',
    stepBackward_other: '{{count}} pas en arrière',
    stepForward_one: '{{count}} pas en avant',
    stepForward_other: '{{count}} pas en avant',
    sessionStart: 'Début de la session',
    currentState: 'État actuel',
    noteAdd: 'Note ajoutée',
    noteChange: 'Note modifiée',
    noteDelete: 'Note supprimée',
    nodeConnect: 'Node connecté',
    nodeChange: 'Nœud changé',
    nodeResize: 'Nœud redimensionné',
    edgeDelete: 'Nœud déconnecté',
    nodeDelete: 'Nœud supprimé',
    nodePaste: 'Node collé',
    nodeDragStop: 'Nœud déplacé',
    nodeTitleChange: 'Titre du nœud modifié',
    nodeAdd: 'Nœud ajouté',
    nodeDescriptionChange: 'La description du nœud a changé',
  },
  errorMsg: {
    fieldRequired: '{{field}} est requis',
    authRequired: 'Autorisation requise',
    invalidJson: '{{field}} est un JSON invalide',
    fields: {
      variable: 'Nom de la variable',
      variableValue: 'Valeur de la variable',
      code: 'Code',
      model: 'Modèle',
      rerankModel: 'Modèle de rerank',
      visionVariable: 'Vision Variable',
    },
    invalidVariable: 'Variable invalide',
    rerankModelRequired: 'Avant d’activer le modèle de reclassement, veuillez confirmer que le modèle a été correctement configuré dans les paramètres.',
    noValidTool: '{{field}} aucun outil valide sélectionné',
    toolParameterRequired: '{{field}} : le paramètre [{{param}}] est obligatoire',
  },
  singleRun: {
    testRun: 'Exécution de test',
    startRun: 'Démarrer l\'exécution',
    running: 'En cours d\'exécution',
    testRunIteration: 'Itération de l\'exécution de test',
    back: 'Retour',
    iteration: 'Itération',
    loop: 'Boucle',
  },
  tabs: {
    'tools': 'Outils',
    'allTool': 'Tous',
    'builtInTool': 'Intégré',
    'customTool': 'Personnalisé',
    'workflowTool': 'Flux de travail',
    'question-understand': 'Compréhension des questions',
    'logic': 'Logique',
    'transform': 'Transformer',
    'utilities': 'Utilitaires',
    'noResult': 'Aucun résultat trouvé',
    'searchTool': 'Outil de recherche',
    'plugin': 'Plugin',
    'agent': 'Stratégie d’agent',
    'blocks': 'Nœuds',
    'searchBlock': 'Nœud de recherche',
  },
  blocks: {
    'start': 'Début',
    'end': 'Fin',
    'answer': 'Réponse',
    'llm': 'LLM',
    'knowledge-retrieval': 'Récupération de connaissances',
    'question-classifier': 'Classificateur de questions',
    'if-else': 'SI/SINON',
    'code': 'Code',
    'template-transform': 'Modèle',
    'http-request': 'Requête HTTP',
    'variable-assigner': 'Assigneur de variables',
    'variable-aggregator': 'Agrégateur de variables',
    'assigner': 'Assignateur de Variables',
    'iteration-start': 'Début d\'itération',
    'iteration': 'Itération',
    'parameter-extractor': 'Extracteur de paramètres',
    'list-operator': 'Opérateur de liste',
    'document-extractor': 'Extracteur de documents',
    'agent': 'Agent',
    'loop-end': 'Sortir de la boucle',
    'loop': 'Boucle',
    'loop-start': 'Début de boucle',
  },
  blocksAbout: {
    'start': 'Définir les paramètres initiaux pour lancer un flux de travail',
    'end': 'Définir la fin et le type de résultat d\'un flux de travail',
    'answer': 'Définir le contenu de la réponse d\'une conversation',
    'llm': 'Inviter de grands modèles de langage pour répondre aux questions ou traiter le langage naturel',
    'knowledge-retrieval': 'Permet de consulter le contenu textuel lié aux questions des utilisateurs à partir de la base de connaissances',
    'question-classifier': 'Définir les conditions de classification des questions des utilisateurs, LLM peut définir comment la conversation progresse en fonction de la description de la classification',
    'if-else': 'Permet de diviser le flux de travail en deux branches basées sur des conditions if/else',
    'code': 'Exécuter un morceau de code Python ou NodeJS pour implémenter une logique personnalisée',
    'template-transform': 'Convertir les données en chaîne en utilisant la syntaxe du template Jinja',
    'http-request': 'Permettre l\'envoi de requêtes serveur via le protocole HTTP',
    'variable-assigner': 'Agrégation de variables de plusieurs branches en une seule variable pour la configuration unifiée des nœuds en aval.',
    'assigner': 'Le nœud d\'assignation de variables est utilisé pour attribuer des valeurs aux variables modifiables (comme les variables de conversation).',
    'variable-aggregator': 'Agrégation de variables de plusieurs branches en une seule variable pour la configuration unifiée des nœuds en aval.',
    'iteration': 'Effectuer plusieurs étapes sur un objet de liste jusqu\'à ce que tous les résultats soient produits.',
    'parameter-extractor': 'Utiliser LLM pour extraire des paramètres structurés du langage naturel pour les invocations d\'outils ou les requêtes HTTP.',
    'list-operator': 'Utilisé pour filtrer ou trier le contenu d’un tableau.',
    'document-extractor': 'Utilisé pour analyser les documents téléchargés en contenu texte facilement compréhensible par LLM.',
    'agent': 'Appel de grands modèles de langage pour répondre à des questions ou traiter le langage naturel',
    'loop': 'Exécutez une boucle de logique jusqu\'à ce que la condition de terminaison soit remplie ou que le nombre maximum de boucles soit atteint.',
    'loop-end': 'Équivalent à "break". Ce nœud n\'a pas d\'éléments de configuration. Lorsque le corps de la boucle atteint ce nœud, la boucle se termine.',
  },
  operator: {
    zoomIn: 'Zoomer',
    zoomOut: 'Dézoomer',
    zoomTo50: 'Zoomer à 50%',
    zoomTo100: 'Zoomer à 100%',
    zoomToFit: 'Zoomer pour ajuster',
  },
  panel: {
    userInputField: 'Champ de saisie de l\'utilisateur',
    helpLink: 'Lien d\'aide',
    about: 'À propos',
    createdBy: 'Créé par',
    nextStep: 'Étape suivante',
    runThisStep: 'Exécuter cette étape',
    checklist: 'Liste de contrôle',
    checklistTip: 'Assurez-vous que tous les problèmes sont résolus avant de publier',
    checklistResolved: 'Tous les problèmes ont été résolus',
    change: 'Modifier',
    optional: '(facultatif)',
    moveToThisNode: 'Déplacer vers ce nœud',
    organizeBlocks: 'Organiser les nœuds',
    addNextStep: 'Ajoutez la prochaine étape dans ce flux de travail',
    selectNextStep: 'Sélectionner la prochaine étape',
    changeBlock: 'Changer de nœud',
    maximize: 'Maximiser le Canvas',
    minimize: 'Sortir du mode plein écran',
  },
  nodes: {
    common: {
      outputVars: 'Variables de sortie',
      insertVarTip: 'Insérer une variable',
      memory: {
        memory: 'Mémoire',
        memoryTip: 'Paramètres de mémoire de conversation',
        windowSize: 'Taille de la fenêtre',
        conversationRoleName: 'Nom du rôle de conversation',
        user: 'Préfixe utilisateur',
        assistant: 'Préfixe assistant',
      },
      memories: {
        title: 'Mémoires',
        tip: 'Mémoire de conversation',
        builtIn: 'Intégré',
      },
      errorHandle: {
        none: {
          title: 'Aucun',
          desc: 'Le nœud cessera de s’exécuter si une exception se produit et n’est pas gérée',
        },
        defaultValue: {
          title: 'Valeur par défaut',
          desc: 'Lorsqu’une erreur se produit, spécifiez un contenu de sortie statique.',
          tip: 'En cas d’erreur, le retour est inférieur à la valeur.',
          inLog: 'Exception de nœud, sortie en fonction des valeurs par défaut.',
          output: 'Valeur par défaut de sortie',
        },
        failBranch: {
          desc: 'Lorsqu’une erreur se produit, il exécute la branche d’exception',
          customize: 'Accédez au canevas pour personnaliser la logique de branche d’échec.',
          customizeTip: 'Lorsque la branche fail est activée, les exceptions levées par les nœuds ne mettent pas fin au processus. Au lieu de cela, il exécutera automatiquement la branche d’échec prédéfinie, ce qui vous permettra de fournir de manière flexible des messages d’erreur, des rapports, des correctifs ou des actions d’ignorance.',
          inLog: 'Exception de nœud, exécutera automatiquement la branche d’échec. La sortie du nœud renverra un type d’erreur et un message d’erreur et les transmettra en aval.',
          title: 'Branche d’échec',
        },
        partialSucceeded: {
          tip: 'Il y a des nœuds {{num}} dans le processus qui fonctionnent anormalement, veuillez aller dans le traçage pour vérifier les journaux.',
        },
        title: 'Gestion des erreurs',
        tip: 'Stratégie de gestion des exceptions, déclenchée lorsqu’un nœud rencontre une exception.',
      },
      retry: {
        retry: 'Réessayer',
        retryOnFailure: 'Réessai en cas d’échec',
        maxRetries: 'Nombre maximal de tentatives',
        retryInterval: 'intervalle de nouvelle tentative',
        retryTimes: 'Réessayez {{times}} fois en cas d’échec',
        retrying: 'Réessayer...',
        retrySuccessful: 'Réessai réussi',
        retryFailed: 'Échec de la nouvelle tentative',
        retryFailedTimes: '{{times}} les tentatives ont échoué',
        times: 'fois',
        ms: 'ms',
        retries: '{{num}} Tentatives',
      },
      typeSwitch: {},
    },
    start: {
      required: 'requis',
      inputField: 'Champ de saisie',
      builtInVar: 'Variables intégrées',
      outputVars: {
        query: 'Saisie utilisateur',
        memories: {
          des: 'Historique de conversation',
          type: 'type de message',
          content: 'contenu du message',
        },
        files: 'Liste de fichiers',
      },
      noVarTip: 'Définir les entrées qui peuvent être utilisées dans le flux de travail',
    },
    end: {
      outputs: 'Sorties',
      output: {
        type: 'type de sortie',
        variable: 'variable de sortie',
      },
      type: {
        'none': 'Aucun',
        'plain-text': 'Texte brut',
        'structured': 'Structuré',
      },
    },
    answer: {
      answer: 'Réponse',
      outputVars: 'Variables de sortie',
    },
    llm: {
      model: 'modèle',
      variables: 'variables',
      context: 'contexte',
      contextTooltip: 'Vous pouvez importer des connaissances en tant que contexte',
      notSetContextInPromptTip: 'Pour activer la fonctionnalité de contexte, remplissez la variable de contexte dans le PROMPT.',
      prompt: 'invite',
      roleDescription: {
        system: 'Donner des instructions de haut niveau pour la conversation',
        user: 'Fournir des instructions, des questions ou toute entrée textuelle au modèle',
        assistant: 'Les réponses du modèle basées sur les messages des utilisateurs',
      },
      addMessage: 'Ajouter un message',
      vision: 'vision',
      files: 'Fichiers',
      resolution: {
        name: 'Résolution',
        high: 'Haute',
        low: 'Basse',
      },
      outputVars: {
        output: 'Contenu généré',
        usage: 'Informations sur l\'utilisation du modèle',
      },
      singleRun: {
        variable: 'Variable',
      },
      sysQueryInUser: 'sys.query dans le message utilisateur est requis',
      jsonSchema: {
        warningTips: {
          saveSchema: 'Veuillez terminer la modification du champ actuel avant d\'enregistrer le schéma.',
        },
        apply: 'Appliquer',
        addField: 'Ajouter un champ',
        generationTip: 'Vous pouvez utiliser un langage naturel pour créer rapidement un schéma JSON.',
        promptPlaceholder: 'Décrivez votre schéma JSON...',
        descriptionPlaceholder: 'Ajouter une description',
        instruction: 'Instruction',
        resetDefaults: 'Réinitialiser',
        generatedResult: 'Résultat généré',
        fieldNamePlaceholder: 'Nom du champ',
        addChildField: 'Ajouter un champ enfant',
        back: 'Retour',
        showAdvancedOptions: 'Afficher les options avancées',
        title: 'Schéma de sortie structuré',
        generating: 'Génération de schéma JSON...',
        stringValidations: 'Validations de chaîne',
        import: 'Importer depuis JSON',
        promptTooltip: 'Convertissez la description textuelle en une structure de schéma JSON standardisé.',
        generate: 'Générer',
        doc: 'En savoir plus sur la sortie structurée',
        regenerate: 'Régénérer',
        required: 'nécessaire',
        generateJsonSchema: 'Générer un schéma JSON',
        resultTip: 'Voici le résultat généré. Si vous n\'êtes pas satisfait, vous pouvez revenir en arrière et modifier votre demande.',
      },
    },
    knowledgeRetrieval: {
      queryVariable: 'Variable de requête',
      knowledge: 'Connaissances',
      outputVars: {
        output: 'Données segmentées récupérées',
        content: 'Contenu segmenté',
        title: 'Titre segmenté',
        icon: 'Icône segmentée',
        url: 'URL segmentée',
        metadata: 'Autres métadonnées',
      },
      metadata: {
        options: {
          disabled: {
            subTitle: 'Ne pas activer le filtrage des métadonnées',
            title: 'Handicapé',
          },
          automatic: {
            subTitle: 'Générer automatiquement des conditions de filtrage des métadonnées en fonction de la requête de l\'utilisateur',
            title: 'Automatique',
            desc: 'Générer automatiquement des conditions de filtrage de métadonnées en fonction de la variable de requête.',
          },
          manual: {
            subTitle: 'Ajouter manuellement des conditions de filtrage des métadonnées',
            title: 'Manuel',
          },
        },
        panel: {
          placeholder: 'Entrez la valeur',
          add: 'Ajouter une condition',
          search: 'Rechercher des métadonnées',
          conditions: 'Conditions',
          datePlaceholder: 'Choisissez un moment...',
          select: 'Sélectionner une variable...',
          title: 'Conditions de filtrage des métadonnées',
        },
        title: 'Filtrage des métadonnées',
      },
    },
    http: {
      inputVars: 'Variables de saisie',
      api: 'API',
      apiPlaceholder: 'Entrez l\'URL, tapez ‘/’ pour insérer une variable',
      notStartWithHttp: 'L\'API doit commencer par http:// ou https://',
      key: 'Clé',
      value: 'Valeur',
      bulkEdit: 'Édition en masse',
      keyValueEdit: 'Édition clé-valeur',
      headers: 'En-têtes',
      params: 'Paramètres',
      body: 'Corps',
      outputVars: {
        body: 'Contenu de la réponse',
        statusCode: 'Code de statut de la réponse',
        headers: 'Liste des en-têtes de réponse JSON',
        files: 'Liste des fichiers',
      },
      authorization: {
        'authorization': 'Autorisation',
        'authorizationType': 'Type d\'autorisation',
        'no-auth': 'Aucune',
        'api-key': 'Clé API',
        'auth-type': 'Type d\'authentification',
        'basic': 'De base',
        'bearer': 'Bearer',
        'custom': 'Personnalisé',
        'api-key-title': 'Clé API',
        'header': 'En-tête',
      },
      insertVarPlaceholder: 'tapez \'/\' pour insérer une variable',
      timeout: {
        title: 'Délai d\'expiration',
        connectLabel: 'Délai de connexion',
        connectPlaceholder: 'Entrez le délai de connexion en secondes',
        readLabel: 'Délai de lecture',
        readPlaceholder: 'Entrez le délai de lecture en secondes',
        writeLabel: 'Délai d\'écriture',
        writePlaceholder: 'Entrez le délai d\'écriture en secondes',
      },
      binaryFileVariable: 'Variable de fichier binaire',
      type: 'Type',
      extractListPlaceholder: 'Entrez l’index de l’élément de liste, tapez \'/\' insérer la variable',
      curl: {
        placeholder: 'Collez la chaîne cURL ici',
        title: 'Importer à partir de cURL',
      },
      verifySSL: {
        title: 'Vérifier le certificat SSL',
        warningTooltip: 'Désactiver la vérification SSL n\'est pas recommandé pour les environnements de production. Cela ne devrait être utilisé que dans le développement ou les tests, car cela rend la connexion vulnérable aux menaces de sécurité telles que les attaques de type \'man-in-the-middle\'.',
      },
    },
    code: {
      inputVars: 'Variables de saisie',
      outputVars: 'Variables de sortie',
      advancedDependencies: 'Dépendances avancées',
      advancedDependenciesTip: 'Ajoutez quelques dépendances préchargées qui prennent plus de temps à consommer ou ne sont pas par défaut ici',
      searchDependencies: 'Rechercher des dépendances',
    },
    templateTransform: {
      inputVars: 'Variables de saisie',
      code: 'Code',
      codeSupportTip: 'Prend en charge uniquement Jinja2',
      outputVars: {
        output: 'Contenu transformé',
      },
    },
    ifElse: {
      if: 'Si',
      else: 'Sinon',
      elseDescription: 'Utilisé pour définir la logique à exécuter lorsque la condition if n\'est pas remplie.',
      and: 'et',
      or: 'ou',
      operator: 'Opérateur',
      notSetVariable: 'Veuillez d\'abord définir la variable',
      comparisonOperator: {
        'contains': 'contient',
        'not contains': 'ne contient pas',
        'start with': 'commence par',
        'end with': 'se termine par',
        'is': 'est',
        'is not': 'n\'est pas',
        'empty': 'est vide',
        'not empty': 'n\'est pas vide',
        'null': 'est nul',
        'not null': 'n\'est pas nul',
        'regex match': 'correspondance regex',
        'in': 'dans',
        'not in': 'pas dans',
        'exists': 'Existe',
        'all of': 'l’ensemble des',
        'not exists': 'n’existe pas',
        'before': 'avant',
        'after': 'après',
      },
      enterValue: 'Entrez la valeur',
      addCondition: 'Ajouter une condition',
      conditionNotSetup: 'Condition NON configurée',
      selectVariable: 'Sélectionner une variable...',
      optionName: {
        video: 'Vidéo',
        image: 'Image',
        audio: 'Audio',
        doc: 'Médecin',
        localUpload: 'Téléchargement local',
        url: 'URL',
      },
      select: 'Choisir',
      addSubVariable: 'Sous-variable',
      condition: 'Condition',
    },
    variableAssigner: {
      title: 'Attribuer des variables',
      outputType: 'Type de sortie',
      varNotSet: 'Variable non définie',
      noVarTip: 'Ajoutez les variables à attribuer',
      type: {
        string: 'Chaîne',
        number: 'Nombre',
        object: 'Objet',
        array: 'Tableau',
      },
      aggregationGroup: 'Groupe d\'agrégation',
      aggregationGroupTip: 'L\'activation de cette fonctionnalité permet à l\'agrégateur de variables d\'agréger plusieurs ensembles de variables.',
      addGroup: 'Ajouter un groupe',
      outputVars: {
        varDescribe: 'Sortie {{groupName}}',
      },
      setAssignVariable: 'Définir la variable à attribuer',
    },
    assigner: {
      'assignedVariable': 'Variable Assignée',
      'writeMode': 'Mode d\'Écriture',
      'writeModeTip': 'Lorsque la VARIABLE ASSIGNÉE est un tableau, le mode d\'ajout ajoute à la fin.',
      'over-write': 'Écraser',
      'append': 'Ajouter',
      'plus': 'Plus',
      'clear': 'Effacer',
      'setVariable': 'Définir Variable',
      'variable': 'Variable',
      'operations': {
        'clear': 'Clair',
        '*=': '*=',
        '-=': '-=',
        'extend': 'Étendre',
        '+=': '+=',
        'over-write': 'Écraser',
        'set': 'Poser',
        'append': 'Ajouter',
        'title': 'Opération',
        '/=': '/=',
        'overwrite': 'Écraser',
        'remove-last': 'Supprimer le dernier',
        'remove-first': 'Retirer le premier',
      },
      'assignedVarsDescription': 'Les variables affectées doivent être accessibles en écriture, telles que des variables de conversation.',
      'noVarTip': 'Cliquez sur le bouton « + » pour ajouter des variables',
      'variables': 'Variables',
      'setParameter': 'Définir le paramètre...',
      'noAssignedVars': 'Aucune variable affectée disponible',
      'varNotSet': 'Variable NON définie',
      'selectAssignedVariable': 'Sélectionner la variable affectée...',
    },
    tool: {
      inputVars: 'Variables de saisie',
      outputVars: {
        text: 'contenu généré par l\'outil',
        files: {
          title: 'fichiers générés par l\'outil',
          type: 'Type de support. Actuellement ne prend en charge que l\'image',
          transfer_method: 'Méthode de transfert. La valeur est remote_url ou local_file',
          url: 'URL de l\'image',
          upload_file_id: 'ID du fichier téléchargé',
        },
        json: 'JSON généré par un outil',
      },
      authorize: 'Autoriser',
    },
    questionClassifiers: {
      model: 'modèle',
      inputVars: 'Variables de saisie',
      outputVars: {
        className: 'Nom de la classe',
        usage: 'Informations sur l\'utilisation du modèle',
      },
      class: 'Classe',
      classNamePlaceholder: 'Écrivez le nom de votre classe',
      advancedSetting: 'Paramètre avancé',
      topicName: 'Nom du sujet',
      topicPlaceholder: 'Écrivez le nom de votre sujet',
      addClass: 'Ajouter une classe',
      instruction: 'Instruction',
      instructionTip: 'Entrez des instructions supplémentaires pour aider le classificateur de questions à mieux comprendre comment catégoriser les questions.',
      instructionPlaceholder: 'Écrivez votre instruction',
    },
    parameterExtractor: {
      inputVar: 'Variable de saisie',
      outputVars: {
        isSuccess: 'Est réussi. En cas de succès, la valeur est 1, en cas d\'échec, la valeur est 0.',
        errorReason: 'Raison de l\'erreur',
        usage: 'Informations sur l\'utilisation du modèle',
      },
      extractParameters: 'Extraire des paramètres',
      importFromTool: 'Importer des outils',
      addExtractParameter: 'Ajouter un paramètre d\'extraction',
      addExtractParameterContent: {
        name: 'Nom',
        namePlaceholder: 'Nom du paramètre d\'extraction',
        type: 'Type',
        typePlaceholder: 'Type de paramètre d\'extraction',
        description: 'Description',
        descriptionPlaceholder: 'Description du paramètre d\'extraction',
        required: 'Requis',
        requiredContent: 'Requis est utilisé uniquement comme référence pour l\'inférence du modèle, et non pour la validation obligatoire de la sortiedu paramètre.',
      },
      extractParametersNotSet: 'Paramètres d\'extraction non configurés',
      instruction: 'Instruction',
      instructionTip: 'Entrez des instructions supplémentaires pour aider l\'extracteur de paramètres à comprendre comment extraire les paramètres.',
      advancedSetting: 'Paramètre avancé',
      reasoningMode: 'Mode de raisonnement',
      reasoningModeTip: 'Vous pouvez choisir le mode de raisonnement approprié en fonction de la capacité du modèle à répondre aux instructions pour les appels de fonction ou les invites.',
    },
    iteration: {
      deleteTitle: 'Supprimer le nœud d\'itération?',
      deleteDesc: 'La suppression du nœud d\'itération supprimera tous les nœuds enfants',
      input: 'Entrée',
      output: 'Variables de sortie',
      iteration_one: '{{count}} Itération',
      iteration_other: '{{count}} Itérations',
      currentIteration: 'Itération actuelle',
      ErrorMethod: {
        operationTerminated: 'Terminé',
        removeAbnormalOutput: 'remove-abnormal-output',
        continueOnError: 'continuer sur l’erreur',
      },
      comma: ',',
      error_one: '{{compte}} Erreur',
      error_other: '{{compte}} Erreurs',
      parallelModeEnableDesc: 'En mode parallèle, les tâches au sein des itérations prennent en charge l’exécution parallèle. Vous pouvez le configurer dans le panneau des propriétés à droite.',
      parallelModeUpper: 'MODE PARALLÈLE',
      parallelPanelDesc: 'En mode parallèle, les tâches de l’itération prennent en charge l’exécution parallèle.',
      MaxParallelismDesc: 'Le parallélisme maximal est utilisé pour contrôler le nombre de tâches exécutées simultanément en une seule itération.',
      errorResponseMethod: 'Méthode de réponse aux erreurs',
      MaxParallelismTitle: 'Parallélisme maximal',
      answerNodeWarningDesc: 'Avertissement en mode parallèle : les nœuds de réponse, les affectations de variables de conversation et les opérations de lecture/écriture persistantes au sein des itérations peuvent provoquer des exceptions.',
      parallelModeEnableTitle: 'Mode parallèle activé',
      parallelMode: 'Mode parallèle',
    },
    note: {
      addNote: 'Ajouter note',
      editor: {
        placeholder: 'Redigez votre note...',
        small: 'Petit',
        medium: 'Moyen',
        large: 'Grand',
        bold: 'Gras',
        italic: 'Italique',
        strikethrough: 'Barré',
        link: 'Lien',
        openLink: 'Ouvrir',
        unlink: 'Annuler le lien',
        enterUrl: 'Entrer l\'URL...',
        invalidUrl: 'URL invalide',
        bulletList: 'Liste à puces',
        showAuthor: 'Afficher l\'auteur',
      },
    },
    docExtractor: {
      outputVars: {
        text: 'Texte extrait',
      },
      learnMore: 'Pour en savoir plus',
      inputVar: 'Variable d’entrée',
      supportFileTypes: 'Types de fichiers de support : {{types}}.',
    },
    listFilter: {
      outputVars: {
        result: 'Filtrer le résultat',
        last_record: 'Dernier enregistrement',
        first_record: 'Premier enregistrement',
      },
      filterCondition: 'État du filtre',
      asc: 'L’ASC',
      inputVar: 'Variable d’entrée',
      filterConditionComparisonValue: 'Valeur de la condition de filtre',
      desc: 'DESC',
      filterConditionComparisonOperator: 'Opérateur de comparaison de l’état des filtres',
      selectVariableKeyPlaceholder: 'Sélectionner la clé de sous-variable',
      limit: 'Haut N',
      orderBy: 'Trier par',
      filterConditionKey: 'Clé de condition de filtre',
      extractsCondition: 'Extraire l’élément N',
    },
    agent: {
      strategy: {
        configureTip: 'Veuillez configurer la stratégie agentique.',
        tooltip: 'Différentes stratégies agentiques déterminent la façon dont le système planifie et exécute les appels d’outils en plusieurs étapes',
        shortLabel: 'Stratégie',
        selectTip: 'Sélectionner la stratégie agentique',
        configureTipDesc: 'Après avoir configuré la stratégie agentique, ce nœud chargera automatiquement les configurations restantes. La stratégie affectera le mécanisme du raisonnement à l’outil en plusieurs étapes.',
        searchPlaceholder: 'Stratégie de recherche agentique',
        label: 'Stratégie agentique',
      },
      pluginInstaller: {
        installing: 'Installation',
        install: 'Installer',
      },
      modelNotInMarketplace: {
        manageInPlugins: 'Gérer dans les plugins',
        desc: 'Ce modèle est installé à partir d’un référentiel local ou GitHub. Veuillez utiliser après l’installation.',
        title: 'Modèle non installé',
      },
      modelNotSupport: {
        title: 'Modèle non pris en charge',
        desc: 'La version du plugin installée ne fournit pas ce modèle.',
        descForVersionSwitch: 'La version du plugin installée ne fournit pas ce modèle. Cliquez pour changer de version.',
      },
      modelSelectorTooltips: {
        deprecated: 'Ce modèle est obsolète',
      },
      outputVars: {
        files: {
          title: 'Fichiers générés par l’agent',
          url: 'URL de l’image',
          transfer_method: 'Méthode de transfert. La valeur est remote_url ou local_file',
          type: 'Type de support. Maintenant, seulement l’image de support',
          upload_file_id: 'Télécharger l’identifiant du fichier',
        },
        json: 'JSON généré par l’agent',
        text: 'Contenu généré par l’agent',
      },
      checkList: {
        strategyNotSelected: 'Stratégie non sélectionnée',
      },
      installPlugin: {
        title: 'Installer le plugin',
        install: 'Installer',
        changelog: 'Journal des modifications',
        cancel: 'Annuler',
        desc: 'Sur le point d’installer le plugin suivant',
      },
      modelNotSelected: 'Modèle non sélectionné',
      configureModel: 'Configurer le modèle',
      pluginNotFoundDesc: 'Ce plugin est installé à partir de GitHub. Veuillez aller dans Plugins pour réinstaller',
      strategyNotSet: 'Stratégie agentique non définie',
      unsupportedStrategy: 'Stratégie non soutenue',
      linkToPlugin: 'Lien vers les plugins',
      toolNotInstallTooltip: '{{tool}} n’est pas installé',
      model: 'modèle',
      learnMore: 'Pour en savoir plus',
      pluginNotInstalled: 'Ce plugin n’est pas installé',
      modelNotInstallTooltip: 'Ce modèle n’est pas installé',
      tools: 'Outils',
      notAuthorized: 'Non autorisé',
      strategyNotInstallTooltip: '{{strategy}} n’est pas installé',
      strategyNotFoundDesc: 'La version du plugin installée ne fournit pas cette stratégie.',
      strategyNotFoundDescAndSwitchVersion: 'La version du plugin installée ne fournit pas cette stratégie. Cliquez pour changer de version.',
      toolbox: 'boîte à outils',
      pluginNotInstalledDesc: 'Ce plugin est installé à partir de GitHub. Veuillez aller dans Plugins pour réinstaller',
      maxIterations: 'Nombre maximal d’itérations',
      toolNotAuthorizedTooltip: '{{outil}} Non autorisé',
    },
    loop: {
      ErrorMethod: {
        operationTerminated: 'Terminé',
        removeAbnormalOutput: 'Supprimer la sortie anormale',
        continueOnError: 'Continuer en cas d\'erreur',
      },
      currentLoop: 'Boucle de courant',
      loopMaxCount: 'Nombre maximum de boucles',
      loop_one: '{{count}} Boucle',
      output: 'Variable de sortie',
      error_other: '{{count}} erreurs',
      loopMaxCountError: 'Veuillez entrer un nombre maximal de boucles valide, compris entre 1 et {{maxCount}}.',
      totalLoopCount: 'Nombre total de boucles : {{count}}',
      initialLoopVariables: 'Variables de boucle initiales',
      breakCondition: 'Condition de terminaison de boucle',
      variableName: 'Nom de Variable',
      finalLoopVariables: 'Variables de boucle finales',
      inputMode: 'Mode d\'entrée',
      setLoopVariables: 'Définir des variables dans la portée de la boucle',
      loop_other: '{{count}} Boucles',
      comma: ',',
      loopNode: 'Nœud de boucle',
      error_one: '{{count}} Erreur',
      errorResponseMethod: 'Méthode de réponse d\'erreur',
      input: 'Entrée',
      currentLoopCount: 'Nombre de boucles actuel : {{count}}',
      deleteDesc: 'Supprimer le nœud de boucle supprimera tous les nœuds enfants.',
      exitConditionTip: 'Un nœud de boucle nécessite au moins une condition de sortie',
      breakConditionTip: 'Seules les variables dans les boucles avec des conditions de terminaison et les variables de conversation peuvent être référencées.',
      loopVariables: 'Variables de boucle',
      deleteTitle: 'Supprimer le nœud de boucle ?',
    },
  },
  tracing: {
    stopBy: 'Arrêté par {{user}}',
  },
  variableReference: {
    noAssignedVars: 'Aucune variable affectée disponible',
    noVarsForOperation: 'Aucune variable n’est disponible pour l’affectation avec l’opération sélectionnée.',
    noAvailableVars: 'Aucune variable disponible',
    assignedVarsDescription: 'Les variables affectées doivent être des variables accessibles en écriture, telles que',
    conversationVars: 'Variables de conversation',
  },
  versionHistory: {
    filter: {
      all: 'Tout',
      reset: 'Réinitialiser le filtre',
      onlyYours: 'Rien que le tien',
      empty: 'Aucune version correspondante trouvée',
      onlyShowNamedVersions: 'Afficher uniquement les versions nommées',
    },
    editField: {
      releaseNotesLengthLimit: 'Les notes de version ne peuvent pas dépasser {{limit}} caractères.',
      title: 'Titre',
      titleLengthLimit: 'Le titre ne peut pas dépasser {{limit}} caractères.',
      releaseNotes: 'Notes de version',
    },
    action: {
      updateSuccess: 'Version mise à jour',
      deleteFailure: 'Échec de la suppression de la version',
      restoreSuccess: 'Version restaurée',
      deleteSuccess: 'Version supprimée',
      updateFailure: 'Échec de la mise à jour de la version',
      restoreFailure: 'Échec de la restauration de la version',
    },
    title: 'Versions',
    releaseNotesPlaceholder: 'Décrivez ce qui a changé',
    nameThisVersion: 'Nommez cette version',
    currentDraft: 'Projet actuel',
    defaultName: 'Version sans titre',
    editVersionInfo: 'Modifier les informations de version',
    restorationTip: 'Après la restauration de la version, le brouillon actuel sera écrasé.',
    deletionTip: 'La suppression est irreversible, veuillez confirmer.',
    latest: 'Dernier',
  },
  debug: {
    noData: {
      description: 'Les résultats de la dernière exécution seront affichés ici',
      runThisNode: 'Exécutez ce nœud',
    },
    variableInspect: {
      trigger: {
        clear: 'Clair',
        cached: 'Afficher les variables mises en cache',
        running: 'État d\'exécution du cache',
        stop: 'Arrête de courir',
        normal: 'Inspection de Variable',
      },
      title: 'Inspection de Variable',
      clearAll: 'Réinitialiser tout',
      envNode: 'Environnement',
      clearNode: 'Effacer la variable mise en cache',
      view: 'Voir le journal',
      systemNode: 'Système',
      reset: 'Réinitialiser à la dernière valeur d\'exécution',
      chatNode: 'Conversation',
      emptyLink: 'En savoir plus',
      edited: 'Édité',
      resetConversationVar: 'Réinitialiser la variable de conversation à la valeur par défaut',
      emptyTip: 'Après avoir dessiné un nœud sur le canevas ou exécuté un nœud étape par étape, vous pouvez voir la valeur actuelle de la variable du nœud dans l\'Inspecteur de Variables.',
    },
    settingsTab: 'Paramètres',
    lastRunTab: 'Dernière Exécution',
  },
}

export default translation
