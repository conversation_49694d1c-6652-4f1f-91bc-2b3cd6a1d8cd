"""fix wrong service-api history

Revision ID: d8e744d88ed6
Revises: 33f5fac87f29
Create Date: 2024-10-09 13:29:23.548498

"""
from alembic import op
from constants import UUID_NIL
import models as models
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd8e744d88ed6'
down_revision = '33f5fac87f29'
branch_labels = None
depends_on = None

# (UTC) release date of v0.9.0
v0_9_0_release_date= '2024-09-29 12:00:00'

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sql = f"""UPDATE
    messages
SET
    parent_message_id = '{UUID_NIL}'
WHERE
    invoke_from = 'service-api'
    AND parent_message_id IS NULL
    AND created_at >= '{v0_9_0_release_date}';"""
    op.execute(sql)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sql = f"""UPDATE
    messages
SET
    parent_message_id = NULL
WHERE
    invoke_from = 'service-api'
    AND parent_message_id = '{UUID_NIL}'
    AND created_at >= '{v0_9_0_release_date}';"""
    op.execute(sql)
    # ### end Alembic commands ###
