const translation = {
  input: 'NHẬP',
  result: 'KẾT QUẢ',
  detail: 'CHI TIẾT',
  tracing: 'THEO DÕI',
  resultPanel: {
    status: 'TRẠNG THÁI',
    time: 'THỜI GIAN',
    tokens: 'TỔNG SỐ TOKEN',
  },
  meta: {
    title: 'DỮ LIỆU META',
    status: 'Trạng thái',
    version: 'Phiên bản',
    executor: 'Người thực thi',
    startTime: 'Thời gian bắt đầu',
    time: 'Thời gian đã trôi qua',
    tokens: 'Tổng số token',
    steps: '<PERSON><PERSON>c bước chạy',
  },
  resultEmpty: {
    title: 'Chạy này chỉ xuất ra định dạng JSON,',
    tipLeft: 'vui lòng truy cập ',
    link: 'bảng chi tiết',
    tipRight: ' xem nó.',
  },
  circularInvocationTip: 'Có lệnh gọi vòng tròn các công cụ/nút trong quy trình làm việc hiện tại.',
  actionLogs: 'Nhật ký hành động',
}

export default translation
