"""add_assistant_app

Revision ID: 3ef9b2b6bee6
Revises: 89c7899ca936
Create Date: 2024-01-05 15:26:25.117551

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '3ef9b2b6bee6'
down_revision = '89c7899ca936'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tool_api_providers',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('name', sa.String(length=40), nullable=False),
    sa.Column('schema', sa.Text(), nullable=False),
    sa.Column('schema_type_str', sa.String(length=40), nullable=False),
    sa.Column('user_id', postgresql.UUID(), nullable=False),
    sa.Column('tenant_id', postgresql.UUID(), nullable=False),
    sa.Column('description_str', sa.Text(), nullable=False),
    sa.Column('tools_str', sa.Text(), nullable=False),
    sa.PrimaryKeyConstraint('id', name='tool_api_provider_pkey')
    )
    op.create_table('tool_builtin_providers',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', postgresql.UUID(), nullable=True),
    sa.Column('user_id', postgresql.UUID(), nullable=False),
    sa.Column('provider', sa.String(length=40), nullable=False),
    sa.Column('encrypted_credentials', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='tool_builtin_provider_pkey'),
    sa.UniqueConstraint('tenant_id', 'provider', name='unique_builtin_tool_provider')
    )
    op.create_table('tool_published_apps',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('app_id', postgresql.UUID(), nullable=False),
    sa.Column('user_id', postgresql.UUID(), nullable=False),
    sa.Column('description', sa.Text(), nullable=False),
    sa.Column('llm_description', sa.Text(), nullable=False),
    sa.Column('query_description', sa.Text(), nullable=False),
    sa.Column('query_name', sa.String(length=40), nullable=False),
    sa.Column('tool_name', sa.String(length=40), nullable=False),
    sa.Column('author', sa.String(length=40), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.ForeignKeyConstraint(['app_id'], ['apps.id'], ),
    sa.PrimaryKeyConstraint('id', name='published_app_tool_pkey'),
    sa.UniqueConstraint('app_id', 'user_id', name='unique_published_app_tool')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('tool_published_apps')
    op.drop_table('tool_builtin_providers')
    op.drop_table('tool_api_providers')
    # ### end Alembic commands ###
