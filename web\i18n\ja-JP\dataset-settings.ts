const translation = {
  title: 'ナレッジベースの設定',
  desc: 'ここではナレッジベースのプロパティと動作方法を変更できます。',
  form: {
    name: 'ナレッジベース名',
    namePlaceholder: 'ナレッジベース名を入力してください',
    nameError: '名前は空にできません',
    desc: 'ナレッジベースの説明',
    descInfo: 'ナレッジベースの内容を概説するための明確なテキストの説明を書いてください。この説明は、複数のナレッジから推論を選択する際の基準として使用されます。',
    descPlaceholder: 'このデータセットの内容を記述してください。詳細に記述することで、AI がデータセットの内容に迅速にアクセスできるようになります。空欄の場合、LangGenius はデフォルトの検索方法を使用します。',
    helpText: '適切なデータセットの説明を作成する方法を学びましょう。',
    descWrite: '良いナレッジベースの説明の書き方を学ぶ。',
    permissions: '権限',
    permissionsOnlyMe: '自分のみ',
    permissionsAllMember: 'すべてのチームメンバー',
    permissionsInvitedMembers: '一部のチームメンバー',
    me: '(あなた)',
    indexMethod: 'インデックス方法',
    indexMethodHighQuality: '高品質',
    indexMethodHighQualityTip: 'より正確な検索のため、埋め込みモデルを呼び出してドキュメントを処理することで、LLM は高品質な回答を生成できます。',
    upgradeHighQualityTip: '高品質モードにアップグレードすると、経済的モードには戻せません。',
    indexMethodEconomy: '経済的',
    indexMethodEconomyTip: 'チャンクあたり 10 個のキーワードを検索に使用します。トークンは消費しませんが、検索精度は低下します。',
    embeddingModel: '埋め込みモデル',
    embeddingModelTip: '埋め込みモデルを変更するには、',
    embeddingModelTipLink: '設定',
    retrievalSetting: {
      title: '検索設定',
      method: '検索方法',
      learnMore: '詳細はこちら',
      description: ' 検索方法についての詳細',
      longDescription: ' 検索方法についての詳細については、いつでもナレッジベースの設定で変更できます。',
    },
    save: '保存',
    externalKnowledgeID: '外部ナレッジベース ID',
    retrievalSettings: '取得設定',
    externalKnowledgeAPI: '外部ナレッジベース API',
    indexMethodChangeToEconomyDisabledTip: 'HQ から ECO へのダウングレードはできません。',
    searchModel: 'モデル検索',
  },
}

export default translation
