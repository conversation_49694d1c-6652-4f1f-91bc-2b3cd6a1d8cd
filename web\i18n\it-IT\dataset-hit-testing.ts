const translation = {
  title: 'Test di Recupero',
  desc: '<PERSON>a l\'effetto di recupero della Conoscenza basato sul testo di query fornito.',
  dateTimeFormat: 'MM/DD/YYYY hh:mm A',
  recents: 'Recenti',
  table: {
    header: {
      source: '<PERSON><PERSON>',
      text: 'Testo',
      time: 'Or<PERSON>',
    },
  },
  input: {
    title: 'Testo di origine',
    placeholder:
      'Per favore inserisci un testo, si consiglia una frase dichiarativa breve.',
    countWarning: 'Fino a 200 caratteri.',
    indexWarning: 'Solo Conoscenza di alta qualità.',
    testing: 'Test in corso',
  },
  hit: {
    title: 'PARAGRAFI RECUPERATI',
    emptyTip: 'I risultati del Test di Recupero verranno mostrati qui',
  },
  noRecentTip: 'Nessun risultato di query recente qui',
  viewChart: 'Visualizza GRAFICO VETTORIALE',
  settingTitle: 'Impostazione di recupero',
  viewDetail: 'vedi dettagli',
  chunkDetail: '<PERSON>taglio pezzo',
  hitChunks: 'Premi {{num}} blocchi figlio',
  open: 'Aperto',
  keyword: 'Parole chiavi',
  records: 'Archivio',
}

export default translation
