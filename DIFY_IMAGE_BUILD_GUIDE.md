# Dify 镜像构建与发布指南

## 概述

本指南将帮助您在修改 Dify 项目代码后，构建和发布自定义的 Docker 镜像。

## 项目结构

Dify 项目包含两个主要组件：
- **API 服务** (`api/`) - Python Flask 后端
- **Web 服务** (`web/`) - Next.js 前端

## 构建方法

### 方法一：使用 Makefile（推荐）

项目根目录提供了便捷的 Makefile 命令：

```bash
# 构建 API 镜像
make build-api

# 构建 Web 镜像  
make build-web

# 构建所有镜像
make build-all

# 推送镜像到 Docker Hub
make push-api
make push-web
make push-all

# 构建并推送所有镜像
make build-push-all
```

### 方法二：直接使用 Docker 命令

#### 构建 API 镜像

```bash
# 在项目根目录执行
docker build -t your-registry/dify-api:your-tag ./api
```

#### 构建 Web 镜像

```bash
# 在项目根目录执行
docker build -t your-registry/dify-web:your-tag ./web
```

### 方法三：使用 Docker Compose

1. 修改 `docker/docker-compose.yaml` 中的镜像名称：

```yaml
services:
  api:
    image: your-registry/dify-api:your-tag  # 修改这里
    # ... 其他配置

  web:
    image: your-registry/dify-web:your-tag  # 修改这里
    # ... 其他配置
```

2. 构建镜像：

```bash
cd docker
docker compose build api web
```

## 自定义镜像标签

### 修改 Makefile 中的配置

编辑项目根目录的 `Makefile`：

```makefile
# Variables
DOCKER_REGISTRY=your-registry  # 修改为你的镜像仓库
WEB_IMAGE=$(DOCKER_REGISTRY)/dify-web
API_IMAGE=$(DOCKER_REGISTRY)/dify-api
VERSION=your-version  # 修改版本号
```

### 使用环境变量

```bash
export DOCKER_REGISTRY=your-registry
export VERSION=your-version
make build-all
```

## 推送镜像

### 登录 Docker Hub

```bash
docker login
# 或者指定仓库
docker login your-registry.com
```

### 推送镜像

```bash
# 使用 Makefile
make push-all

# 或直接推送
docker push your-registry/dify-api:your-tag
docker push your-registry/dify-web:your-tag
```

## 部署自定义镜像

### 方法一：修改 docker-compose.yaml

1. 编辑 `docker/docker-compose.yaml`：

```yaml
services:
  api:
    image: your-registry/dify-api:your-tag
    # ... 其他配置保持不变

  web:
    image: your-registry/dify-web:your-tag
    # ... 其他配置保持不变
```

2. 重新部署：

```bash
cd docker
docker compose down
docker compose up -d
```

### 方法二：使用环境变量

1. 在 `docker/.env` 文件中添加：

```env
# 自定义镜像配置
CUSTOM_API_IMAGE=your-registry/dify-api:your-tag
CUSTOM_WEB_IMAGE=your-registry/dify-web:your-tag
```

2. 修改 `docker-compose.yaml` 使用环境变量：

```yaml
services:
  api:
    image: ${CUSTOM_API_IMAGE:-langgenius/dify-api:1.6.0}
    # ... 其他配置

  web:
    image: ${CUSTOM_WEB_IMAGE:-langgenius/dify-web:1.6.0}
    # ... 其他配置
```

## 多架构构建

项目支持构建多架构镜像（AMD64 和 ARM64）：

### 使用 Docker Buildx

```bash
# 创建并使用 buildx 构建器
docker buildx create --name mybuilder --use

# 构建多架构镜像
docker buildx build --platform linux/amd64,linux/arm64 \
  -t your-registry/dify-api:your-tag \
  --push ./api

docker buildx build --platform linux/amd64,linux/arm64 \
  -t your-registry/dify-web:your-tag \
  --push ./web
```

### 使用 GitHub Actions

项目已配置 GitHub Actions 自动构建多架构镜像，参考 `.github/workflows/build-push.yml`。

## 本地开发镜像

### 构建开发版本

```bash
# API 开发镜像
docker build -t dify-api-dev ./api

# Web 开发镜像
docker build -t dify-web-dev ./web
```

### 运行开发环境

```bash
# 启动中间件
cd docker
docker compose -f docker-compose.middleware.yaml up -d

# 运行 API 开发镜像
docker run -it --rm \
  -p 5001:5001 \
  -e CONSOLE_API_URL=http://127.0.0.1:5001 \
  -e CONSOLE_WEB_URL=http://127.0.0.1:3000 \
  dify-api-dev

# 运行 Web 开发镜像
docker run -it --rm \
  -p 3000:3000 \
  -e CONSOLE_API_URL=http://127.0.0.1:5001 \
  -e APP_API_URL=http://127.0.0.1:5001 \
  dify-web-dev
```

## 镜像优化建议

### 1. 使用多阶段构建

项目已使用多阶段构建优化镜像大小：
- `api/Dockerfile` 使用 `base`、`packages`、`production` 阶段
- `web/Dockerfile` 使用 `base`、`packages`、`builder`、`production` 阶段

### 2. 缓存优化

```bash
# 使用 BuildKit 缓存
DOCKER_BUILDKIT=1 docker build \
  --build-arg BUILDKIT_INLINE_CACHE=1 \
  -t your-registry/dify-api:your-tag ./api
```

### 3. 镜像标签策略

建议使用以下标签策略：
- `latest` - 最新稳定版本
- `v1.6.0` - 语义化版本号
- `commit-sha` - Git 提交哈希
- `dev-YYYYMMDD` - 开发版本

## 故障排除

### 常见问题

1. **构建失败**
   - 检查 Dockerfile 语法
   - 确认依赖文件存在（pyproject.toml, package.json 等）
   - 查看构建日志

2. **推送失败**
   - 确认已登录 Docker Hub
   - 检查镜像仓库权限
   - 验证镜像标签格式

3. **运行失败**
   - 检查环境变量配置
   - 确认端口映射正确
   - 查看容器日志

### 调试命令

```bash
# 查看镜像信息
docker images | grep dify

# 查看容器日志
docker logs <container-name>

# 进入容器调试
docker exec -it <container-name> /bin/bash

# 检查镜像层
docker history <image-name>
```

## 最佳实践

1. **版本管理**：使用语义化版本号
2. **安全扫描**：定期扫描镜像漏洞
3. **镜像大小**：优化镜像大小，减少传输时间
4. **文档更新**：及时更新镜像说明文档
5. **测试验证**：部署前进行充分测试

## 相关文件

- `api/Dockerfile` - API 服务镜像构建文件
- `web/Dockerfile` - Web 服务镜像构建文件
- `Makefile` - 构建脚本
- `docker/docker-compose.yaml` - 部署配置
- `.github/workflows/build-push.yml` - CI/CD 配置

## 参考链接

- [Docker 官方文档](https://docs.docker.com/)
- [Docker Compose 文档](https://docs.docker.com/compose/)
- [Dify 官方文档](https://docs.dify.ai/) 