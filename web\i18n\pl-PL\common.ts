const translation = {
  api: {
    success: '<PERSON>k<PERSON>',
    actionSuccess: '<PERSON>k<PERSON><PERSON> powiodła się',
    saved: '<PERSON><PERSON>isan<PERSON>',
    create: '<PERSON>t<PERSON><PERSON><PERSON>',
    remove: '<PERSON><PERSON><PERSON><PERSON>',
  },
  operation: {
    create: '<PERSON>t<PERSON><PERSON><PERSON>',
    confirm: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    cancel: 'Anulu<PERSON>',
    clear: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    save: '<PERSON><PERSON><PERSON><PERSON>',
    saveAndEnable: '<PERSON>ap<PERSON>z i Włącz',
    edit: 'Edytuj',
    add: 'Doda<PERSON>',
    added: '<PERSON><PERSON><PERSON>',
    refresh: 'Od<PERSON><PERSON><PERSON><PERSON>',
    reset: 'Resetuj',
    search: 'Szukaj',
    change: '<PERSON><PERSON><PERSON>',
    remove: '<PERSON>u<PERSON>',
    send: 'Wyślij',
    copy: 'Kopiuj',
    lineBreak: '<PERSON><PERSON><PERSON><PERSON> linii',
    sure: 'Jestem pewien',
    download: 'Pobierz',
    delete: 'Usuń',
    settings: 'Ustawienia',
    setup: 'Konfiguruj',
    getForFree: 'Zdobąd<PERSON> za darmo',
    reload: '<PERSON><PERSON><PERSON>ładuj',
    ok: 'OK',
    log: '<PERSON><PERSON><PERSON><PERSON>',
    learnMore: '<PERSON><PERSON><PERSON> się więcej',
    params: 'Parametry',
    duplicate: '<PERSON><PERSON><PERSON>uj',
    rename: '<PERSON><PERSON><PERSON> nazwę',
    audioSourceUnavailable: 'AudioSource jest niedostępny',
    copyImage: 'Kopiuj obraz',
    openInNewTab: 'Otwórz w nowej karcie',
    zoomIn: 'Powiększenie',
    zoomOut: 'Pomniejszanie',
    saveAndRegenerate: 'Zapisywanie i regeneracja fragmentów podrzędnych',
    view: 'Widok',
    regenerate: 'Ponownie wygenerować',
    viewMore: 'ZOBACZ WIĘCEJ',
    close: 'Zamykać',
    submit: 'Prześlij',
    skip: 'Statek',
    imageCopied: 'Skopiowany obraz',
    deleteApp: 'Usuń aplikację',
    copied: 'Kopiowane',
    in: 'w',
    viewDetails: 'Wyświetl szczegóły',
    format: 'Format',
    downloadFailed: 'Pobieranie nie powiodło się. Proszę spróbować ponownie później.',
    more: 'Więcej',
    downloadSuccess: 'Pobieranie zakończone.',
    deSelectAll: 'Odznacz wszystkie',
    selectAll: 'Zaznacz wszystkie',
  },
  placeholder: {
    input: 'Proszę wprowadzić',
    select: 'Proszę wybrać',
  },
  voice: {
    language: {
      zhHans: 'Chiński',
      zhHant: 'Chiński tradycyjny',
      enUS: 'Angielski',
      deDE: 'Niemiecki',
      frFR: 'Francuski',
      esES: 'Hiszpański',
      itIT: 'Włoski',
      thTH: 'Tajski',
      idID: 'Indonezyjski',
      jaJP: 'Japoński',
      koKR: 'Koreański',
      ptBR: 'Portugalski',
      ruRU: 'Rosyjski',
      ukUA: 'Ukraiński',
      viVN: 'Wietnamski',
      plPL: 'Polski',
      roRO: 'Rumuński',
      hiIN: 'Hindi',
      trTR: 'Turecki',
      faIR: 'Perski',
    },
  },
  unit: {
    char: 'znaki',
  },
  actionMsg: {
    noModification: 'W tej chwili brak zmian.',
    modifiedSuccessfully: 'Zmodyfikowano pomyślnie',
    modifiedUnsuccessfully: 'Nie udało się zmodyfikować',
    copySuccessfully: 'Skopiowano pomyślnie',
    paySucceeded: 'Płatność zakończona sukcesem',
    payCancelled: 'Płatność anulowana',
    generatedSuccessfully: 'Wygenerowano pomyślnie',
    generatedUnsuccessfully: 'Nie udało się wygenerować',
  },
  model: {
    params: {
      temperature: 'Temperatura',
      temperatureTip:
        'Kontroluje przypadkowość: obniżenie powoduje mniej przypadkowych uzupełnień. Gdy temperatura zbliża się do zera, model staje się deterministyczny i powtarzalny.',
      top_p: 'Top P',
      top_pTip:
        'Kontroluje różnorodność poprzez próbkowanie jądra: 0,5 oznacza, że rozważane są połowa wszystkich opcji ważonych prawdopodobieństwem.',
      presence_penalty: 'Kara za obecność',
      presence_penaltyTip:
        'Jak bardzo karać nowe tokeny w zależności od tego, czy pojawiły się już w tekście.\nZwiększa prawdopodobieństwo, że model zacznie rozmawiać o nowych tematach.',
      frequency_penalty: 'Kara za częstotliwość',
      frequency_penaltyTip:
        'Jak bardzo karać nowe tokeny bazując na ich dotychczasowej częstotliwości w tekście.\nZmniejsza prawdopodobieństwo, że model będzie powtarzał tę samą linię dosłownie.',
      max_tokens: 'Maksymalna liczba tokenów',
      max_tokensTip:
        'Służy do ograniczania maksymalnej długości odpowiedzi w tokenach. \nWiększe wartości mogą ograniczyć miejsce na słowa wstępne, dzienniki rozmów i Wiedzę. \nZaleca się ustawienie go poniżej dwóch trzecich\ngpt-4-1106-preview, gpt-4-vision-preview maksymalna liczba tokenów (input 128k output 4k)',
      maxTokenSettingTip:
        'Twoje ustawienie maksymalnej liczby tokenów jest wysokie, potencjalnie ograniczając miejsce na monity, zapytania i dane. Rozważ ustawienie go poniżej 2/3.',
      setToCurrentModelMaxTokenTip:
        'Maksymalna liczba tokenów została zaktualizowana do 80% maksymalnej liczby tokenów obecnego modelu {{maxToken}}.',
      stop_sequences: 'Sekwencje zatrzymujące',
      stop_sequencesTip:
        'Do czterech sekwencji, w których API przestanie generować dalsze tokeny. Zwrócony tekst nie będzie zawierał sekwencji zatrzymującej.',
      stop_sequencesPlaceholder: 'Wpisz sekwencję i naciśnij Tab',
    },
    tone: {
      Creative: 'Kreatywny',
      Balanced: 'Zrównoważony',
      Precise: 'Precyzyjny',
      Custom: 'Niestandardowy',
    },
    addMoreModel: 'Przejdź do ustawień, aby dodać więcej modeli',
    settingsLink: 'Ustawienia dostawcy modelu',
    capabilities: 'Możliwości multimodalne',
  },
  menus: {
    status: 'beta',
    explore: 'Eksploruj',
    apps: 'Studio',
    plugins: 'Pluginy',
    pluginsTips:
      'Integruj pluginy stron trzecich lub twórz pluginy AI kompatybilne z ChatGPT.',
    datasets: 'Wiedza',
    datasetsTips:
      'NADCHODZI: Importuj swoje własne dane tekstowe lub wpisuj dane w czasie rzeczywistym przez Webhook, aby wzmocnić kontekst LLM.',
    newApp: 'Nowa aplikacja',
    newDataset: 'Utwórz Wiedzę',
    tools: 'Narzędzia',
    exploreMarketplace: 'Zapoznaj się z Marketplace',
    appDetail: 'Szczegóły aplikacji',
    account: 'klient',
  },
  userProfile: {
    settings: 'Ustawienia',
    emailSupport: 'Wsparcie e-mail',
    workspace: 'Przestrzeń robocza',
    createWorkspace: 'Utwórz przestrzeń roboczą',
    helpCenter: 'Pomoc',
    communityFeedback: 'Opinie',
    roadmap: 'Plan działania',
    community: 'Społeczność',
    about: 'O',
    logout: 'Wyloguj się',
    support: 'Wsparcie',
    github: 'GitHub',
    compliance: 'Zgodność',
  },
  settings: {
    accountGroup: 'KONTO',
    workplaceGroup: 'PRZESTRZEŃ ROBOCZA',
    account: 'Moje konto',
    members: 'Członkowie',
    billing: 'Rozliczenia',
    integrations: 'Integracje',
    language: 'Język',
    provider: 'Dostawca modelu',
    dataSource: 'Źródło danych',
    plugin: 'Pluginy',
    apiBasedExtension: 'Rozszerzenie API',
    generalGroup: 'OGÓLNE',
  },
  account: {
    avatar: 'Awatar',
    name: 'Nazwa',
    email: 'Email',
    password: 'Hasło',
    passwordTip:
      'Możesz ustawić stałe hasło, jeśli nie chcesz używać tymczasowych kodów logowania',
    setPassword: 'Ustaw hasło',
    resetPassword: 'Zresetuj hasło',
    currentPassword: 'Obecne hasło',
    newPassword: 'Nowe hasło',
    confirmPassword: 'Potwierdź hasło',
    notEqual: 'Dwa hasła są różne.',
    langGeniusAccount: 'Konto Dify',
    langGeniusAccountTip: 'Twoje konto Dify i powiązane dane użytkownika.',
    editName: 'Edytuj nazwę',
    showAppLength: 'Pokaż {{length}} aplikacje',
    delete: 'Usuń konto',
    deleteTip: 'Usunięcie konta spowoduje trwałe usunięcie wszystkich danych i nie będzie można ich odzyskać.',
    deleteConfirmTip: 'Aby potwierdzić, wyślij następujące informacje z zarejestrowanego adresu e-mail na adres ',
    myAccount: 'Moje konto',
    studio: 'Dify Studio',
    account: 'Rachunek',
    deletePrivacyLinkTip: 'Aby uzyskać więcej informacji o tym, jak postępujemy z Twoimi danymi, zapoznaj się z naszą',
    deletePrivacyLink: 'Polityka prywatności.',
    deleteSuccessTip: 'Twoje konto potrzebuje czasu na dokończenie usuwania. Wyślemy Ci wiadomość e-mail, gdy wszystko będzie gotowe.',
    deleteLabel: 'Aby potwierdzić, wpisz poniżej swój adres e-mail',
    deletePlaceholder: 'Podaj swój adres e-mail',
    sendVerificationButton: 'Wyślij kod weryfikacyjny',
    verificationLabel: 'Kod weryfikacyjny',
    verificationPlaceholder: 'Wklej 6-cyfrowy kod',
    permanentlyDeleteButton: 'Trwale usuń konto',
    feedbackTitle: 'Sprzężenie zwrotne',
    feedbackLabel: 'Powiedz nam, dlaczego usunąłeś swoje konto?',
    feedbackPlaceholder: 'Fakultatywny',
    workspaceIcon: 'Ikona robocza',
    workspaceName: 'Nazwa miejsca pracy',
    editWorkspaceInfo: 'Edytuj informacje o przestrzeni roboczej',
  },
  members: {
    team: 'Zespół',
    invite: 'Dodaj',
    name: 'NAZWA',
    lastActive: 'OSTATNIA AKTYWNOŚĆ',
    role: 'ROLE',
    pending: 'Oczekujący...',
    owner: 'Właściciel',
    admin: 'Admin',
    adminTip: 'Może tworzyć aplikacje i zarządzać ustawieniami zespołu',
    normal: 'Normalny',
    normalTip: 'Może tylko korzystać z aplikacji, nie może tworzyć aplikacji',
    editor: 'Edytor',
    editorTip: 'Może tworzyć i edytować aplikacje, ale nie zarządzać ustawieniami zespołu',
    inviteTeamMember: 'Dodaj członka zespołu',
    inviteTeamMemberTip:
      'Mogą uzyskać bezpośredni dostęp do danych Twojego zespołu po zalogowaniu.',
    emailNotSetup: 'Serwer poczty nie jest skonfigurowany, więc nie można wysyłać zaproszeń e-mail. Proszę powiadomić użytkowników o linku do zaproszenia, który zostanie wydany po zaproszeniu.',
    email: 'Email',
    emailInvalid: 'Nieprawidłowy format e-maila',
    emailPlaceholder: 'Proszę podać adresy e-mail',
    sendInvite: 'Wyślij zaproszenie',
    invitedAsRole: 'Zaproszony jako użytkownik typu {{role}}',
    invitationSent: 'Zaproszenie wysłane',
    invitationSentTip:
      'Zaproszenie zostało wysłane, a oni mogą zalogować się do Dify, aby uzyskać dostęp do danych Twojego zespołu.',
    invitationLink: 'Link zaproszenia',
    failedInvitationEmails: 'Poniższe osoby nie zostały pomyślnie zaproszone',
    ok: 'OK',
    removeFromTeam: 'Usuń z zespołu',
    removeFromTeamTip: 'Usunie dostęp do zespołu',
    setAdmin: 'Ustaw jako administratora',
    setMember: 'Ustaw jako zwykłego członka',
    setEditor: 'Ustaw jako edytora',
    disInvite: 'Anuluj zaproszenie',
    deleteMember: 'Usuń członka',
    you: '(Ty)',
    datasetOperatorTip: 'Może zarządzać tylko bazą wiedzy',
    setBuilder: 'Ustaw jako budowniczego',
    builder: 'Budowniczy',
    builderTip: 'Może tworzyć i edytować własne aplikacje',
    datasetOperator: 'Wiedza Admin',
  },
  integrations: {
    connected: 'Połączony',
    google: 'Google',
    googleAccount: 'Zaloguj się przy użyciu konta Google',
    github: 'GitHub',
    githubAccount: 'Zaloguj się przy użyciu konta GitHub',
    connect: 'Połącz',
  },
  language: {
    displayLanguage: 'Język interfejsu',
    timezone: 'Strefa czasowa',
  },
  provider: {
    apiKey: 'Klucz API',
    enterYourKey: 'Wprowadź tutaj swój klucz API',
    invalidKey: 'Nieprawidłowy klucz API OpenAI',
    validatedError: 'Weryfikacja nie powiodła się: ',
    validating: 'Weryfikowanie klucza...',
    saveFailed: 'Zapis klucza API nie powiódł się',
    apiKeyExceedBill: 'Ten KLUCZ API nie ma dostępnych limitów, przeczytaj',
    addKey: 'Dodaj klucz',
    comingSoon: 'Już wkrótce',
    editKey: 'Edytuj',
    invalidApiKey: 'Nieprawidłowy klucz API',
    azure: {
      apiBase: 'Podstawa API',
      apiBasePlaceholder:
        'Adres URL podstawowy Twojego końcowego punktu Azure OpenAI.',
      apiKey: 'Klucz API',
      apiKeyPlaceholder: 'Wprowadź tutaj swój klucz API',
      helpTip: 'Dowiedz się więcej o usłudze Azure OpenAI',
    },
    openaiHosted: {
      openaiHosted: 'Hostowany OpenAI',
      onTrial: 'NA PROBĘ',
      exhausted: 'WYCZERPANY LIMIT',
      desc: 'Usługa hostowania OpenAI dostarczana przez Dify pozwala korzystać z modeli takich jak GPT-3.5. Przed wyczerpaniem limitu próbnego należy skonfigurować inne dostawców modeli.',
      callTimes: 'Czasy wywołań',
      usedUp: 'Limit próbny został wyczerpany. Dodaj własnego dostawcę modeli.',
      useYourModel: 'Aktualnie używany jest własny dostawca modeli.',
      close: 'Zamknij',
    },
    anthropicHosted: {
      anthropicHosted: 'Anthropic Claude',
      onTrial: 'NA PROBĘ',
      exhausted: 'WYCZERPANY LIMIT',
      desc: 'Potężny model, który doskonale sprawdza się w szerokim spektrum zadań, od zaawansowanego dialogu i generowania treści twórczych po szczegółowe instrukcje.',
      callTimes: 'Czasy wywołań',
      usedUp: 'Limit próbny został wyczerpany. Dodaj własnego dostawcę modeli.',
      useYourModel: 'Aktualnie używany jest własny dostawca modeli.',
      close: 'Zamknij',
      trialQuotaTip: 'Twój limit próbny Anthropic wygaśnie w dniu 11.03.2025 i nie będzie już dostępny po tym czasie. Prosimy o skorzystanie z niego w odpowiednim czasie.',
    },
    anthropic: {
      using: 'Zdolność do osadzania jest używana',
      enableTip:
        'Aby włączyć model Anthropica, musisz najpierw powiązać się z usługą OpenAI lub Azure OpenAI.',
      notEnabled: 'Nie włączono',
      keyFrom: 'Pobierz swój klucz API od Anthropic',
    },
    encrypted: {
      front: 'Twój KLUCZ API będzie szyfrowany i przechowywany za pomocą',
      back: ' technologii.',
    },
  },
  modelProvider: {
    notConfigured:
      'Systemowy model nie został jeszcze w pełni skonfigurowany, co może skutkować niedostępnością niektórych funkcji.',
    systemModelSettings: 'Ustawienia modelu systemowego',
    systemModelSettingsLink:
      'Dlaczego konieczne jest skonfigurowanie modelu systemowego?',
    selectModel: 'Wybierz swój model',
    setupModelFirst: 'Proszę najpierw skonfigurować swój model',
    systemReasoningModel: {
      key: 'Model wnioskowania systemowego',
      tip: 'Ustaw domyślny model wnioskowania do użytku przy tworzeniu aplikacji, a także cechy takie jak generowanie nazw dialogów i sugestie następnego pytania będą również korzystać z domyślnego modelu wnioskowania.',
    },
    embeddingModel: {
      key: 'Model osadzania',
      tip: 'Ustaw domyślny model do przetwarzania osadzania dokumentów wiedzy; zarówno pozyskiwanie, jak i importowanie wiedzy wykorzystują ten model osadzania do przetwarzania wektorowego. Zmiana spowoduje niezgodność wymiarów wektorów między importowaną wiedzą a pytaniem, co skutkować będzie niepowodzeniem w pozyskiwaniu. Aby uniknąć niepowodzeń, prosimy nie zmieniać tego modelu dowolnie.',
      required: 'Model osadzania jest wymagany',
    },
    speechToTextModel: {
      key: 'Model mowy na tekst',
      tip: 'Ustaw domyślny model do przetwarzania mowy na tekst w rozmowach.',
    },
    ttsModel: {
      key: 'Model tekstu na mowę',
      tip: 'Ustaw domyślny model dla konwersji tekstu na mowę w rozmowach.',
    },
    rerankModel: {
      key: 'Model ponownego rankingu',
      tip: 'Model ponownego rankingu zmieni kolejność listy dokumentów kandydatów na podstawie semantycznego dopasowania z zapytaniem użytkownika, poprawiając wyniki rankingu semantycznego',
    },
    quota: 'Limit',
    searchModel: 'Model wyszukiwania',
    noModelFound: 'Nie znaleziono modelu dla {{model}}',
    models: 'Modele',
    showMoreModelProvider: 'Pokaż więcej dostawców modeli',
    selector: {
      tip: 'Ten model został usunięty. Proszę dodać model lub wybrać inny model.',
      emptyTip: 'Brak dostępnych modeli',
      emptySetting: 'Przejdź do ustawień, aby skonfigurować',
      rerankTip: 'Proszę skonfigurować model ponownego rankingu',
    },
    card: {
      quota: 'LIMIT',
      onTrial: 'Na próbę',
      paid: 'Płatny',
      quotaExhausted: 'Wyczerpany limit',
      callTimes: 'Czasy wywołań',
      tokens: 'Tokeny',
      buyQuota: 'Kup limit',
      priorityUse: 'Używanie z priorytetem',
      removeKey: 'Usuń klucz API',
      tip: 'Priorytet zostanie nadany płatnemu limitowi. Po wyczerpaniu limitu próbnego zostanie użyty limit płatny.',
    },
    item: {
      deleteDesc:
        '{{modelName}} są używane jako modele wnioskowania systemowego. Niektóre funkcje mogą nie być dostępne po usunięciu. Proszę potwierdź.',
      freeQuota: 'LIMIT GRATIS',
    },
    addApiKey: 'Dodaj swój klucz API',
    invalidApiKey: 'Nieprawidłowy klucz API',
    encrypted: {
      front: 'Twój KLUCZ API będzie szyfrowany i przechowywany za pomocą',
      back: ' technologii.',
    },
    freeQuota: {
      howToEarn: 'Jak zdobyć',
    },
    addMoreModelProvider: 'DODAJ WIĘCEJ DOSTAWCÓW MODELI',
    addModel: 'Dodaj model',
    modelsNum: '{{num}} Modele',
    showModels: 'Pokaż modele',
    showModelsNum: 'Pokaż {{num}} modele',
    collapse: 'Zwiń',
    config: 'Konfiguracja',
    modelAndParameters: 'Model i parametry',
    model: 'Model',
    featureSupported: '{{feature}} obsługiwane',
    callTimes: 'Czasy wywołań',
    credits: 'Kredyty wiadomości',
    buyQuota: 'Kup limit',
    getFreeTokens: 'Odbierz darmowe tokeny',
    priorityUsing: 'Priorytetyzacja użycia',
    deprecated: 'Przestarzałe',
    confirmDelete: 'potwierdzić usunięcie?',
    quotaTip: 'Pozostałe dostępne darmowe tokeny',
    loadPresets: 'Załaduj ustawienia wstępne',
    parameters: 'PARAMETRY',
    apiKey: 'KLUCZ-API',
    loadBalancing: 'Równoważenie obciążenia',
    defaultConfig: 'Domyślna konfiguracja',
    providerManagedDescription: 'Użyj pojedynczego zestawu poświadczeń dostarczonych przez dostawcę modelu.',
    loadBalancingHeadline: 'Równoważenie obciążenia',
    modelHasBeenDeprecated: 'Ten model jest przestarzały',
    loadBalancingDescription: 'Zmniejsz presję dzięki wielu zestawom poświadczeń.',
    providerManaged: 'Zarządzany przez dostawcę',
    upgradeForLoadBalancing: 'Uaktualnij swój plan, aby włączyć równoważenie obciążenia.',
    apiKeyStatusNormal: 'Stan APIKey jest normalny',
    loadBalancingLeastKeyWarning: 'Aby włączyć równoważenie obciążenia, muszą być włączone co najmniej 2 klucze.',
    loadBalancingInfo: 'Domyślnie równoważenie obciążenia używa strategii działania okrężnego. Jeśli zostanie uruchomione ograniczenie szybkości, zostanie zastosowany 1-minutowy okres odnowienia.',
    configLoadBalancing: 'Równoważenie obciążenia konfiguracji',
    editConfig: 'Edytuj konfigurację',
    addConfig: 'Dodaj konfigurację',
    apiKeyRateLimit: 'Osiągnięto limit szybkości, dostępny po {{sekund}}s',
    installProvider: 'Instalowanie dostawców modeli',
    emptyProviderTip: 'Najpierw zainstaluj dostawcę modeli.',
    discoverMore: 'Dowiedz się więcej w',
    toBeConfigured: 'Do skonfigurowania',
    configureTip: 'Konfigurowanie klucza interfejsu API lub dodawanie modelu do użycia',
    emptyProviderTitle: 'Dostawca modelu nie jest skonfigurowany',
  },
  dataSource: {
    add: 'Dodaj źródło danych',
    connect: 'Połącz',
    notion: {
      title: 'Notion',
      description: 'Korzystanie z Notion jako źródła danych dla Wiedzy.',
      connectedWorkspace: 'Połączona przestrzeń robocza',
      addWorkspace: 'Dodaj przestrzeń roboczą',
      connected: 'Połączono',
      disconnected: 'Rozłączono',
      changeAuthorizedPages: 'Zmień uprawnione strony',
      pagesAuthorized: 'Strony autoryzowane',
      sync: 'Synchronizuj',
      remove: 'Usuń',
      selector: {
        pageSelected: 'Zaznaczone strony',
        searchPages: 'Szukaj stron...',
        noSearchResult: 'Brak wyników wyszukiwania',
        addPages: 'Dodaj strony',
        preview: 'PODGLĄD',
      },
    },
    website: {
      active: 'Aktywny',
      with: 'Z',
      title: 'Strona internetowa',
      description: 'Importuj zawartość ze stron internetowych za pomocą robota indeksującego.',
      configuredCrawlers: 'Skonfigurowane roboty indeksujące',
      inactive: 'Nieaktywny',
    },
    configure: 'Konfigurować',
  },
  plugin: {
    serpapi: {
      apiKey: 'Klucz API',
      apiKeyPlaceholder: 'Wprowadź swój klucz API',
      keyFrom: 'Pobierz swój klucz SerpAPI ze strony konta SerpAPI',
    },
  },
  apiBasedExtension: {
    title:
      'Rozszerzenia oparte na interfejsie API zapewniają scentralizowane zarządzanie interfejsami API, upraszczając konfigurację dla łatwego użytkowania w aplikacjach Dify.',
    link: 'Dowiedz się, jak opracować własne rozszerzenie interfejsu API.',
    add: 'Dodaj rozszerzenie interfejsu API',
    selector: {
      title: 'Rozszerzenie interfejsu API',
      placeholder: 'Wybierz rozszerzenie interfejsu API',
      manage: 'Zarządzaj rozszerzeniem interfejsu API',
    },
    modal: {
      title: 'Dodaj rozszerzenie interfejsu API',
      editTitle: 'Edytuj rozszerzenie interfejsu API',
      name: {
        title: 'Nazwa',
        placeholder: 'Proszę wprowadź nazwę',
      },
      apiEndpoint: {
        title: 'Koniec API',
        placeholder: 'Proszę wprowadź koniec API',
      },
      apiKey: {
        title: 'Klucz API',
        placeholder: 'Proszę wprowadź klucz API',
        lengthError: 'Długość klucza API nie może być mniejsza niż 5 znaków',
      },
    },
    type: 'Typ',
  },
  about: {
    changeLog: 'Dziennik zmian',
    updateNow: 'Aktualizuj teraz',
    nowAvailable: 'Dify {{version}} jest teraz dostępny.',
    latestAvailable: 'Dify {{version}} jest najnowszą dostępną wersją.',
  },
  appMenus: {
    overview: 'Monitorowanie',
    promptEng: 'Orkiestracja',
    apiAccess: 'Dostęp API',
    logAndAnn: 'Logi i ogł.',
    logs: 'Logi',
  },
  environment: {
    testing: 'TESTOWANIE',
    development: 'ROZWOJOWA',
  },
  appModes: {
    completionApp: 'Generator tekstu',
    chatApp: 'Aplikacja czatowa',
  },
  datasetMenus: {
    documents: 'Dokumenty',
    hitTesting: 'Testowanie poboru',
    settings: 'Ustawienia',
    emptyTip:
      'Wiedza nie została powiązana, przejdź do aplikacji lub wtyczki, aby ukończyć powiązanie.',
    viewDoc: 'Zobacz dokumentację',
    relatedApp: 'powiązane aplikacje',
    noRelatedApp: 'Brak połączonych aplikacji',
  },
  voiceInput: {
    speaking: 'Mów teraz...',
    converting: 'Konwertowanie na tekst...',
    notAllow: 'mikrofon nieautoryzowany',
  },
  modelName: {
    'gpt-3.5-turbo': 'GPT-3.5-Turbo',
    'gpt-3.5-turbo-16k': 'GPT-3.5-Turbo-16K',
    'gpt-4': 'GPT-4',
    'gpt-4-32k': 'GPT-4-32K',
    'text-davinci-003': 'Tekst-Davinci-003',
    'text-embedding-ada-002': 'Tekst-Wan-Ada-002',
    'whisper-1': 'Szept-1',
    'claude-instant-1': 'Claude-Natychmiastowy',
    'claude-2': 'Claude-2',
  },
  chat: {
    renameConversation: 'Zmień nazwę rozmowy',
    conversationName: 'Nazwa rozmowy',
    conversationNamePlaceholder: 'Proszę wprowadź nazwę rozmowy',
    conversationNameCanNotEmpty: 'Nazwa rozmowy wymagana',
    citation: {
      title: 'Cytaty',
      linkToDataset: 'Link do Wiedzy',
      characters: 'Postacie:',
      hitCount: 'Liczba trafień:',
      vectorHash: 'Wektor hash:',
      hitScore: 'Wynik trafień:',
    },
    inputPlaceholder: 'Porozmawiaj z {{botName}}',
    thought: 'Myśl',
    thinking: 'Myślenie...',
    resend: 'Prześlij ponownie',
  },
  promptEditor: {
    placeholder:
      'Wpisz swoje słowo kluczowe tutaj, wprowadź \'{\' aby wstawić zmienną, wprowadź \'/\' aby wstawić blok treści słownika',
    context: {
      item: {
        title: 'Kontekst',
        desc: 'Wstaw szablon kontekstu',
      },
      modal: {
        title: '{{num}} Wiedzy w Kontekście',
        add: 'Dodaj Kontekst ',
        footer: 'Możesz zarządzać kontekstami poniżej w sekcji Kontekstów.',
      },
    },
    history: {
      item: {
        title: 'Historia rozmów',
        desc: 'Wstaw szablon historycznej wiadomości',
      },
      modal: {
        title: 'PRZYKŁAD',
        user: 'Cześć',
        assistant: 'Cześć! W czym mogę pomóc?',
        edit: 'Edytuj nazwy ról rozmów',
      },
    },
    variable: {
      item: {
        title: 'Zmienne i Narzędzia Zewnętrzne',
        desc: 'Wstaw Zmienne i Narzędzia Zewnętrzne',
      },
      outputToolDisabledItem: {
        title: 'Zmienne',
        desc: 'Wstaw Zmienne',
      },
      modal: {
        add: 'Nowa zmienna',
        addTool: 'Nowe narzędzie',
      },
    },
    query: {
      item: {
        title: 'Zapytanie',
        desc: 'Wstaw szablon zapytania użytkownika',
      },
    },
    existed: 'Już istnieje w poleceniu',
  },
  imageUploader: {
    uploadFromComputer: 'Załaduj z komputera',
    uploadFromComputerReadError: 'Błąd odczytu obrazu, spróbuj ponownie.',
    uploadFromComputerUploadError:
      'Błąd przesyłania obrazu, prześlij go ponownie.',
    uploadFromComputerLimit:
      'Obrazy do przesłania nie mogą przekroczyć {{size}} MB',
    pasteImageLink: 'Wklej link do obrazu',
    pasteImageLinkInputPlaceholder: 'Wklej tutaj link do obrazu',
    pasteImageLinkInvalid: 'Nieprawidłowy link obrazu',
    imageUpload: 'Przesyłanie obrazu',
  },
  tag: {
    placeholder: 'Wszystkie tagi',
    addNew: 'Dodaj nowy tag',
    noTag: 'Brak tagów',
    noTagYet: 'Brak tagów jeszcze',
    addTag: 'Dodaj tagi',
    editTag: 'Edytuj tagi',
    manageTags: 'Zarządzaj Tagami',
    selectorPlaceholder: 'Wpisz, aby wyszukać lub utworzyć',
    create: 'Utwórz',
    delete: 'Usuń tag',
    deleteTip: 'Ten tag jest używany, czy chcesz go usunąć?',
    created: 'Tag został pomyślnie utworzony',
    failed: 'Nie udało się utworzyć tagu',
  },
  errorMsg: {
    fieldRequired: '{{field}} jest wymagane',
    urlError: 'Adres URL powinien zaczynać się od http:// lub https://',
  },
  fileUploader: {
    pasteFileLinkInputPlaceholder: 'Wpisz adres URL...',
    uploadFromComputerLimit: 'Prześlij plik nie może przekraczać {{size}}',
    pasteFileLink: 'Wklej link do pliku',
    uploadFromComputerUploadError: 'Przesyłanie pliku nie powiodło się, prześlij ponownie.',
    pasteFileLinkInvalid: 'Nieprawidłowy link do pliku',
    uploadFromComputerReadError: 'Odczyt pliku nie powiódł się, spróbuj ponownie.',
    fileExtensionNotSupport: 'Rozszerzenie pliku nie jest obsługiwane',
    uploadFromComputer: 'Przesyłanie lokalne',
  },
  license: {
    expiring_plural: 'Wygasa za {{count}} dni',
    expiring: 'Wygasa w ciągu jednego dnia',
    unlimited: 'Nieograniczony',
  },
  pagination: {
    perPage: 'Ilość elementów na stronie',
  },
  theme: {
    light: 'światło',
    theme: 'Temat',
    dark: 'ciemny',
    auto: 'system',
  },
  compliance: {
    soc2Type2: 'Raport SOC 2 Typ II',
    sandboxUpgradeTooltip: 'Dostępne tylko w planie Professional lub Team.',
    professionalUpgradeTooltip: 'Dostępne tylko w planie zespołowym lub wyższym.',
    iso27001: 'Certyfikacja ISO 27001:2022',
    soc2Type1: 'Raport SOC 2 Typ I',
    gdpr: 'GDPR DPA',
  },
  imageInput: {
    dropImageHere: 'Upuść swój obraz tutaj, lub',
    browse: 'przeglądaj',
    supportedFormats: 'Obsługuje PNG, JPG, JPEG, WEBP i GIF',
  },
  you: 'Ty',
}

export default translation
