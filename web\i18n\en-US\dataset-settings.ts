const translation = {
  title: 'Knowledge settings',
  desc: 'Here you can modify the properties and retrieval settings of this Knowledge.',
  form: {
    name: 'Knowledge Name',
    namePlaceholder: 'Please enter the Knowledge name',
    nameError: 'Name cannot be empty',
    desc: 'Knowledge Description',
    descInfo: 'Please write a clear textual description to outline the content of the Knowledge. This description will be used as a basis for matching when selecting from multiple Knowledge for inference.',
    descPlaceholder: 'Describe what is in this data set. A detailed description allows AI to access the content of the data set in a timely manner. If empty, Dify will use the default hit strategy.',
    helpText: 'Learn how to write a good dataset description.',
    descWrite: 'Learn how to write a good Knowledge description.',
    permissions: 'Permissions',
    permissionsOnlyMe: 'Only me',
    permissionsAllMember: 'All team members',
    permissionsInvitedMembers: 'Partial team members',
    me: '(You)',
    indexMethod: 'Index Method',
    indexMethodHighQuality: 'High Quality',
    indexMethodHighQualityTip: 'Calling the embedding model to process documents for more precise retrieval helps LLM generate high-quality answers.',
    upgradeHighQualityTip: 'Once upgrading to High Quality mode, reverting to Economical mode is not available',
    indexMethodEconomy: 'Economical',
    indexMethodEconomyTip: 'Using 10 keywords per chunk for retrieval, no tokens are consumed at the expense of reduced retrieval accuracy.',
    embeddingModel: 'Embedding Model',
    embeddingModelTip: 'Change the embedded model, please go to ',
    embeddingModelTipLink: 'Settings',
    retrievalSetting: {
      title: 'Retrieval Setting',
      method: 'Retrieval Method',
      learnMore: 'Learn more',
      description: ' about retrieval method.',
      longDescription: ' about retrieval method, you can change this at any time in the Knowledge settings.',
    },
    externalKnowledgeAPI: 'External Knowledge API',
    externalKnowledgeID: 'External Knowledge ID',
    retrievalSettings: 'Retrieval Settings',
    save: 'Save',
    indexMethodChangeToEconomyDisabledTip: 'Not available for downgrading from HQ to ECO',
    searchModel: 'Search model',
  },
}

export default translation
