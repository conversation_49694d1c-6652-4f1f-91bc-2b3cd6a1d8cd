# 參與貢獻

我們很高興你想要為 Dify 做出貢獻！作為一個資源有限的新創團隊，我們期望打造最直觀的 LLM 應用開發與管理工作流程。社群中的每一份貢獻對我們來說都非常重要。

作為一個快速發展的專案，我們需要保持敏捷並快速迭代，同時也希望能為貢獻者提供順暢的參與體驗。我們準備了這份貢獻指南，幫助你了解程式碼庫和我們與貢獻者合作的方式，讓你能夠盡快投入有趣的開發工作。

這份指南與 Dify 一樣，都在持續完善中。如果指南內容有落後於實際專案的情況，還請見諒，也歡迎提供改進建議。

關於授權部分，請花點時間閱讀我們簡短的[授權和貢獻者協議](./LICENSE)。社群也需遵守[行為準則](https://github.com/langgenius/.github/blob/main/CODE_OF_CONDUCT.md)。

## 開始之前

想找點事做？瀏覽我們的[新手友善議題](https://github.com/langgenius/dify/issues?q=is%3Aissue%20state%3Aopen%20label%3A%22good%20first%20issue%22)並挑選一個開始！

有酷炫的模型執行時期或工具要新增？在我們的[外掛倉庫](https://github.com/langgenius/dify-plugins)開啟 PR 展示你的作品。

需要更新現有的模型執行時期、工具或修復錯誤？前往我們的[官方外掛倉庫](https://github.com/langgenius/dify-official-plugins)開始你的魔法之旅！

加入我們，一起貢獻並打造令人驚艷的作品吧！💡✨

別忘了在 PR 描述中連結現有議題或開啟新議題。

### 錯誤回報

> [!IMPORTANT]  
> 提交錯誤回報時，請務必包含以下資訊：

- 清晰明確的標題
- 詳細的錯誤描述，包含任何錯誤訊息
- 重現錯誤的步驟
- 預期行為
- **日誌**，如果有的話。對後端問題來說這點很重要，你可以在 docker-compose logs 中找到
- 截圖或影片（如適用）

優先順序評估：

  | 議題類型 | 優先級 |
  | -------- | ------ |
  | 核心功能錯誤（雲端服務、無法登入、應用程式無法運作、安全漏洞） | 緊急 |
  | 非緊急錯誤、效能優化 | 中等 |
  | 次要修正（拼字錯誤、介面混淆但可運作） | 低 |

### 功能請求

> [!NOTE]  
> 提交功能請求時，請務必包含以下資訊：

- 清晰明確的標題
- 詳細的功能描述
- 功能的使用情境
- 其他相關背景說明或截圖

優先順序評估：

  | 功能類型 | 優先級 |
  | -------- | ------ |
  | 團隊成員標記為高優先級的功能 | 高 |
  | 來自[社群回饋板](https://github.com/langgenius/dify/discussions/categories/feedbacks)的熱門功能請求 | 中 |
  | 非核心功能和小幅改進 | 低 |
  | 有價值但非急迫的功能 | 未來功能 |

## 提交 PR

### PR 流程

1. Fork 專案
2. 在開始撰寫 PR 前，請先建立議題討論你想做的更改
3. 為你的更改建立新分支
4. 請為你的更改新增相應的測試
5. 確保你的程式碼通過現有測試
6. 請在 PR 描述中連結相關議題，使用 `fixes #<issue_number>`
7. 等待合併！

### 專案設定

#### 前端

關於前端服務的設定，請參考 `web/README.md` 中的完整[指南](https://github.com/langgenius/dify/blob/main/web/README.md)。此文件提供詳細說明，幫助你正確設定前端環境。

#### 後端

關於後端服務的設定，請參考 `api/README.md` 中的詳細[說明](https://github.com/langgenius/dify/blob/main/api/README.md)。此文件包含逐步指引，幫助你順利啟動後端服務。

#### 其他注意事項

我們建議在開始設定前仔細閱讀此文件，因為它包含以下重要資訊：
- 前置需求和相依性
- 安裝步驟
- 設定細節
- 常見問題排解

如果在設定過程中遇到任何問題，歡迎隨時詢問。

## 尋求協助

如果你在貢獻過程中遇到困難或有急切的問題，可以透過相關的 GitHub 議題詢問，或加入我們的 [Discord](https://discord.gg/8Tpq4AcN9c) 進行即時交流。

