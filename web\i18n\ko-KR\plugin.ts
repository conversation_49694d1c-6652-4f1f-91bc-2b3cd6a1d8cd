const translation = {
  category: {
    agents: '에이전트 전략',
    models: '모델',
    all: '모두',
    extensions: '확장',
    tools: '도구',
    bundles: '번들',
  },
  categorySingle: {
    extension: '확장',
    tool: '도구',
    agent: '에이전트 전략',
    bundle: '보따리',
    model: '모델',
  },
  list: {
    source: {
      marketplace: '마켓플레이스에서 설치',
      local: '로컬 패키지 파일에서 설치',
      github: 'GitHub 에서 설치',
    },
    noInstalled: '설치된 플러그인이 없습니다.',
    notFound: '플러그인을 찾을 수 없습니다.',
  },
  source: {
    local: '로컬 패키지 파일',
    marketplace: '마켓',
    github: '깃허브',
  },
  detailPanel: {
    categoryTip: {
      marketplace: '마켓플레이스에서 설치됨',
      debugging: '디버깅 플러그인',
      github: 'Github 에서 설치됨',
      local: '로컬 플러그인',
    },
    operation: {
      detail: '세부 정보',
      install: '설치',
      viewDetail: '자세히보기',
      info: '플러그인 정보',
      update: '업데이트',
      remove: '제거',
      checkUpdate: '업데이트 확인',
    },
    toolSelector: {
      empty: '\'+\' 버튼을 클릭하여 도구를 추가합니다. 여러 도구를 추가할 수 있습니다.',
      descriptionLabel: '도구 설명',
      uninstalledContent: '이 플러그인은 로컬/GitHub 저장소에서 설치됩니다. 설치 후 사용하십시오.',
      params: '추론 구성',
      paramsTip1: 'LLM 추론 파라미터를 제어합니다.',
      uninstalledLink: '플러그인에서 관리',
      unsupportedTitle: '지원되지 않는 작업',
      auto: '자동 번역',
      settings: '사용자 설정',
      unsupportedContent2: '버전을 전환하려면 클릭합니다.',
      uninstalledTitle: '도구가 설치되지 않음',
      descriptionPlaceholder: '도구의 용도에 대한 간략한 설명 (예: 특정 위치의 온도 가져오기).',
      title: '추가 도구',
      toolLabel: '도구',
      placeholder: '도구 선택...',
      paramsTip2: '\'자동\'이 꺼져 있으면 기본값이 사용됩니다.',
      unsupportedContent: '설치된 플러그인 버전은 이 작업을 제공하지 않습니다.',
      toolSetting: '도구 설정',
    },
    configureApp: '앱 구성',
    strategyNum: '{{번호}} {{전략}} 포함',
    endpointModalDesc: '구성이 완료되면 API 엔드포인트를 통해 플러그인에서 제공하는 기능을 사용할 수 있습니다.',
    actionNum: '{{번호}} {{행동}} 포함',
    endpointDeleteTip: '엔드포인트 제거',
    modelNum: '{{번호}} 포함 된 모델',
    configureModel: '모델 구성',
    configureTool: '구성 도구',
    switchVersion: '스위치 버전',
    endpointsEmpty: '\'+\' 버튼을 클릭하여 엔드포인트를 추가합니다.',
    endpointModalTitle: '엔드포인트 설정',
    endpointsTip: '이 플러그인은 엔드포인트를 통해 특정 기능을 제공하며 현재 작업 공간에 대해 여러 엔드포인트 세트를 구성할 수 있습니다.',
    endpointDisableContent: '{{name}}을 비활성화하시겠습니까?',
    endpointDeleteContent: '{{name}}을 제거하시겠습니까?',
    disabled: '비활성화',
    endpointsDocLink: '문서 보기',
    endpoints: '끝점',
    serviceOk: '서비스 정상',
    endpointDisableTip: '엔드포인트 비활성화',
  },
  debugInfo: {
    title: '디버깅',
    viewDocs: '문서 보기',
  },
  privilege: {
    admins: '관리자',
    title: '플러그인 기본 설정',
    whoCanDebug: '누가 플러그인을 디버깅할 수 있나요?',
    noone: '아무도 없어',
    everyone: '모두',
    whoCanInstall: '누가 플러그인을 설치하고 관리할 수 있습니까?',
  },
  pluginInfoModal: {
    packageName: '패키지',
    repository: '저장소',
    title: '플러그인 정보',
    release: '석방',
  },
  action: {
    deleteContentRight: '플러그인?',
    usedInApps: '이 플러그인은 {{num}}개의 앱에서 사용되고 있습니다.',
    pluginInfo: '플러그인 정보',
    checkForUpdates: '업데이트 확인',
    deleteContentLeft: '제거하시겠습니까?',
    delete: '플러그인 제거',
  },
  installModal: {
    labels: {
      package: '패키지',
      repository: '저장소',
      version: '버전',
    },
    back: '뒤로',
    readyToInstallPackage: '다음 플러그인을 설치하려고 합니다.',
    close: '닫다',
    fromTrustSource: '<trustSource>신뢰할 수 있는 출처</trustSource>의 플러그인만 설치하도록 하세요.',
    readyToInstall: '다음 플러그인을 설치하려고 합니다.',
    uploadFailed: '업로드 실패',
    installPlugin: '플러그인 설치',
    pluginLoadErrorDesc: '이 플러그인은 설치되지 않습니다.',
    installedSuccessfully: '설치 성공',
    installedSuccessfullyDesc: '플러그인이 성공적으로 설치되었습니다.',
    installing: '설치...',
    pluginLoadError: '플러그인 로드 오류',
    installFailedDesc: '플러그인이 설치되지 않았습니다.',
    installFailed: '설치 실패',
    next: '다음',
    installComplete: '설치 완료',
    install: '설치하다',
    readyToInstallPackages: '다음 {{num}} 플러그인을 설치하려고 합니다.',
    uploadingPackage: '{{packageName}} 업로드 중...',
    dropPluginToInstall: '플러그인 패키지를 여기에 놓아 설치하십시오.',
    cancel: '취소',
    installWarning: '이 플러그인은 설치할 수 없습니다.',
  },
  installFromGitHub: {
    uploadFailed: '업로드 실패',
    selectVersionPlaceholder: '버전을 선택하세요.',
    installPlugin: 'GitHub 에서 플러그인 설치',
    installFailed: '설치 실패',
    updatePlugin: 'GitHub 에서 플러그인 업데이트',
    selectPackage: '패키지 선택',
    gitHubRepo: 'GitHub 리포지토리',
    selectPackagePlaceholder: '패키지를 선택하세요.',
    installedSuccessfully: '설치 성공',
    selectVersion: '버전 선택',
    installNote: '신뢰할 수 있는 출처의 플러그인만 설치하도록 하세요.',
  },
  upgrade: {
    usedInApps: '{{num}}개의 앱에서 사용됨',
    description: '다음 플러그인을 설치하려고 합니다.',
    successfulTitle: '설치 성공',
    upgrade: '설치하다',
    upgrading: '설치...',
    close: '닫다',
    title: '플러그인 설치',
  },
  error: {
    noReleasesFound: '릴리스를 찾을 수 없습니다. GitHub 리포지토리 또는 입력 URL 을 확인하세요.',
    fetchReleasesError: '릴리스를 검색할 수 없습니다. 나중에 다시 시도하십시오.',
    inValidGitHubUrl: '잘못된 GitHub URL 입니다. 유효한 URL 을 https://github.com/owner/repo 형식으로 입력하십시오.',
  },
  marketplace: {
    sortOption: {
      recentlyUpdated: '최근 업데이트',
      firstReleased: '첫 출시',
      newlyReleased: '새로 출시 된',
      mostPopular: '가장 인기 있는',
    },
    noPluginFound: '플러그인을 찾을 수 없습니다.',
    empower: 'AI 개발 역량 강화',
    viewMore: '더보기',
    difyMarketplace: 'Dify 마켓플레이스',
    pluginsResult: '{{num}} 결과',
    discover: '발견하다',
    moreFrom: 'Marketplace 에서 더 보기',
    sortBy: '정렬',
    and: '그리고',
    verifiedTip: 'Dify 에 의해 확인됨',
    partnerTip: 'Dify 파트너에 의해 확인됨',
  },
  task: {
    installingWithSuccess: '{{installingLength}} 플러그인 설치, {{successLength}} 성공.',
    installedError: '{{errorLength}} 플러그인 설치 실패',
    installing: '{{installingLength}} 플러그인 설치, 0 완료.',
    installingWithError: '{{installingLength}} 플러그인 설치, {{successLength}} 성공, {{errorLength}} 실패',
    installError: '{{errorLength}} 플러그인 설치 실패, 보려면 클릭하십시오.',
    clearAll: '모두 지우기',
  },
  installAction: '설치하다',
  searchTools: '검색 도구...',
  installPlugin: '플러그인 설치',
  endpointsEnabled: '{{num}}개의 엔드포인트 집합이 활성화되었습니다.',
  installFrom: '에서 설치',
  allCategories: '모든 카테고리',
  findMoreInMarketplace: 'Marketplace 에서 더 알아보기',
  searchCategories: '검색 카테고리',
  search: '검색',
  searchInMarketplace: 'Marketplace 에서 검색',
  from: '보낸 사람',
  searchPlugins: '검색 플러그인',
  install: '{{num}} 설치',
  fromMarketplace: 'Marketplace 에서',
  metadata: {
    title: '플러그인',
  },
  difyVersionNotCompatible: '현재 Dify 버전이 이 플러그인과 호환되지 않습니다. 필요한 최소 버전으로 업그레이드하십시오: {{minimalDifyVersion}}',
  requestAPlugin: '플러그인을 요청하세요',
  publishPlugins: '플러그인 게시',
}

export default translation
