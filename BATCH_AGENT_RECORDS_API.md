# 批量查询Agent Records API

## 概述

新的批量查询API允许您一次性查询多个应用的智能体记录信息，包括创建信息、发布信息、运行信息、统计信息和调用记录。

## API端点

```
POST /console/api/agent-records/batch
```

## 请求参数

请求体为JSON格式，包含以下参数：

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| app_ids | array | 是 | 应用ID列表 | `["550e8400-e29b-41d4-a716-446655440000", "550e8400-e29b-41d4-a716-446655440001"]` |
| start | string | 否 | 开始时间（YYYY-MM-DD HH:MM格式） | `2024-01-01 00:00` |
| end | string | 否 | 结束时间（YYYY-MM-DD HH:MM格式） | `2024-12-31 23:59` |
| limit | integer | 否 | 每个应用返回的调用记录数量限制，默认50 | `20` |
| total_limit | integer | 否 | 所有应用的总记录数限制，可选 | `100` |

## 请求示例

### 基本批量查询
```bash
curl -X POST "http://localhost:5001/console/api/agent-records/batch" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "app_ids": ["550e8400-e29b-41d4-a716-446655440000", "550e8400-e29b-41d4-a716-446655440001"],
    "limit": 10
  }'
```

### 带时间范围的批量查询
```bash
curl -X POST "http://localhost:5001/console/api/agent-records/batch" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "app_ids": ["550e8400-e29b-41d4-a716-446655440000"],
    "start": "2024-01-01 00:00",
    "end": "2024-12-31 23:59",
    "limit": 20
  }'
```

### 带总记录数限制的批量查询
```bash
curl -X POST "http://localhost:5001/console/api/agent-records/batch" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "app_ids": ["550e8400-e29b-41d4-a716-446655440000", "550e8400-e29b-41d4-a716-446655440001"],
    "limit": 30,
    "total_limit": 50
  }'
```

## 响应格式

```json
{
  "data": {
    "550e8400-e29b-41d4-a716-446655440000": {
      "app_id": "550e8400-e29b-41d4-a716-446655440000",
      "app_name": "应用名称",
      "app_mode": "chat",
      "creation_info": {
        "created_at": "2024-01-01T00:00:00Z",
        "created_by": "user_id",
        "creator_name": "创建者姓名",
        "initial_config": {
          "mode": "chat",
          "description": "应用描述",
          "enable_site": true,
          "enable_api": true
        }
      },
      "publish_info": {
        "last_published_at": "2024-01-01T00:00:00Z",
        "published_by": "user_id",
        "publisher_name": "发布者姓名",
        "version": "1.0.0",
        "workflow_id": "workflow_id"
      },
      "runtime_info": {
        "total_runs": 100,
        "successful_runs": 95,
        "failed_runs": 5,
        "average_response_time": 1.5,
        "total_tokens_used": 10000,
        "total_cost": 0.05,
        "currency": "USD"
      },
      "statistics": {
        "total_conversations": 50,
        "total_messages": 200,
        "user_satisfaction_rate": 0.85,
        "daily_usage": [
          {
            "date": "2024-01-01",
            "message_count": 10
          }
        ],
        "monthly_usage": []
      },
      "call_records": [
        {
          "id": "message_id",
          "timestamp": "2024-01-01T00:00:00Z",
          "user_id": "user_id",
          "user_name": "用户姓名",
          "input": "用户输入",
          "output": "AI输出",
          "status": "success",
          "response_time": 1.2,
          "tokens_used": 100,
          "conversation_id": "conversation_id",
          "message_id": "message_id"
        }
      ]
    },
    "550e8400-e29b-41d4-a716-446655440001": {
      "app_id": "550e8400-e29b-41d4-a716-446655440001",
      "app_name": "另一个应用",
      "error": "处理失败: 具体错误信息"
    }
  },
  "meta": {
    "total_apps_requested": 2,
    "total_apps_returned": 2,
    "total_records_requested": 100,
    "total_records_returned": 95,
    "limit_per_app": 50,
    "total_limit": 100
  }
}
```

## 错误响应

### 400 Bad Request
```json
{
  "error": "app_ids参数必须是非空列表"
}
```

```json
{
  "error": "app_id必须是字符串类型: 123"
}
```

```json
{
  "error": "无效的app_id格式: invalid-uuid"
}
```

### 403 Forbidden
```json
{
  "error": "部分应用不存在或无权限访问"
}
```

## 特性说明

1. **批量处理**: 支持同时查询多个应用的记录
2. **列表格式**: 使用JSON数组格式传递app_ids，更加规范和易用
3. **错误容错**: 如果某个应用处理失败，会返回错误信息但继续处理其他应用
4. **权限验证**: 自动验证用户是否有权限访问请求的应用
5. **UUID验证**: 自动验证app_ids的UUID格式
6. **类型验证**: 验证app_ids必须是字符串列表
7. **时间范围过滤**: 支持按时间范围过滤数据
8. **双重限制**: 支持每个应用限制（limit）和总记录数限制（total_limit）
9. **元信息返回**: 返回详细的查询元信息，包括请求和返回的统计
10. **智能截取**: 当达到总限制时，智能截取记录并停止处理更多应用

## 与原API的对比

| 特性 | 原API | 新批量API |
|------|-------|-----------|
| 端点 | `/apps/{app_id}/agent-records` | `/console/api/agent-records/batch` |
| 方法 | GET | POST |
| 应用数量 | 单个 | 多个 |
| 参数传递 | 路径参数 | JSON请求体 |
| 响应格式 | 单个应用数据 | 多个应用数据字典 |
| 错误处理 | 整体失败 | 部分失败容错 |

## 使用建议

1. **性能考虑**: 批量查询大量应用时，建议适当增加请求超时时间
2. **数据量控制**: 
   - 使用 `limit` 参数控制每个应用返回的记录数量
   - 使用 `total_limit` 参数控制所有应用的总记录数量
   - 建议设置合理的限制值，避免响应过大
3. **时间范围**: 使用start和end参数缩小查询范围，提高查询效率
4. **错误处理**: 客户端需要处理部分应用可能返回错误的情况
5. **元信息利用**: 利用返回的meta信息了解实际返回的数据量，便于调试和优化

## 测试

可以使用提供的测试脚本 `test_batch_api.py` 来验证API功能：

```bash
python test_batch_api.py
```

请根据实际情况修改脚本中的BASE_URL和测试用的app_ids。

## Python示例

```python
import requests
import json

# 配置
BASE_URL = "http://localhost:5001"
API_ENDPOINT = "/console/api/agent-records/batch"
TOKEN = "YOUR_AUTH_TOKEN"

# 请求数据
data = {
    "app_ids": ["550e8400-e29b-41d4-a716-446655440000", "550e8400-e29b-41d4-a716-446655440001"],
    "start": "2024-01-01 00:00",
    "end": "2024-12-31 23:59",
    "limit": 20,
    "total_limit": 30
}

# 发送请求
headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {TOKEN}"
}

response = requests.post(
    f"{BASE_URL}{API_ENDPOINT}",
    json=data,
    headers=headers
)

if response.status_code == 200:
    result = response.json()
    print("查询成功!")
    
    # 打印元信息
    meta = result.get("meta", {})
    if meta:
        print(f"元信息:")
        print(f"  请求应用数: {meta.get('total_apps_requested', 0)}")
        print(f"  返回应用数: {meta.get('total_apps_returned', 0)}")
        print(f"  请求记录数: {meta.get('total_records_requested', 0)}")
        print(f"  返回记录数: {meta.get('total_records_returned', 0)}")
        print(f"  每应用限制: {meta.get('limit_per_app', 0)}")
        if 'total_limit' in meta:
            print(f"  总记录限制: {meta.get('total_limit', 0)}")
    
    for app_id, app_data in result["data"].items():
        if "error" in app_data:
            print(f"应用 {app_id}: {app_data['error']}")
        else:
            print(f"应用 {app_id}: {app_data['app_name']}")
            print(f"  运行次数: {app_data['runtime_info']['total_runs']}")
            print(f"  消息数量: {app_data['statistics']['total_messages']}")
            print(f"  调用记录数: {len(app_data['call_records'])}")
else:
    print(f"请求失败: {response.status_code}")
    print(response.text)
``` 