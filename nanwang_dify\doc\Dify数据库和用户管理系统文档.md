# Dify项目数据库创建和用户管理系统文档

## 📋 文档概述

本文档详细描述了Dify项目中数据库创建、用户信息管理、用户权限控制等核心功能的实现。涵盖了从数据库模型定义到权限系统实现的完整技术架构。

**主要内容包括：**
- 数据库配置和初始化
- 用户账户和租户管理
- 组织架构模型
- 权限控制系统
- 数据库迁移机制

> **📖 相关文档**: 建议先阅读 [Dify_API架构和分类说明.md](./Dify_API架构和分类说明.md) 了解整体API架构

---

## 📁 数据库创建和初始化相关文件

### 1.1 数据库配置和引擎

| 文件路径 | 描述 |
|---------|------|
| `api/configs/middleware/__init__.py` | 数据库连接配置 |
| `api/models/engine.py` | SQLAlchemy数据库引擎定义 |
| `api/extensions/ext_database.py` | 数据库扩展初始化 |
| `api/extensions/ext_migrate.py` | 数据库迁移扩展 |

### 1.2 数据库迁移文件

| 文件路径 | 描述 |
|---------|------|
| `api/migrations/versions/64b051264f32_init.py` | 初始化数据库迁移脚本 |
| `api/migrations/versions/` | 包含所有数据库结构变更的迁移文件 |

---

## 👥 用户相关数据库管理

### 2.1 用户模型定义 (`api/models/account.py`)

#### 用户角色枚举定义

```python
# 用户角色枚举
class TenantAccountRole(enum.StrEnum):
    OWNER = "owner"           # 所有者
    ADMIN = "admin"           # 管理员
    EDITOR = "editor"         # 编辑者
    NORMAL = "normal"         # 普通用户
    DATASET_OPERATOR = "dataset_operator"  # 数据集操作员

    @staticmethod
    def is_valid_role(role: str) -> bool:
        """验证角色是否有效"""
        if not role:
            return False
        return role in {
            TenantAccountRole.OWNER,
            TenantAccountRole.ADMIN,
            TenantAccountRole.EDITOR,
            TenantAccountRole.NORMAL,
            TenantAccountRole.DATASET_OPERATOR,
        }

    @staticmethod
    def is_privileged_role(role: Optional["TenantAccountRole"]) -> bool:
        """检查是否为特权角色"""
        if not role:
            return False
        return role in {TenantAccountRole.OWNER, TenantAccountRole.ADMIN}
```

#### 用户账户模型

```python
# 用户账户模型
class Account(UserMixin, Base):
    __tablename__ = "accounts"
    __table_args__ = (
        db.PrimaryKeyConstraint("id", name="account_pkey"), 
        db.Index("account_email_idx", "email")
    )
    
    id = mapped_column(StringUUID, server_default=db.text("uuid_generate_v4()"))
    name = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(255), nullable=False)
    password = db.Column(db.String(255), nullable=True)
    password_salt = db.Column(db.String(255), nullable=True)
    avatar = db.Column(db.String(255))
    interface_language = db.Column(db.String(255))
    interface_theme = db.Column(db.String(255))
    timezone = db.Column(db.String(255))
    last_login_at = db.Column(db.DateTime)
    last_login_ip = db.Column(db.String(255))
    last_active_at = db.Column(db.DateTime, nullable=False, 
                              server_default=func.current_timestamp())
    status = db.Column(db.String(16), nullable=False, 
                      server_default=db.text("'active'::character varying"))
    initialized_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, nullable=False, 
                          server_default=func.current_timestamp())
    updated_at = db.Column(db.DateTime, nullable=False, 
                          server_default=func.current_timestamp())

    @property
    def is_admin_or_owner(self):
        """检查是否为管理员或所有者"""
        return TenantAccountRole.is_privileged_role(self.role)

    @property
    def is_admin(self):
        """检查是否为管理员"""
        return TenantAccountRole.is_admin_role(self.role)

    @property
    def is_editor(self):
        """检查是否为编辑者"""
        return TenantAccountRole.is_editing_role(self.role)
```

#### 租户模型

```python
# 租户模型
class Tenant(Base):
    __tablename__ = "tenants"
    __table_args__ = (db.PrimaryKeyConstraint("id", name="tenant_pkey"),)
    
    id = db.Column(StringUUID, server_default=db.text("uuid_generate_v4()"))
    name = db.Column(db.String(255), nullable=False)
    encrypt_public_key = db.Column(db.Text)
    plan = db.Column(db.String(255), nullable=False, 
                    server_default=db.text("'basic'::character varying"))
    status = db.Column(db.String(255), nullable=False, 
                      server_default=db.text("'normal'::character varying"))
    custom_config = db.Column(db.Text)
    created_at = db.Column(db.DateTime, nullable=False, 
                          server_default=func.current_timestamp())
    updated_at = db.Column(db.DateTime, nullable=False, 
                          server_default=func.current_timestamp())

    def get_accounts(self) -> list[Account]:
        """获取租户下的所有用户"""
        return (
            db.session.query(Account)
            .filter(Account.id == TenantAccountJoin.account_id, 
                   TenantAccountJoin.tenant_id == self.id)
            .all()
        )

    @property
    def custom_config_dict(self) -> dict:
        """获取自定义配置字典"""
        return json.loads(self.custom_config) if self.custom_config else {}
```

#### 用户-租户关联表

```python
# 用户-租户关联表
class TenantAccountJoin(Base):
    __tablename__ = "tenant_account_joins"
    __table_args__ = (
        db.PrimaryKeyConstraint("id", name="tenant_account_join_pkey"),
        db.Index("tenant_account_join_account_id_idx", "account_id"),
        db.Index("tenant_account_join_tenant_id_idx", "tenant_id"),
        db.UniqueConstraint("tenant_id", "account_id", name="unique_tenant_account_join"),
    )
    
    id = db.Column(StringUUID, server_default=db.text("uuid_generate_v4()"))
    tenant_id = db.Column(StringUUID, nullable=False)
    account_id = db.Column(StringUUID, nullable=False)
    current = db.Column(db.Boolean, nullable=False, server_default=db.text("false"))
    role = db.Column(db.String(16), nullable=False, server_default="normal")
    invited_by = db.Column(StringUUID, nullable=True)
    created_at = db.Column(db.DateTime, nullable=False, 
                          server_default=func.current_timestamp())
    updated_at = db.Column(db.DateTime, nullable=False, 
                          server_default=func.current_timestamp())
```

### 2.2 组织架构模型 (`api/models/organization.py`)

#### 组织架构模型

```python
# 组织架构模型
class Organization(Base):
    """组织架构模型 - 支持多级组织架构"""
    __tablename__ = "organizations"
    __table_args__ = (
        db.PrimaryKeyConstraint("id", name="organization_pkey"),
        db.Index("organization_tenant_id_idx", "tenant_id"),
        db.Index("organization_parent_id_idx", "parent_id"),
        db.Index("organization_code_idx", "org_code"),
        db.UniqueConstraint("tenant_id", "org_code", name="unique_tenant_org_code"),
    )
    
    id = mapped_column(StringUUID, server_default=db.text("uuid_generate_v4()"))
    tenant_id = mapped_column(StringUUID, nullable=False)
    
    # 组织基本信息
    org_code = db.Column(db.String(64), nullable=False)     # 组织编码
    org_name = db.Column(db.String(255), nullable=False)    # 组织名称
    org_short_name = db.Column(db.String(128), nullable=True) # 组织简称
    org_type = db.Column(db.String(16), nullable=False, 
                        server_default=db.text("'dept'::character varying"))
    org_level = db.Column(db.String(16), nullable=False, 
                         server_default=db.text("'department'::character varying"))
    
    # 组织层级关系
    parent_id = db.Column(StringUUID, nullable=True)        # 上级组织ID
    org_path = db.Column(db.Text, nullable=True)            # 组织路径 (例如: /root/company/dept)
    level_depth = db.Column(db.Integer, nullable=False, 
                           server_default=db.text("1"))     # 组织层级深度
    sort_order = db.Column(db.Integer, nullable=False, 
                          server_default=db.text("0"))      # 同级排序
    
    # 组织详细信息
    description = db.Column(db.Text, nullable=True)         # 组织描述
    address = db.Column(db.String(500), nullable=True)      # 地址
    phone = db.Column(db.String(32), nullable=True)
    email = db.Column(db.String(128), nullable=True)        # 联系邮箱
    
    # 管理信息
    manager_id = db.Column(StringUUID, nullable=True)       # 负责人ID
    manager_name = db.Column(db.String(128), nullable=True) # 负责人姓名
    deputy_manager_id = db.Column(StringUUID, nullable=True) # 副负责人ID
    
    # 业务信息
    business_scope = db.Column(db.Text, nullable=True)      # 业务范围
    cost_center_code = db.Column(db.String(64), nullable=True) # 成本中心编码
    
    # 状态和时间
    status = db.Column(db.String(16), nullable=False, 
                      server_default=db.text("'active'::character varying"))
    effective_date = db.Column(db.DateTime, nullable=True)  # 生效日期
    expiry_date = db.Column(db.DateTime, nullable=True)     # 失效日期
```

#### 用户-组织关联表

```python
# 用户-组织关联表
class UserOrganization(Base):
    """用户-组织关联表"""
    __tablename__ = "user_organizations"
    __table_args__ = (
        db.PrimaryKeyConstraint("id", name="user_organization_pkey"),
        db.Index("user_organization_account_id_idx", "account_id"),
        db.Index("user_organization_org_id_idx", "organization_id"),
        db.UniqueConstraint("account_id", "organization_id", name="unique_user_org"),
    )
    
    id = mapped_column(StringUUID, server_default=db.text("uuid_generate_v4()"))
    account_id = mapped_column(StringUUID, nullable=False)
    organization_id = mapped_column(StringUUID, nullable=False)
    
    # 关联信息
    is_primary = db.Column(db.Boolean, nullable=False, 
                          server_default=db.text("false"))  # 是否主组织
    position_in_org = db.Column(db.String(128), nullable=True)   # 在该组织中的职位
    join_date = db.Column(db.DateTime, nullable=True)           # 加入时间
    leave_date = db.Column(db.DateTime, nullable=True)          # 离开时间
    
    # 审计字段
    created_by = db.Column(StringUUID, nullable=True)
    created_at = db.Column(db.DateTime, nullable=False, 
                          server_default=func.current_timestamp())
    updated_at = db.Column(db.DateTime, nullable=False, 
                          server_default=func.current_timestamp())

    @property
    def is_active(self) -> bool:
        """判断关联是否有效"""
        now = datetime.utcnow()
        if self.join_date and self.join_date > now:
            return False
        if self.leave_date and self.leave_date < now:
            return False
        return True
```

### 2.3 权限控制系统 (`api/controllers/console/auth/permissions.py`)

这个文件包含了完整的权限控制装饰器系统，支持多种权限验证机制：

#### 基础权限装饰器

```python
# 基础权限装饰器
@user_type_required(AccountType.INTERNAL)          # 用户类型权限
@security_level_required(SecurityLevel.HIGH)       # 安全等级权限
@role_required(TenantAccountRole.ADMIN)            # 角色权限
@internal_user_only                                # 仅内部用户
@external_user_forbidden                           # 禁止外部用户

def user_type_required(allowed_types: Union[AccountType, List[AccountType]]):
    """用户类型权限检查装饰器"""
    if isinstance(allowed_types, AccountType):
        allowed_types = [allowed_types]
        
    def decorator(view):
        @wraps(view)
        def decorated(*args, **kwargs):
            account = _get_current_account()
            _check_account_status(account)
            
            if account.account_type_enum not in allowed_types:
                raise PermissionDeniedError(
                    f"User type '{account.account_type}' not allowed. "
                    f"Required: {[t.value for t in allowed_types]}"
                )
            
            return view(*args, **kwargs)
        return decorated
    return decorator
```

#### 业务权限装饰器

```python
# 业务权限装饰器
@business_admin_required                           # 业务管理员权限
@workflow_editor_required                          # 工作流编辑权限
@auditor_role_required                            # 审计员权限
@readonly_forbidden                               # 禁止只读用户访问

def business_admin_required(view):
    """业务管理员权限装饰器"""
    @wraps(view)
    def decorated(*args, **kwargs):
        account = _get_current_account()
        _check_account_status(account)
        
        if not account.is_business_admin:
            raise PermissionDeniedError("Business admin role required")
        
        return view(*args, **kwargs)
    return decorated
```

#### 复合权限装饰器

```python
# 复合权限装饰器
@privileged_user_required                         # 特权用户(OWNER+ADMIN)
@high_security_required                          # 高安全等级
@critical_security_required                      # 关键安全等级
@internal_admin_required                         # 内部管理员

def privileged_user_required(view):
    """特权用户权限装饰器 (OWNER + ADMIN)"""
    @wraps(view)
    def decorated(*args, **kwargs):
        account = _get_current_account()
        _check_account_status(account)
        
        if not account.is_admin_or_owner:
            raise PermissionDeniedError("Privileged user role (owner/admin) required")
        
        return view(*args, **kwargs)
    return decorated
```

#### 数据权限装饰器

```python
# 数据权限装饰器
@data_access_permission_required("dataset", "read")  # 数据访问权限
@organization_data_access_required                   # 组织数据访问权限

def data_access_permission_required(resource_type: str, permission_level: str = "read"):
    """数据访问权限装饰器"""
    def decorator(view):
        @wraps(view)
        def decorated(*args, **kwargs):
            account = _get_current_account()
            _check_account_status(account)
            
            # 这里可以实现基于组织架构的数据权限检查
            # 例如检查用户是否有权限访问特定组织的数据
            
            return view(*args, **kwargs)
        return decorated
    return decorator
```

#### 审计装饰器

```python
# 审计装饰器
@audit_log_required("create_dataset", "dataset")    # 审计日志记录
@working_hours_required(9, 18)                     # 工作时间权限
@ip_whitelist_required(["*************"])          # IP白名单权限

def audit_log_required(operation: str, resource_type: str = None):
    """审计日志装饰器"""
    def decorator(view):
        @wraps(view)
        def decorated(*args, **kwargs):
            account = _get_current_account()
            _check_account_status(account)
            
            # 记录操作开始
            start_time = datetime.utcnow()
            
            try:
                # 执行原始方法
                result = view(*args, **kwargs)
                
                # 记录成功的操作日志
                _log_operation(account, operation, resource_type, "SUCCESS", start_time)
                
                return result
            except Exception as e:
                # 记录失败的操作日志
                _log_operation(account, operation, resource_type, "FAILED", start_time, str(e))
                raise
            
        return decorated
    return decorator
```

---

## 🗃️ 数据库初始化流程

### 3.1 应用初始化 (`api/app_factory.py`)

```python
def initialize_extensions(app: DifyApp):
    """初始化应用扩展"""
    from extensions import (
        ext_timezone,
        ext_logging,
        ext_warnings,
        ext_import_modules,
        ext_set_secretkey,
        ext_compress,
        ext_code_based_extension,
        ext_database,      # 数据库扩展初始化
        ext_app_metrics,
        ext_migrate,       # 数据库迁移扩展
        ext_redis,
        ext_storage,
        ext_celery,
        ext_login,
        ext_mail,
        ext_hosting_provider,
        ext_sentry,
        ext_proxy_fix,
        ext_blueprints,
        ext_commands,
        ext_otel,
        ext_request_logging,
    )
    
    extensions = [
        ext_timezone,
        ext_logging,
        ext_warnings,
        ext_import_modules,
        ext_set_secretkey,
        ext_compress,
        ext_code_based_extension,
        ext_database,      # 数据库扩展初始化
        ext_app_metrics,
        ext_migrate,       # 数据库迁移扩展
        ext_redis,
        ext_storage,
        ext_celery,
        ext_login,
        ext_mail,
        ext_hosting_provider,
        ext_sentry,
        ext_proxy_fix,
        ext_blueprints,
        ext_commands,
        ext_otel,
        ext_request_logging,
    ]
    
    for ext in extensions:
        short_name = ext.__name__.split(".")[-1]
        is_enabled = ext.is_enabled() if hasattr(ext, "is_enabled") else True
        if not is_enabled:
            if dify_config.DEBUG:
                logging.info(f"Skipped {short_name}")
            continue

        start_time = time.perf_counter()
        ext.init_app(app)
        end_time = time.perf_counter()
        if dify_config.DEBUG:
            logging.info(f"Loaded {short_name} ({round((end_time - start_time) * 1000, 2)} ms)")
```

### 3.2 系统设置服务 (`api/services/account_service.py`)

```python
class RegisterService:
    @classmethod
    def setup(cls, email: str, name: str, password: str, ip_address: str) -> None:
        """系统初始化设置"""
        try:
            # 1. 创建账户
            account = AccountService.create_account(
                email=email,
                name=name,
                interface_language=languages[0],
                password=password,
                is_setup=True,
            )
            
            account.last_login_ip = ip_address
            account.initialized_at = datetime.now(UTC).replace(tzinfo=None)
            
            # 2. 创建租户
            TenantService.create_owner_tenant_if_not_exist(
                account=account, 
                is_setup=True
            )
            
            # 3. 创建系统设置记录
            dify_setup = DifySetup(version=dify_config.project.version)
            db.session.add(dify_setup)
            db.session.commit()
            
        except Exception as e:
            # 回滚操作
            db.session.query(DifySetup).delete()
            db.session.query(TenantAccountJoin).delete()
            db.session.query(Account).delete()
            db.session.query(Tenant).delete()
            db.session.commit()
            
            logging.exception(f"Setup account failed, email: {email}, name: {name}")
            raise ValueError(f"Setup failed: {e}")
```

### 3.3 数据库配置 (`api/configs/middleware/__init__.py`)

```python
class DatabaseConfig(BaseSettings):
    DB_HOST: str = Field(
        description="Hostname or IP address of the database server.",
        default="localhost",
    )
    DB_PORT: PositiveInt = Field(
        description="Port number for database connection.",
        default=5432,
    )
    DB_USERNAME: str = Field(
        description="Username for database authentication.",
        default="postgres",
    )
    DB_PASSWORD: str = Field(
        description="Password for database authentication.",
        default="",
    )
    DB_DATABASE: str = Field(
        description="Name of the database to connect to.",
        default="dify",
    )

    @computed_field
    def SQLALCHEMY_DATABASE_URI(self) -> str:
        db_extras = (
            f"{self.DB_EXTRAS}&client_encoding={self.DB_CHARSET}" if self.DB_CHARSET else self.DB_EXTRAS
        ).strip("&")
        db_extras = f"?{db_extras}" if db_extras else ""
        return (
            f"{self.SQLALCHEMY_DATABASE_URI_SCHEME}://"
            f"{quote_plus(self.DB_USERNAME)}:{quote_plus(self.DB_PASSWORD)}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_DATABASE}"
            f"{db_extras}"
        )
```

---

## 🛠️ 主要特性

### 多租户架构
支持多租户隔离，每个租户有独立的数据空间，通过tenant_id进行数据隔离。每个用户可以属于多个租户，在不同租户中拥有不同的角色和权限。

### 分级权限系统
提供5种用户角色：
- **OWNER（所有者）**：拥有最高权限，可以管理租户的所有资源
- **ADMIN（管理员）**：拥有管理权限，可以管理用户和大部分资源
- **EDITOR（编辑者）**：拥有编辑权限，可以创建和修改内容
- **NORMAL（普通用户）**：基础使用权限
- **DATASET_OPERATOR（数据集操作员）**：专门负责数据集管理

### 组织架构
支持多级组织架构和用户-组织-岗位关联，实现基于组织的权限管理：
- 树状组织结构，支持无限层级
- 用户可以属于多个组织
- 每个用户在组织中可以有不同的职位
- 支持组织路径和层级深度管理

### 安全机制
包含多重安全功能：
- **密码加密**：使用盐值进行密码哈希
- **会话管理**：Flask-Login集成的会话控制
- **IP白名单**：支持IP地址访问控制
- **安全等级控制**：不同安全等级的操作权限
- **账户状态管理**：支持账户锁定、过期等状态

### 审计日志
完整的操作审计和日志记录功能：
- 记录用户所有重要操作
- 包含操作时间、IP地址、用户代理等信息
- 支持操作成功/失败状态记录
- 便于合规审计和问题追踪

### 数据库迁移
使用Alembic进行版本化的数据库结构管理：
- 支持平滑升级和回滚
- 版本化管理数据库结构变更
- 自动生成迁移脚本
- 支持多环境部署

### 权限装饰器
丰富的权限装饰器系统：
- 支持组合使用多个权限验证
- 简化权限验证代码
- 易于扩展新的权限规则
- 支持细粒度权限控制

### 数据权限
基于组织架构的数据权限控制：
- 用户只能访问权限范围内的数据
- 支持基于组织层级的数据可见性
- 灵活的数据访问策略配置

---

## 📊 核心数据库表结构概览

| 表名 | 描述 | 主要功能 |
|------|------|----------|
| **accounts** | 用户账户表 | 存储用户基本信息、登录凭证、个人设置等 |
| **tenants** | 租户表 | 存储租户信息，实现多租户架构 |
| **tenant_account_joins** | 用户-租户关联表 | 管理用户在不同租户中的角色和权限 |
| **organizations** | 组织架构表 | 存储组织结构信息，支持多级组织 |
| **user_organizations** | 用户-组织关联表 | 管理用户与组织的关联关系 |
| **user_positions** | 用户-岗位关联表 | 管理用户岗位信息 |
| **dify_setups** | 系统设置表 | 存储系统初始化和版本信息 |
| **api_tokens** | API令牌表 | 管理API访问令牌 |
| **end_users** | 终端用户表 | 管理应用的终端用户信息 |
| **account_integrates** | 账户集成表 | 管理第三方账户集成（如OAuth） |

### 表关系图

```
accounts (用户账户)
    ├── tenant_account_joins (用户-租户关联)
    │   └── tenants (租户)
    ├── user_organizations (用户-组织关联)
    │   └── organizations (组织架构)
    ├── user_positions (用户-岗位关联)
    └── account_integrates (账户集成)

apps (应用)
    ├── conversations (对话)
    ├── messages (消息)
    ├── sites (站点)
    └── api_tokens (API令牌)

datasets (数据集)
    ├── documents (文档)
    ├── document_segments (文档片段)
    └── embeddings (向量嵌入)
```

---

## 🔧 开发和部署指南

### 环境要求

- **Python**: 3.8+
- **数据库**: PostgreSQL 12+
- **Redis**: 6.0+
- **包管理器**: UV (推荐) 或 Poetry

### 数据库初始化步骤

1. **安装依赖**
```bash
uv sync --dev
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑.env文件设置数据库连接信息
```

3. **运行数据库迁移**
```bash
uv run flask db upgrade
```

4. **启动应用**
```bash
uv run flask run --host 0.0.0.0 --port=5001 --debug
```

### 数据库迁移命令

```bash
# 生成新的迁移文件
uv run flask db migrate -m "description"

# 应用迁移
uv run flask db upgrade

# 回滚迁移
uv run flask db downgrade

# 查看迁移历史
uv run flask db history
```

---

## 📝 总结

Dify项目实现了一套完整的企业级用户管理和权限控制系统，主要特点包括：

### 1. **架构设计**
采用多租户架构，支持SaaS模式部署，各租户数据完全隔离。每个租户可以独立配置和管理，支持不同的业务需求。

### 2. **权限模型**
基于RBAC(基于角色的访问控制)模型，结合组织架构实现灵活的权限管理。支持细粒度的权限控制，满足复杂的业务场景需求。

### 3. **安全性**
多层次安全保障，包括：
- 密码加密存储
- 会话安全管理
- IP地址访问控制
- 操作审计日志
- 账户状态管理
- 安全等级验证

### 4. **扩展性**
模块化设计，权限装饰器可组合使用，便于扩展新的权限规则。系统架构支持水平扩展，可以适应不断增长的业务需求。

### 5. **可维护性**
使用Alembic进行数据库版本管理，支持平滑升级和回滚。代码结构清晰，便于团队协作开发和维护。

### 6. **企业级特性**
- 支持组织架构管理
- 完整的审计追踪
- 灵活的权限配置
- 多种认证方式
- 数据访问控制

该系统为Dify提供了稳定可靠的用户管理基础，满足企业级应用的安全性、可扩展性和可维护性需求。通过完善的权限控制机制和灵活的组织架构设计，能够适应各种复杂的业务场景和合规要求。

---

## 📚 参考资料

- [Dify官方文档](https://docs.dify.ai/)
- [SQLAlchemy文档](https://docs.sqlalchemy.org/)
- [Flask-Login文档](https://flask-login.readthedocs.io/)
- [Alembic文档](https://alembic.sqlalchemy.org/)
- [PostgreSQL文档](https://www.postgresql.org/docs/)

---

*文档版本：v1.0*  
*最后更新：2024年*  
*维护者：Dify开发团队* 