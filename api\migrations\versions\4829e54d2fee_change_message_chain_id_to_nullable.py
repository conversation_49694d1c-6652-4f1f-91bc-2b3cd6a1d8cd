"""change message chain id to nullable

Revision ID: 4829e54d2fee
Revises: 114eed84c228
Create Date: 2024-01-12 03:42:27.362415

"""
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '4829e54d2fee'
down_revision = '114eed84c228'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('message_agent_thoughts', schema=None) as batch_op:
        batch_op.alter_column('message_chain_id',
               existing_type=postgresql.UUID(),
               nullable=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('message_agent_thoughts', schema=None) as batch_op:
        batch_op.alter_column('message_chain_id',
               existing_type=postgresql.UUID(),
               nullable=False)

    # ### end Alembic commands ###
