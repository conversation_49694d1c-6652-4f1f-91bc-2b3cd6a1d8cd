# 🚀 Dify 前后端联调调试指南

## 📋 当前状态
✅ **后端调试已启动** - Flask API 运行在 127.0.0.1:5001  
🔧 **前端待启动** - 需要配置 Next.js 开发服务器

## 🎯 实现目标
前端页面操作 → 触发后端 API 调用 → 后端断点命中 → 调试变量和逻辑

---

## 第一步：启动前端开发服务器

### 1. 重命名环境配置文件
```powershell
# 在项目根目录执行
Rename-Item "web\web_env_config.txt" "web\.env.local"
```

### 2. 检查 Node.js 环境
```powershell
cd web
node --version  # 需要 >= 18.x
pnpm --version  # 需要 pnpm 包管理器
```

### 3. 安装依赖并启动前端
```powershell
cd web
pnpm install     # 安装依赖
pnpm dev        # 启动开发服务器
```

**预期结果**：前端运行在 http://localhost:3000

---

## 第二步：验证前后端连接

### 环境变量配置确认
前端 `.env.local` 文件中的关键配置：
```env
NEXT_PUBLIC_API_PREFIX=http://127.0.0.1:5001/console/api
NEXT_PUBLIC_PUBLIC_API_PREFIX=http://127.0.0.1:5001/api
```

### 网络调用路径
```
前端页面 (localhost:3000) 
    ↓ AJAX/Fetch 请求
后端 API (127.0.0.1:5001)
    ↓ 断点命中
VSCode 调试器
```

---

## 第三步：设置调试断点

### 常用断点位置

1. **用户认证相关**
   - `api/controllers/console/auth/login.py`
   - `api/services/account_service.py`

2. **应用管理相关**
   - `api/controllers/console/app/`
   - `api/services/app_service.py`

3. **对话和生成相关**
   - `api/controllers/service_api/`
   - `api/core/app/`

### 设置断点方法
1. 在 VSCode 中打开目标文件
2. 点击行号左侧设置红色断点
3. 确保调试会话正在运行（绿色播放按钮）

---

## 第四步：触发调试流程

### 典型调试场景

#### 场景1：用户登录调试
```python
# 在 api/controllers/console/auth/login.py 设置断点
@api.route('/login', methods=['POST'])
def login():
    # 断点设置在这里 ← 
    data = request.get_json()
    # 可以查看登录数据
    breakpoint()  # 或在此行设置 VSCode 断点
```

#### 场景2：创建应用调试
```python
# 在 api/controllers/console/app/apps.py 设置断点
@api.route('/apps', methods=['POST'])
def create_app():
    # 断点设置在这里 ←
    data = request.get_json()
    # 查看创建应用的参数
```

#### 场景3：对话调试
```python
# 在 api/controllers/service_api/completion.py 设置断点
@api.route('/chat-messages', methods=['POST'])
def chat():
    # 断点设置在这里 ←
    data = request.get_json()
    # 查看对话消息数据
```

---

## 第五步：调试操作流程

### 1. 前端操作
- 打开浏览器访问 http://localhost:3000
- 执行任意操作（登录、创建应用、发送消息等）

### 2. 后端断点命中
- VSCode 会自动暂停在断点处
- 左侧显示变量面板
- 底部显示调试控制台

### 3. 调试操作
- **F10**：单步执行（不进入函数）
- **F11**：单步进入（进入函数内部）
- **Shift+F11**：单步退出
- **F5**：继续执行

### 4. 变量查看
- **Variables 面板**：查看当前作用域所有变量
- **Watch 面板**：添加监视表达式
- **Call Stack**：查看函数调用栈
- **Debug Console**：执行 Python 表达式

---

## 🔧 完整启动脚本

### PowerShell 一键启动脚本
```powershell
# 保存为 start_debug.ps1

# 检查后端是否已启动
$apiProcess = Get-Process | Where-Object {$_.ProcessName -eq "python" -and $_.CommandLine -like "*flask*"}
if (-not $apiProcess) {
    Write-Host "后端未启动，请先在 VSCode 中启动 Flask API 调试" -ForegroundColor Yellow
    exit
}

# 启动前端
cd web
Write-Host "启动前端开发服务器..." -ForegroundColor Green
pnpm dev
```

---

## 🐛 常见问题解决

### 前端无法连接后端
**症状**：网络请求失败，控制台显示 CORS 或连接错误

**解决**：
1. 确认后端运行在 127.0.0.1:5001
2. 检查防火墙设置
3. 验证 `.env.local` 中的 API 地址

### 断点不命中
**症状**：前端请求正常，但后端断点不触发

**解决**：
1. 确认断点设置在正确的路由处理函数
2. 检查请求路径是否匹配
3. 确认调试会话正在运行

### Node.js 版本问题
**症状**：`pnpm dev` 失败

**解决**：
```powershell
# 安装 Node.js 18+ 和 pnpm
winget install OpenJS.NodeJS
npm install -g pnpm
```

---

## 🎉 调试技巧

### 1. 条件断点
```python
# 在断点上右键 → 添加条件
# 例如：只在特定用户 ID 时暂停
user_id == "admin"
```

### 2. 日志断点
```python
# 在断点上右键 → 添加日志点
# 输出变量值而不暂停执行
print(f"用户 {user_id} 正在登录")
```

### 3. 监视复杂表达式
```python
# 在 Watch 面板添加
request.json.get('query', '')
app.config['DATABASE_URL']
```

现在您可以享受无缝的前后端联调体验了！🐾 