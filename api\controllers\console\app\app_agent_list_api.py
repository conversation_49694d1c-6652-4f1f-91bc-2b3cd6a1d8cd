import logging
import uuid
from typing import cast, Dict, Any, List

from flask import request
from flask_login import current_user
from flask_restful import Resource, inputs, marshal, marshal_with, reqparse
from sqlalchemy import select
from sqlalchemy.orm import Session
from werkzeug.exceptions import BadRequest, Forbidden, Unauthorized, abort

# 延迟导入以避免循环依赖
from controllers.console.app.wraps import get_app_model
from controllers.console.wraps import (
    account_initialization_required,
    cloud_edition_billing_resource_check,
    enterprise_license_required,
    setup_required,
)
from core.ops.ops_trace_manager import OpsTraceManager
from extensions.ext_database import db
from fields.app_fields import app_detail_fields, app_detail_fields_with_site, app_pagination_fields
from libs.login import login_required
from libs.token_auth import token_required, get_current_user_info
from models import Account, App
from services.app_dsl_service import AppDslService, ImportMode
from services.app_service import AppService
from services.enterprise.enterprise_service import EnterpriseService
from services.feature_service import FeatureService

ALLOW_CREATE_APP_MODES = ["chat", "agent-chat", "advanced-chat", "workflow", "completion"]


class AgentListApi(Resource):
    """获取智能体记录 - 基于AppListApi框架，支持TokenUtils认证和传统认证"""
    
    @setup_required
    @token_required()  # 新的token认证装饰器，支持降级到传统认证
    @login_required
    @account_initialization_required
    @enterprise_license_required
    def get(self):
        """获取指定用户拥有的智能体列表"""
        
        # 解析请求参数
        args = self._parse_request_params()
        
        # 添加调试日志
        import logging
        from flask import g
        from flask_login import current_user
        logging.info(f"AgentListApi.get() - use_token_auth: {getattr(g, 'use_token_auth', False)}, current_user.id: {current_user.id}")
        
        # 获取应用列表 - 像老接口一样直接使用current_user
        app_pagination = self._get_app_list(args)
        
        if not app_pagination:
            return self._format_empty_response(args)
        
        # 为每个应用添加运行地址信息
        self._add_app_urls(app_pagination.items)
        
        # 处理企业版功能
        if FeatureService.get_system_features().webapp_auth.enabled:
            self._add_webapp_auth_info(app_pagination.items)
        
        # 根据认证方式选择响应格式
        from flask import g
        if hasattr(g, 'use_token_auth') and g.use_token_auth:
            # 使用Token验证时，转换为新格式
            processed_apps = self._process_app_data(app_pagination.items)
            return self._format_response(app_pagination, processed_apps, args)
        else:
            # 使用传统认证时，使用原有格式
            return marshal(app_pagination, app_pagination_fields), 200

    def _parse_request_params(self) -> Dict[str, Any]:
        """解析请求参数 - 基于AppListApi的参数解析"""
        def uuid_list(value):
            try:
                return [str(uuid.UUID(v)) for v in value.split(",")]
            except ValueError:
                raise BadRequest("Invalid UUID format in tag_ids.")

        parser = reqparse.RequestParser()
        
        # 基础参数 - 基于AppListApi
        parser.add_argument("page", type=inputs.int_range(1, 99999), required=False, default=1, location="args")
        # 【修改】limit改为page_size
        parser.add_argument("page_size", type=inputs.int_range(1, 100), required=False, default=20, location="args")
        parser.add_argument(
            "mode",
            type=str,
            choices=[
                "completion",
                "chat",
                "advanced-chat",
                "workflow",
                "agent-chat",
                "channel",
                "all",
            ],
            default="all",
            location="args",
            required=False,
        )
        parser.add_argument("name", type=str, location="args", required=False)
        parser.add_argument("tag_ids", type=uuid_list, location="args", required=False)
        parser.add_argument("is_created_by_me", type=inputs.boolean, location="args", required=False)
        
        # 【新增】access_token参数（用于兼容）- 必填
        parser.add_argument("access_token", type=str, required=False, location="args")
        # 【新增】refer参数 - 必填
        parser.add_argument("refer", type=str, required=True, location="args")
        # 【新增】client_id参数
        parser.add_argument("client_id", type=str, required=False, location="args")

        args = parser.parse_args()
        
        # 将page_size转换为limit以兼容AppService
        args["limit"] = args.pop("page_size")
        
        return args

    def _get_app_list(self, args: Dict[str, Any]):
        """获取应用列表 - 像老接口一样直接使用current_user"""
        app_service = AppService()
        
        # 像老接口一样直接使用current_user
        from flask_login import current_user
        
        # 添加调试日志
        import logging
        logging.info(f"AgentListApi - user_id: {current_user.id}, tenant_id: {current_user.current_tenant_id}")
        
        return app_service.get_paginate_apps(current_user.id, current_user.current_tenant_id, args)

    def _add_app_urls(self, apps):
        """为应用添加URL信息 - 基于AppListApi的逻辑"""
        for app in apps:
            if app.enable_site and app.site and app.site.code:
                # 构建工作编排页面地址
                app.run_url = f"{app.site.app_base_url}/app/{app.site.app_id}/{app.mode}"
                # 构建前端网页问答地址
                app.chat_url = f"{app.site.app_base_url}/chat/{app.site.code}"
            else:
                app.run_url = None
                app.chat_url = None

    def _add_webapp_auth_info(self, apps):
        """添加WebApp认证信息 - 基于AppListApi的逻辑"""
        app_ids = [str(app.id) for app in apps]
        res = EnterpriseService.WebAppAuth.batch_get_app_access_mode_by_id(app_ids=app_ids)
        if len(res) != len(app_ids):
            raise BadRequest("Invalid app id in webapp auth")

        for app in apps:
            if str(app.id) in res:
                app.access_mode = res[str(app.id)].access_mode

    def _process_app_data(self, apps) -> List[Dict[str, Any]]:
        """处理应用数据并转换为新格式"""
        processed_apps = []
        
        for app in apps:
            # 转换为新的响应格式
            app_dict = self._convert_app_to_dict(app)
            processed_apps.append(app_dict)
        
        return processed_apps

    def _convert_app_to_dict(self, app) -> Dict[str, Any]:
        """将应用对象转换为新的响应格式"""
        return {
            "id": str(app.id),  # app_id
            "title": app.name,  # 【修改】name改为title
            "mode": app.mode,
            "run_url": getattr(app, 'run_url', None),  # 智能体编排页面跳转地址
            "chat_url": getattr(app, 'chat_url', None),  # 问答页面跳转地址
            "icon_type": app.icon_type,
            "images": app.icon,  # 【修改】icon改为images
            "icon_background": app.icon_background,
            "brief": app.description,  # 【修改】description改为brief
            "created_at": app.created_at.isoformat() if app.created_at else None,
            "created_by": str(app.created_by) if hasattr(app, 'created_by') else None,  # 【新增】created_by
            "user_name": getattr(app, 'user_name', None),  # 【新增】user_name
            "update_time": app.updated_at.isoformat() if app.updated_at else None,  # 【修改】updated_at改为update_time
            "enable_site": app.enable_site,  # 【新增】enable_site
            "enable_api": app.enable_api,  # 【新增】enable_api
            "is_universal": getattr(app, 'is_universal', False),  # 【新增】is_universal
        }

    def _format_response(self, app_pagination, processed_apps: List[Dict[str, Any]], args: Dict[str, Any]):
        """格式化响应数据"""
        return {
            "data": processed_apps,
            "total": app_pagination.total,
            "page": app_pagination.page,
            "page_size": args.get("limit", 20),  # 【修改】limit改为page_size
            "has_more": app_pagination.has_next
        }, 200

    def _format_empty_response(self, args: Dict[str, Any]):
        """格式化空响应"""
        return {
            "data": [],
            "total": 0,
            "page": 1,
            "page_size": args.get("limit", 20),  # 【修改】limit改为page_size
            "has_more": False
        }

    @setup_required
    @login_required
    @account_initialization_required
    @marshal_with(app_detail_fields)
    @cloud_edition_billing_resource_check("apps")
    def post(self):
        """Create app - 保持原有的创建应用功能"""
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, required=True, location="json")
        parser.add_argument("description", type=str, location="json")
        parser.add_argument("mode", type=str, choices=ALLOW_CREATE_APP_MODES, location="json")
        parser.add_argument("icon_type", type=str, location="json")
        parser.add_argument("icon", type=str, location="json")
        parser.add_argument("icon_background", type=str, location="json")
        args = parser.parse_args()

        # The role of the current user in the ta table must be admin, owner, or editor
        if not current_user.is_editor:
            raise Forbidden()

        if "mode" not in args or args["mode"] is None:
            raise BadRequest("mode is required")

        app_service = AppService()
        app = app_service.create_app(current_user.current_tenant_id, args, current_user)

        return app, 201



# 注意：路由注册已移至app.py文件中以避免循环导入

