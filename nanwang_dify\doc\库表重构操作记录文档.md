 # Dify 数据库字段更新操作指南

## 概述

本文档记录了在 Dify 项目中为 `account` 表添加新字段的完整操作流程，包括数据库迁移、问题解决和代码适配。

## 操作环境

- **操作系统**: Windows 10 (win32 10.0.22631)
- **项目路径**: F:\projects\dify
- **数据库**: PostgreSQL (通过 Docker Compose 运行)
- **包管理器**: UV (替代了 Poetry)
- **迁移工具**: Alembic + Flask-Migrate

## 第一步：数据库字段添加

### 1.1 修改数据模型

**文件**: `api/models/account.py`

在 `Account` 类中添加了以下新字段：

```python
# === 数字身份平台必要字段 ===
login_name = db.Column(db.String(100), nullable=False, unique=True, comment='用户登录名，4AId，例如 <EMAIL>')
base_org_id = db.Column(db.String(32), nullable=True, comment='机构ID，组织的唯一ID')
start_date = db.Column(db.Date, nullable=True, comment='临时用户生效时间，格式YYYY-MM-DD')
end_date = db.Column(db.Date, nullable=True, comment='临时用户失效时间，格式YYYY-MM-DD')

# === 数字身份平台可选字段 ===
gender = db.Column(db.String(8), nullable=True, comment='性别')
identity_no = db.Column(db.String(30), nullable=True, comment='身份证号')
birth_day = db.Column(db.Date, nullable=True, comment='生日，格式YYYY-MM-DD')
mobile = db.Column(db.String(30), nullable=True, comment='手机')
office_phone = db.Column(db.String(30), nullable=True, comment='办公座机')
employ_no = db.Column(db.String(30), nullable=True, comment='工号，用户的员工号')
base_post_id = db.Column(db.String(8), nullable=True, comment='岗位ids')
job = db.Column(db.String(80), nullable=True, comment='职称')
duty = db.Column(db.String(30), nullable=True, comment='职务')
degree_code = db.Column(db.String(30), nullable=True, comment='文化程度')
comments = db.Column(db.String(256), nullable=True, comment='备注')
sap_hr_user_id = db.Column(db.String(32), nullable=True, comment='人资编码，HR唯一ID，对应主数据-员工的MRID')
user_type = db.Column(db.String(256), nullable=True, server_default='EMPLOYEE',
                      comment='用户类型：EMPLOYEE内部用户、TEMP临时用户')
```

### 1.2 添加索引

```python
__table_args__ = (
    db.PrimaryKeyConstraint("id", name="account_pkey"),
    db.Index("account_email_idx", "email"),
    db.Index("account_login_name_idx", "login_name"),  # 新增索引
    db.Index("account_base_org_id_idx", "base_org_id")  # 新增索引
)
```

## 第二步：数据库迁移

### 2.1 初始迁移尝试

**命令**:
```bash
cd api
uv run flask db migrate -m "add new fields to account table"
```

**问题**: 遇到数据库版本不匹配错误
```
ERROR [flask_migrate] Error: Can't locate revision identified by '32a8e3a19d45'
```

### 2.2 数据库版本修复

创建了修复脚本 `fix_migration.py`:

```python
#!/usr/bin/env python3
"""
Script to fix the database migration version issue.
"""

import psycopg2
from psycopg2 import sql

# Database connection parameters
DB_HOST = "localhost"
DB_PORT = 5432
DB_NAME = "dify"
DB_USER = "postgres"
DB_PASSWORD = "difyai123456"

def fix_migration_version():
    """Fix the migration version by updating the alembic_version table."""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        
        cursor = conn.cursor()
        
        # Get the latest migration version from the files
        latest_version = "fca025d3b60f"
        
        # Update the alembic_version table
        update_query = sql.SQL("UPDATE alembic_version SET version_num = %s")
        cursor.execute(update_query, (latest_version,))
        
        conn.commit()
        print(f"Successfully updated alembic_version to: {latest_version}")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    fix_migration_version()
```

**执行命令**:
```bash
python fix_migration.py
```

**结果**: 成功修复数据库版本
```
Successfully updated alembic_version to: fca025d3b60f
Current version in database: fca025d3b60f
```

### 2.3 数据库重置方案

由于迁移历史不一致，采用了数据库重置方案。创建了 `reset_database.py`:

```python
#!/usr/bin/env python3
"""
Script to reset the database by dropping and recreating it, then running all migrations.
"""

import psycopg2
from psycopg2 import sql
import subprocess
import time

def drop_database():
    """Drop the dify database if it exists."""
    # ... 删除数据库逻辑

def create_database():
    """Create the dify database."""
    # ... 创建数据库逻辑
00
def run_flask_command(command):
    """Run a flask command in the api directory."""
    # ... 执行 Flask 命令逻辑

def main():
    print("=== Database Reset and Migration Process ===")
    
    # Step 1: Drop the existing database
    drop_database()
    
    # Step 2: Create a new database
    create_database()
    
    # Step 3: Run all migrations from scratch
    run_flask_command("db upgrade")
    
    # Step 4: Generate new migration for account table changes
    run_flask_command("db migrate -m add_new_fields_to_account_table")
    
    # Step 5: Apply the new migration
    run_flask_command("db upgrade")
    
    print("=== Database reset and migration completed successfully! ===")

if __name__ == "__main__":
    main()
```

**执行命令**:
```bash
python reset_database.py
```

**结果**: 成功完成数据库重置和迁移
```
✓ New migration applied successfully
=== Database reset and migration completed successfully! ===
Your account table now has all the new fields you added.
```

## 第三步：代码适配

### 3.1 问题分析

原有的注册接口没有为新字段提供默认值，特别是必填字段 `login_name`，会导致注册失败。

### 3.2 解决方案

修改 `api/services/account_service.py` 中的 `create_account` 方法：

```python
@staticmethod
def create_account(
    email: str,
    name: str,
    interface_language: str,
    password: Optional[str] = None,
    interface_theme: str = "light",
    is_setup: Optional[bool] = False,
) -> Account:
    # ... 原有代码 ...
    
    # Set default values for new required fields
    account.login_name = email  # Use email as login_name by default
    account.user_type = UserType.EMPLOYEE.value  # Default to employee
    account.status = AccountStatus.ACTIVE.value
    account.initialized_at = db.func.current_timestamp()
    
    # Set optional fields with reasonable defaults
    account.base_org_id = None
    account.start_date = None
    account.end_date = None
    account.gender = None
    account.identity_no = None
    account.birth_day = None
    account.mobile = None
    account.office_phone = None
    account.employ_no = None
    account.base_post_id = None
    account.job = None
    account.duty = None
    account.degree_code = None
    account.comments = None
    account.sap_hr_user_id = None
    
    # ... 原有代码 ...
```

### 3.3 添加必要的导入

```python
from models.account import (
    Account,
    AccountIntegrate,
    AccountStatus,
    Tenant,
    TenantAccountJoin,
    TenantAccountRole,
    TenantStatus,
    UserType,  # 新增导入
)
```

## 第四步：测试验证

### 4.1 创建测试脚本

创建了 `test_account_creation.py`:

```python
#!/usr/bin/env python3
"""
Test script to verify account creation works with new required fields
"""

from api.services.account_service import AccountService
from api.extensions.ext_database import db
from api.models.account import Account

def test_account_creation():
    """Test that account creation works with new required fields."""
    try:
        # Create a test account
        account = AccountService.create_account(
            email="<EMAIL>",
            name="Test User",
            interface_language="en-US",
            password="testpassword123"
        )
        
        print(f"✅ Account created successfully!")
        print(f"   Login Name: {account.login_name}")
        print(f"   User Type: {account.user_type}")
        print(f"   Status: {account.status}")
        
        # Verify required fields are set
        if account.login_name and account.user_type and account.status:
            print("✅ All required fields are properly set!")
            return True
        else:
            print("❌ Some required fields are missing!")
            return False
            
    except Exception as e:
        print(f"❌ Account creation test FAILED: {e}")
        return False

if __name__ == "__main__":
    test_account_creation()
```

## 完整操作流程总结

### 1. 环境准备
```bash
cd F:\projects\dify
```

### 2. 修改数据模型
- 编辑 `api/models/account.py`
- 添加新字段和索引

### 3. 数据库迁移
```bash
# 修复数据库版本问题
python fix_migration.py

# 重置数据库并应用迁移
python reset_database.py
```

### 4. 代码适配
- 修改 `api/services/account_service.py`
- 添加新字段的默认值设置

### 5. 测试验证
```bash
python test_account_creation.py
```

## 关键命令汇总

| 操作 | 命令 | 说明 |
|------|------|------|
| 生成迁移脚本 | `uv run flask db migrate -m "message"` | 在 api 目录下执行 |
| 应用迁移 | `uv run flask db upgrade` | 在 api 目录下执行 |
| 检查迁移状态 | `uv run flask db current` | 在 api 目录下执行 |
| 修复数据库版本 | `python fix_migration.py` | 在根目录下执行 |
| 重置数据库 | `python reset_database.py` | 在根目录下执行 |
| 测试账户创建 | `python test_account_creation.py` | 在根目录下执行 |

## 注意事项

1. **数据库备份**: 在生产环境中执行重置操作前，务必备份数据库
2. **前端兼容**: 由于前端代码在 Docker 中，所有修改都在后端完成
3. **默认值**: 新字段的默认值确保向后兼容，后续可通过界面更新
4. **索引优化**: 为新字段添加了适当的索引以提高查询性能

## 文件清单

### 修改的文件
- `api/models/account.py` - 添加新字段和索引
- `api/services/account_service.py` - 添加默认值设置

### 创建的脚本
- `fix_migration.py` - 数据库版本修复脚本
- `reset_database.py` - 数据库重置脚本
- `test_account_creation.py` - 测试脚本

### 文档
- `DATABASE_MIGRATION_GUIDE.md` - 本操作指南
- `ACCOUNT_FIELD_FIX_README.md` - 字段修复说明

## 后续工作

1. **API 扩展**: 创建更新用户信息的 API 接口
2. **数据迁移**: 为现有用户数据设置新字段的默认值
