"""add-tags-and-binding-table

Revision ID: 3c7cac9521c6
Revises: c3311b089690
Create Date: 2024-04-11 06:17:34.278594

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '3c7cac9521c6'
down_revision = 'c3311b089690'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tag_bindings',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', postgresql.UUID(), nullable=True),
    sa.Column('tag_id', postgresql.UUID(), nullable=True),
    sa.Column('target_id', postgresql.UUID(), nullable=True),
    sa.Column('created_by', postgresql.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='tag_binding_pkey')
    )
    with op.batch_alter_table('tag_bindings', schema=None) as batch_op:
        batch_op.create_index('tag_bind_tag_id_idx', ['tag_id'], unique=False)
        batch_op.create_index('tag_bind_target_id_idx', ['target_id'], unique=False)

    op.create_table('tags',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', postgresql.UUID(), nullable=True),
    sa.Column('type', sa.String(length=16), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('created_by', postgresql.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='tag_pkey')
    )
    with op.batch_alter_table('tags', schema=None) as batch_op:
        batch_op.create_index('tag_name_idx', ['name'], unique=False)
        batch_op.create_index('tag_type_idx', ['type'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tags', schema=None) as batch_op:
        batch_op.drop_index('tag_type_idx')
        batch_op.drop_index('tag_name_idx')

    op.drop_table('tags')
    with op.batch_alter_table('tag_bindings', schema=None) as batch_op:
        batch_op.drop_index('tag_bind_target_id_idx')
        batch_op.drop_index('tag_bind_tag_id_idx')

    op.drop_table('tag_bindings')
    # ### end Alembic commands ###
