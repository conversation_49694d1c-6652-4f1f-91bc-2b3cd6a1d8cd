background: '#c0cdff'
configurate_methods:
- predefined-model
description:
  en_US: Models provided by deepseek, such as deepseek-reasoner、deepseek-chat、deepseek-coder.
  zh_Hans: 深度求索提供的模型，例如 deepseek-reasoner、deepseek-chat、deepseek-coder 。
extra:
  python:
    model_sources:
    - models/llm/llm.py
    provider_source: provider/deepseek.py
help:
  title:
    en_US: Get your API Key from deepseek
    zh_Hans: 从深度求索获取 API Key
  url:
    en_US: https://platform.deepseek.com/api_keys
icon_large:
  en_US: icon_l_en.svg
icon_small:
  en_US: icon_s_en.svg
label:
  en_US: deepseek
  zh_Hans: 深度求索
models:
  llm:
    position: models/llm/_position.yaml
    predefined:
    - models/llm/*.yaml
provider: deepseek
provider_credential_schema:
  credential_form_schemas:
  - label:
      en_US: API Key
    placeholder:
      en_US: Enter your API Key
      zh_Hans: 在此输入您的 API Key
    required: true
    type: secret-input
    variable: api_key
  - label:
      en_US: Custom API endpoint URL
      zh_Hans: 自定义 API endpoint 地址
    placeholder:
      en_US: Base URL, e.g. https://api.deepseek.com/v1 or https://api.deepseek.com
      zh_Hans: Base URL, e.g. https://api.deepseek.com/v1 or https://api.deepseek.com
    required: false
    type: text-input
    variable: endpoint_url
supported_model_types:
- llm
