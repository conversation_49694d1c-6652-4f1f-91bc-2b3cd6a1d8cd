"""add custom config in tenant

Revision ID: 88072f0caa04
Revises: fca025d3b60f
Create Date: 2023-12-14 07:36:50.705362

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '88072f0caa04'
down_revision = '246ba09cbbdb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tenants', schema=None) as batch_op:
        batch_op.add_column(sa.Column('custom_config', sa.Text(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tenants', schema=None) as batch_op:
        batch_op.drop_column('custom_config')

    # ### end Alembic commands ###
