# 应用列表接口地址功能

## 概述

在应用列表接口 `/console/api/apps` 中新增了两个地址字段：
- `run_url`: 工作编排页面地址
- `chat_url`: 前端网页问答地址

这两个地址分别对应不同的使用场景。

## 功能说明

### 地址生成规则

1. **启用网站的应用**: 如果应用启用了网站功能（`enable_site = true`）且有对应的Site记录和code，则生成两个地址
2. **未启用网站的应用**: 两个地址都返回 `null`

### 地址格式

#### 工作编排页面地址 (run_url)
```
{app_base_url}/app/{site_code}
```

#### 前端网页问答地址 (chat_url)
```
{app_base_url}/chat/{site_code}
```

其中：
- `app_base_url`: 从Site模型的app_base_url属性获取，通常是 `dify_config.APP_WEB_URL` 或当前请求的根URL
- `site_code`: Site模型中的code字段，是应用的唯一标识符

### 使用场景

- **run_url**: 用于访问应用的工作编排页面，管理员可以在这里配置和调试应用
- **chat_url**: 用于访问应用的前端问答页面，用户可以直接与智能体进行对话

## API 接口

### 获取应用列表

**接口地址:** `GET /console/api/apps`

**请求参数:**
- `mode`: 应用模式过滤（可选）
  - `agent-chat`: 智能体应用
  - `chat`: 对话应用
  - `completion`: 文本生成应用
  - `workflow`: 工作流应用
  - `advanced-chat`: 高级对话应用
  - `channel`: 渠道应用
  - `all`: 全部应用（默认）

**响应示例:**
```json
{
  "data": [
    {
      "id": "app_id",
      "name": "智能客服助手",
      "mode": "agent-chat",
      "description": "智能客服应用",
      "icon_type": "emoji",
      "icon": "🤖",
      "icon_background": "#FF6B6B",
      "run_url": "https://your-domain.com/app/abc123def456",
      "chat_url": "https://your-domain.com/chat/abc123def456",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    },
    {
      "id": "app_id_2",
      "name": "文本生成器",
      "mode": "completion",
      "description": "文本生成应用",
      "icon_type": "emoji",
      "icon": "✍️",
      "icon_background": "#4ECDC4",
      "run_url": null,
      "chat_url": null,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 2,
  "page": 1,
  "limit": 20,
  "has_more": false
}
```

## 使用示例

### 获取所有智能体应用及其地址

```bash
curl -X GET "http://localhost:5001/console/api/apps?mode=agent-chat" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Python 示例

```python
import requests

def get_agent_apps_with_urls():
    headers = {
        "Authorization": "Bearer YOUR_TOKEN",
        "Content-Type": "application/json"
    }
    
    response = requests.get(
        "http://localhost:5001/console/api/apps?mode=agent-chat",
        headers=headers
    )
    
    if response.status_code == 200:
        data = response.json()
        for app in data['data']:
            print(f"应用: {app['name']}")
            print(f"工作编排地址: {app['run_url']}")
            print(f"前端问答地址: {app['chat_url']}")
            print("---")
```

## 注意事项

1. **权限要求**: 需要有效的API token或用户登录
2. **网站启用**: 只有启用了网站功能的应用才会有地址
3. **地址有效性**: 地址的有效性取决于应用的网站配置和服务器状态
4. **安全性**: 地址可能包含敏感信息，请妥善处理
5. **使用场景**: 
   - `run_url` 主要用于管理员访问工作编排页面
   - `chat_url` 主要用于最终用户访问问答页面

## 相关文件

- `api/controllers/console/app/app.py`: 应用列表接口实现
- `api/fields/app_fields.py`: 应用字段定义
- `api/models/model.py`: App和Site模型定义

## 测试

使用提供的测试脚本 `test_app_list_with_run_url.py` 来验证功能：

```bash
python test_app_list_with_run_url.py
```

记得在运行测试前替换脚本中的API token。 