"""enable tool file without conversation id

Revision ID: 563cf8bf777b
Revises: b5429b71023c
Create Date: 2024-03-14 04:54:56.679506

"""
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '563cf8bf777b'
down_revision = 'b5429b71023c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tool_files', schema=None) as batch_op:
        batch_op.alter_column('conversation_id',
               existing_type=postgresql.UUID(),
               nullable=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tool_files', schema=None) as batch_op:
        batch_op.alter_column('conversation_id',
               existing_type=postgresql.UUID(),
               nullable=False)

    # ### end Alembic commands ###
