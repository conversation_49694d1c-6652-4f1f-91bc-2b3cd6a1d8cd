from pptx import Presentation
from pptx.util import Inches
import requests # 用于下载图片

# 1. 加载AI输出的JSON数据
ai_output_json = { ... } # 就是上面那个JSON

# 2. 创建一个PPT对象，可以基于一个预设的母版模板
# 母版模板里定义了主题颜色、字体和各种版式（如标题页、内容页）
prs = Presentation("my_template.pptx")

# 3. 遍历JSON中的每一张幻灯片信息
for slide_data in ai_output_json["slides"]:

    # 4. 根据JSON中指定的'layout'，从母版中选择对应的幻灯片版式
    # python-pptx用索引来选择版式，比如0是标题页，1是内容页等
    # 实际应用中会有一个映射关系： "title_slide" -> 0, "title_and_content" -> 1
    slide_layout = prs.slide_layouts[layout_mapping[slide_data["layout"]]]

    # 5. 添加一张新的幻灯片
    slide = prs.slides.add_slide(slide_layout)

    # 6. 获取新幻灯片上的占位符（placeholders），并填充内容
    title_placeholder = slide.shapes.title
    title_placeholder.text = slide_data["content"]["title"]

    # 根据版式不同，填充不同的内容
    if slide_data["layout"] == "title_slide":
        subtitle_placeholder = slide.placeholders[1] # 假设副标题是第二个占位符
        subtitle_placeholder.text = slide_data["content"]["subtitle"]

    elif slide_data["layout"] == "title_and_content":
        content_placeholder = slide.placeholders[1] # 假设内容框是第二个占位符
        # 清空默认文本
        content_placeholder.text_frame.clear()
        # 填充要点
        for point in slide_data["content"]["points"]:
            p = content_placeholder.text_frame.add_paragraph()
            p.text = point
            p.level = 0 # 设置为一级项目符号

        # 7. 如果有图片，下载并插入
        if "image_url" in slide_data["content"]:
            image_url = slide_data["content"]["image_url"]
            response = requests.get(image_url, stream=True)
            # 定义图片插入的位置和大小
            slide.shapes.add_picture(response.raw, Inches(5), Inches(2), width=Inches(4))

# 8. 所有幻灯片都添加完毕后，保存文件
prs.save("AI生成的演示文稿.pptx")

print("PPT文件已成功生成！")
