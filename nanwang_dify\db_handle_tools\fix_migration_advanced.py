#!/usr/bin/env python3
"""
Advanced script to fix migration issues and generate new migration for account table.
"""

import psycopg2
from psycopg2 import sql
import subprocess
import sys
import os

# Database connection parameters
DB_HOST = "localhost"
DB_PORT = 5432
DB_NAME = "dify"
DB_USER = "postgres"
DB_PASSWORD = "difyai123456"

def get_latest_migration_version():
    """Get the latest migration version from the files."""
    # Based on the file listing, the latest version is fca025d3b60f
    return "fca025d3b60f"

def mark_database_as_up_to_date():
    """Mark the database as being up to date with the latest migration."""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        
        cursor = conn.cursor()
        
        # Get the latest version
        latest_version = get_latest_migration_version()
        
        # Update the alembic_version table
        update_query = sql.SQL("UPDATE alembic_version SET version_num = %s")
        cursor.execute(update_query, (latest_version,))
        
        # Commit the changes
        conn.commit()
        
        print(f"Successfully marked database as up to date: {latest_version}")
        
    except Exception as e:
        print(f"Error marking database as up to date: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def run_flask_command(command):
    """Run a flask command in the api directory."""
    try:
        # Change to api directory
        api_dir = os.path.join(os.getcwd(), 'api')
        if not os.path.exists(api_dir):
            print(f"Error: api directory not found at {api_dir}")
            return False
            
        # Run the command
        result = subprocess.run(
            ['uv', 'run', 'flask'] + command.split(),
            cwd=api_dir,
            capture_output=True,
            text=True
        )
        
        print("STDOUT:", result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"Error running flask command: {e}")
        return False

def main():
    print("=== Fixing Database Migration Issues ===")
    
    # Step 1: Mark database as up to date
    print("\n1. Marking database as up to date...")
    mark_database_as_up_to_date()
    
    # Step 2: Check current status
    print("\n2. Checking current migration status...")
    if run_flask_command("db current"):
        print("✓ Migration status check successful")
    else:
        print("✗ Migration status check failed")
        return
    
    # Step 3: Generate new migration for account table
    print("\n3. Generating new migration for account table changes...")
    if run_flask_command("db migrate -m add_new_fields_to_account_table"):
        print("✓ Migration generation successful")
    else:
        print("✗ Migration generation failed")
        return
    
    # Step 4: Apply the new migration
    print("\n4. Applying the new migration...")
    if run_flask_command("db upgrade"):
        print("✓ Migration application successful")
    else:
        print("✗ Migration application failed")
        return
    
    print("\n=== All operations completed successfully! ===")

if __name__ == "__main__":
    main() 