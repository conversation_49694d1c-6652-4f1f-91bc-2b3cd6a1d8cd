from datetime import datetime
import json
from flask import request
from models.account import Account
from models.model import OperationLog
from extensions.ext_database import db
from services.account_service import TenantService


class OperationLogService:
    @staticmethod
    def _get_client_ip():
        """获取客户端IP地址"""
        return request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))

    @staticmethod
    def _log(account: Account, action: str, content: dict, status: str = "SUCCESS", error_msg: str = None):
        """
        通用日志记录方法
        :param account: 操作账户
        :param action: 操作名称 (例如: 'USER_LOGIN', 'SSO_LOGIN')
        :param content: 附加的日志内容
        :param status: 操作状态 (SUCCESS / FAILED)
        :param error_msg: 错误信息 (如果失败)
        """
        print(f"--- [DEBUG] Attempting to log operation: {action} for account {account.id} ---") # 添加调试打印
        
        # 确保account对象有关联的租户ID
        tenant_id = account.current_tenant_id
        if not tenant_id:
            tenants = TenantService.get_join_tenants(account)
            if tenants:
                tenant_id = tenants[0].id

        try:
            log_content = {
                'operation': action,
                'status': status,
                'user_agent': request.headers.get('User-Agent', ''),
                'request_method': request.method,
                'request_url': request.url,
                **content
            }

            if error_msg:
                log_content['error_message'] = error_msg
            
            if not tenant_id:
                # 如果在所有尝试后仍然没有tenant_id，我们必须处理这个情况
                # 方案A：抛出异常，让调用者知道。
                # raise ValueError(f"Cannot determine tenant_id for account {account.id}")
                # 方案B：记录日志，但是tenant_id为空（需要修改数据库表结构）
                # 方案C：使用一个特殊的或默认的tenant_id（不推荐）
                # 当前我们选择抛出异常或记录错误，但为了不中断主流程，这里先打印错误
                print(f"--- [ERROR] Could not determine tenant_id for account {account.id} during logging. Log skipped. ---")
                return

            operation_log = OperationLog(
                tenant_id=tenant_id,
                account_id=account.id,
                action=action,
                content=log_content,
                created_ip=OperationLogService._get_client_ip()
            )

            db.session.add(operation_log)
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            # 记录日志失败不应该影响主要业务流程
            print(f"Failed to log operation: {e}")

    @staticmethod
    def log_login(account: Account, provider: str = None):
        """
        记录成功的登录事件 (包括标准登录和OAuth)
        :param account: 登录的账户
        :param provider: OAuth提供商 (例如: 'github', 'google')
        """
        action = "SSO_LOGIN" if provider else "USER_LOGIN"
        content = {
            'user_id': account.id,
            'user_email': account.email,
            'user_name': account.name,
        }
        if provider:
            content['provider'] = provider
        
        OperationLogService._log(account=account, action=action, content=content)

    @staticmethod
    def log_logout(account: Account):
        """
        记录成功的登出事件
        :param account: 登出的账户
        """
        action = "USER_LOGOUT"
        content = {
            'user_id': account.id,
            'user_email': account.email,
            'user_name': account.name,
        }
        
        OperationLogService._log(account=account, action=action, content=content)

    @staticmethod
    def log_login_failure(email: str, error_msg: str):
        """
        记录失败的登录尝试
        :param email: 尝试登录的邮箱
        :param error_msg: 失败原因
        """
        # 尝试根据email查找用户以获取tenant_id和account_id
        account = db.session.query(Account).filter(Account.email == email).first()

        content = {
            'operation': 'LOGIN_FAILED',
            'status': 'FAILED',
            'email_attempted': email,
            'user_agent': request.headers.get('User-Agent', ''),
            'request_method': request.method,
            'request_url': request.url,
            'error_message': error_msg
        }

        if account:
            # 如果找到了账户，就使用该账户的信息记录日志
            OperationLogService._log(account=account, action='LOGIN_FAILED', content=content, status='FAILED', error_msg=error_msg)
        else:
            # 如果找不到账户（例如，邮箱输错了），则不记录日志或记录到一个单独的、允许空值的地方
            # 当前选择不记录，以避免数据库错误
            print(f"--- [DEBUG] Login failure for non-existent email: {email}. Log skipped. ---")

