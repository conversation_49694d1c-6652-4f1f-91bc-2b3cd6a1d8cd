import { Ri<PERSON><PERSON>ckLine } from '@remixicon/react'
import cn from '@/utils/classnames'
import IndeterminateIcon from './assets/indeterminate-icon'

type CheckboxProps = {
  id?: string
  checked?: boolean
  onCheck?: () => void
  className?: string
  disabled?: boolean
  indeterminate?: boolean
}

const Checkbox = ({
    id,
    checked,
    onCheck,
    className,
    disabled,
    indeterminate,
}: CheckboxProps) => {
  const checkClassName = (checked || indeterminate)
    ? 'bg-components-checkbox-bg text-components-checkbox-icon hover:bg-components-checkbox-bg-hover'
    : 'border border-components-checkbox-border bg-components-checkbox-bg-unchecked hover:bg-components-checkbox-bg-unchecked-hover hover:border-components-checkbox-border-hover'
  const disabledClassName = (checked || indeterminate)
    ? 'cursor-not-allowed bg-components-checkbox-bg-disabled-checked text-components-checkbox-icon-disabled hover:bg-components-checkbox-bg-disabled-checked'
    : 'cursor-not-allowed border-components-checkbox-border-disabled bg-components-checkbox-bg-disabled hover:border-components-checkbox-border-disabled hover:bg-components-checkbox-bg-disabled'

  return (
    <div
      id={id}
      className={cn(
        'flex h-4 w-4 cursor-pointer items-center justify-center rounded-[4px] shadow-xs shadow-shadow-shadow-3',
        checkClassName,
        disabled && disabledClassName,
        className,
      )}
      onClick={() => {
        if (disabled)
          return
        onCheck?.()
      }}
      data-testid={`checkbox-${id}`}
    >
      {!checked && indeterminate && <IndeterminateIcon />}
      {checked && <RiCheckLine className='h-3 w-3' data-testid={`check-icon-${id}`} />}
    </div>
  )
}

export default Checkbox
