"""add workflow run index

Revision ID: 64a70a7aab8b
Revises: 03f98355ba0e
Create Date: 2024-05-28 12:32:00.276061

"""
from alembic import op

import models as models

# revision identifiers, used by Alembic.
revision = '64a70a7aab8b'
down_revision = '03f98355ba0e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('workflow_runs', schema=None) as batch_op:
        batch_op.create_index('workflow_run_tenant_app_sequence_idx', ['tenant_id', 'app_id', 'sequence_number'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('workflow_runs', schema=None) as batch_op:
        batch_op.drop_index('workflow_run_tenant_app_sequence_idx')

    # ### end Alembic commands ###
