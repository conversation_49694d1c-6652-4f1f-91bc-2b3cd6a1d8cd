"""
本地插件配置加载器
使用Dify标准配置系统读取和管理本地插件配置
"""

import os
import sys
from typing import Dict, Any

# 添加 API 路径以导入Dify配置
current_dir = os.path.dirname(os.path.abspath(__file__))
api_dir = os.path.join(current_dir, '..', 'api')
sys.path.insert(0, api_dir)

from api.configs.app_config import DifyConfig


class LocalPluginConfig:
    """本地插件配置管理器 - 使用Dify标准配置系统"""

    def __init__(self):
        """初始化配置管理器，使用Dify的配置系统"""
        self._config = DifyConfig

    def get_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return {
            "enabled": self.enabled,
            "plugins_dir": self.plugins_dir,
            "verify_signature": self.verify_signature,
            "auto_install_on_startup": self.auto_install_on_startup,
            "auto_install_for_new_users": self.auto_install_for_new_users,
            "log_level": self.log_level,
            "max_retries": self.max_retries,
            "exclude_plugins": self.exclude_plugins,
            "include_plugins": self.include_plugins,
            "force_reinstall": self.force_reinstall,
        }

    @property
    def enabled(self) -> bool:
        """是否启用本地插件自动安装"""
        return self._config.LOCAL_PLUGIN_AUTO_INSTALL_ENABLED

    @property
    def plugins_dir(self) -> str:
        """插件目录路径"""
        return self._config.LOCAL_PLUGIN_AUTO_INSTALL_DIR

    @property
    def verify_signature(self) -> bool:
        """是否验证插件签名"""
        return self._config.LOCAL_PLUGIN_AUTO_INSTALL_VERIFY_SIGNATURE

    @property
    def auto_install_on_startup(self) -> bool:
        """启动时是否自动安装"""
        return self._config.LOCAL_PLUGIN_AUTO_INSTALL_ON_STARTUP

    @property
    def auto_install_for_new_users(self) -> bool:
        """新用户创建时是否自动安装"""
        return self._config.LOCAL_PLUGIN_AUTO_INSTALL_FOR_NEW_USERS

    @property
    def log_level(self) -> str:
        """日志级别"""
        return self._config.LOCAL_PLUGIN_AUTO_INSTALL_LOG_LEVEL

    @property
    def max_retries(self) -> int:
        """最大重试次数"""
        return self._config.LOCAL_PLUGIN_AUTO_INSTALL_MAX_RETRIES

    @property
    def exclude_plugins(self) -> list[str]:
        """排除的插件列表"""
        return self._config.LOCAL_PLUGIN_AUTO_INSTALL_EXCLUDE_PLUGINS_LIST

    @property
    def include_plugins(self) -> list[str]:
        """包含的插件列表"""
        return self._config.LOCAL_PLUGIN_AUTO_INSTALL_INCLUDE_PLUGINS_LIST

    @property
    def force_reinstall(self) -> bool:
        """是否强制重新安装"""
        return self._config.LOCAL_PLUGIN_AUTO_INSTALL_FORCE_REINSTALL


# 全局配置实例
_local_plugin_config = None


def get_local_plugin_config() -> LocalPluginConfig:
    """获取本地插件配置实例"""
    global _local_plugin_config
    if _local_plugin_config is None:
        _local_plugin_config = LocalPluginConfig()
    return _local_plugin_config
