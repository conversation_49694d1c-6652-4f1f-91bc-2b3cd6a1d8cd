#!/usr/bin/env python3
"""
测试应用列表接口是否返回运行地址
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:5001"
API_TOKEN = "your_api_token_here"  # 请替换为实际的API token

def test_app_list_with_run_url():
    """测试应用列表接口"""
    
    headers = {
        "Authorization": f"Bearer {API_TOKEN}",
        "Content-Type": "application/json"
    }
    
    # 测试获取所有应用
    print("=== 测试获取所有应用 ===")
    response = requests.get(f"{BASE_URL}/console/api/apps", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"总应用数: {data.get('total', 0)}")
        print(f"当前页应用数: {len(data.get('data', []))}")
        
        for i, app in enumerate(data.get('data', []), 1):
            print(f"\n应用 {i}:")
            print(f"  ID: {app.get('id')}")
            print(f"  名称: {app.get('name')}")
            print(f"  模式: {app.get('mode')}")
            print(f"  工作编排地址: {app.get('run_url', '未启用网站')}")
            print(f"  前端问答地址: {app.get('chat_url', '未启用网站')}")
            
            # 检查智能体应用
            if app.get('mode') == 'agent-chat':
                print(f"  *** 这是智能体应用 ***")
                if app.get('chat_url'):
                    print(f"  智能体问答地址: {app.get('chat_url')}")
                else:
                    print(f"  智能体未启用网站功能")
    else:
        print(f"请求失败: {response.status_code}")
        print(f"响应内容: {response.text}")
    
    # 测试获取智能体应用
    print("\n\n=== 测试获取智能体应用 ===")
    response = requests.get(f"{BASE_URL}/console/api/apps?mode=agent-chat", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"智能体应用总数: {data.get('total', 0)}")
        
        for i, app in enumerate(data.get('data', []), 1):
            print(f"\n智能体应用 {i}:")
            print(f"  ID: {app.get('id')}")
            print(f"  名称: {app.get('name')}")
            print(f"  工作编排地址: {app.get('run_url', '未启用网站')}")
            print(f"  前端问答地址: {app.get('chat_url', '未启用网站')}")
            
            if app.get('chat_url'):
                print(f"  可以直接访问问答页面: {app.get('chat_url')}")
            if app.get('run_url'):
                print(f"  可以直接访问工作编排页面: {app.get('run_url')}")
    else:
        print(f"请求失败: {response.status_code}")
        print(f"响应内容: {response.text}")

if __name__ == "__main__":
    test_app_list_with_run_url() 