"""add retry_index field to node-execution model

Revision ID: e1944c35e15e
Revises: 11b07f66c737
Create Date: 2024-12-20 06:28:30.287197
"""
from alembic import op
import models as models
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e1944c35e15e'
down_revision = '11b07f66c737'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    # We don't need these fields anymore, but this file is already merged into the main branch,
    # so we need to keep this file for the sake of history, and this change will be reverted in the next migration.
    # with op.batch_alter_table('workflow_node_executions', schema=None) as batch_op:
    #     batch_op.add_column(sa.Column('retry_index', sa.Integer(), server_default=sa.text('0'), nullable=True))

    pass

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # with op.batch_alter_table('workflow_node_executions', schema=None) as batch_op:
    #     batch_op.drop_column('retry_index')
    pass

    # ### end Alembic commands ###
