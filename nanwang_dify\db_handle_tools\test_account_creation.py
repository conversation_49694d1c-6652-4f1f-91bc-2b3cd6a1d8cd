#!/usr/bin/env python3
"""
Test script to verify account creation works with new required fields
"""

import sys
import os

# Add the api directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'api'))

from api.services.account_service import AccountService
from api.extensions.ext_database import db
from api.models.account import Account

def test_account_creation():
    """Test that account creation works with new required fields."""
    
    print("=== Testing Account Creation with New Fields ===")
    
    try:
        # Create a test account
        test_email = "<EMAIL>"
        test_name = "Test User"
        
        # Check if account already exists
        existing_account = db.session.query(Account).filter_by(email=test_email).first()
        if existing_account:
            print(f"Account with email {test_email} already exists, deleting...")
            db.session.delete(existing_account)
            db.session.commit()
        
        # Create new account
        account = AccountService.create_account(
            email=test_email,
            name=test_name,
            interface_language="en-US",
            password="testpassword123"
        )
        
        print(f"✅ Account created successfully!")
        print(f"   ID: {account.id}")
        print(f"   Email: {account.email}")
        print(f"   Name: {account.name}")
        print(f"   Login Name: {account.login_name}")
        print(f"   User Type: {account.user_type}")
        print(f"   Status: {account.status}")
        print(f"   Base Org ID: {account.base_org_id}")
        
        # Verify required fields are set
        if account.login_name and account.user_type and account.status:
            print("✅ All required fields are properly set!")
        else:
            print("❌ Some required fields are missing!")
            return False
            
        # Clean up
        db.session.delete(account)
        db.session.commit()
        print("✅ Test account cleaned up successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ Account creation test FAILED: {e}")
        return False

def main():
    """Run the test."""
    print("Starting Account Creation Test...")
    
    # Initialize database connection (you may need to adjust this based on your setup)
    try:
        result = test_account_creation()
        
        if result:
            print("\n🎉 Account creation test PASSED!")
            print("The new required fields are being set correctly.")
        else:
            print("\n❌ Account creation test FAILED!")
            print("Please check the implementation.")
            
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        print("Make sure the database is running and accessible.")

if __name__ == "__main__":
    main() 