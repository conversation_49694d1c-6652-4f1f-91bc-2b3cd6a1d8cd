#!/usr/bin/env python3
"""
测试脚本：演示如何访问本地插件自动安装配置
"""

import os
import sys

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

def test_direct_config_access():
    """测试直接访问配置"""
    print("=== 测试直接访问配置 ===")

    # 导入全局配置实例
    from api.configs.app_config import dify_config

    # 直接访问配置项
    print(f"启用状态: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_ENABLED}")
    print(f"插件目录: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_DIR}")
    print(f"验证签名: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_VERIFY_SIGNATURE}")
    print(f"启动时安装: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_ON_STARTUP}")
    print(f"新用户安装: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_FOR_NEW_USERS}")
    print(f"日志级别: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_LOG_LEVEL}")
    print(f"最大重试: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_MAX_RETRIES}")
    print(f"排除插件: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_EXCLUDE_PLUGINS_LIST}")
    print(f"包含插件: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_INCLUDE_PLUGINS_LIST}")
    print(f"强制重装: {dify_config.LOCAL_PLUGIN_AUTO_INSTALL_FORCE_REINSTALL}")


def test_plugin_config_loader():
    """测试通过插件配置加载器访问"""
    print("\n=== 测试通过插件配置加载器访问 ===")

    # 导入插件配置加载器
    from api.plugins.config_loader import get_local_plugin_config

    # 获取配置实例
    config = get_local_plugin_config()

    # 访问配置项
    print(f"启用状态: {config.enabled}")
    print(f"插件目录: {config.plugins_dir}")
    print(f"验证签名: {config.verify_signature}")
    print(f"启动时安装: {config.auto_install_on_startup}")
    print(f"新用户安装: {config.auto_install_for_new_users}")
    print(f"日志级别: {config.log_level}")
    print(f"最大重试: {config.max_retries}")
    print(f"排除插件: {config.exclude_plugins}")
    print(f"包含插件: {config.include_plugins}")
    print(f"强制重装: {config.force_reinstall}")

    # 获取完整配置
    full_config = config.get_config()
    print(f"\n完整配置: {full_config}")


def test_environment_variable_override():
    """测试环境变量覆盖"""
    print("\n=== 测试环境变量覆盖 ===")

    # 设置环境变量
    os.environ['LOCAL_PLUGIN_AUTO_INSTALL_ENABLED'] = 'false'
    os.environ['LOCAL_PLUGIN_AUTO_INSTALL_LOG_LEVEL'] = 'DEBUG'
    os.environ['LOCAL_PLUGIN_AUTO_INSTALL_EXCLUDE_PLUGINS'] = 'test-plugin,debug-plugin'

    # 重新导入配置（注意：在实际应用中需要重启应用）
    from api.configs.app_config import DifyConfig

    # 创建新的配置实例
    new_config = DifyConfig()

    print(f"启用状态: {new_config.LOCAL_PLUGIN_AUTO_INSTALL_ENABLED}")
    print(f"日志级别: {new_config.LOCAL_PLUGIN_AUTO_INSTALL_LOG_LEVEL}")
    print(f"排除插件: {new_config.LOCAL_PLUGIN_AUTO_INSTALL_EXCLUDE_PLUGINS_LIST}")

    # 清理环境变量
    del os.environ['LOCAL_PLUGIN_AUTO_INSTALL_ENABLED']
    del os.environ['LOCAL_PLUGIN_AUTO_INSTALL_LOG_LEVEL']
    del os.environ['LOCAL_PLUGIN_AUTO_INSTALL_EXCLUDE_PLUGINS']


def test_config_in_plugin_manager():
    """测试在插件管理器中使用配置"""
    print("\n=== 测试在插件管理器中使用配置 ===")

    from plugins.local_plugin_manager import get_local_plugin_manager

    # 获取插件管理器实例
    manager = get_local_plugin_manager()

    # 访问配置
    print(f"插件目录: {manager.plugins_dir}")
    print(f"验证签名: {manager.config.verify_signature}")
    print(f"排除插件: {manager.config.exclude_plugins}")

    # 测试插件过滤
    test_plugins = ['production-plugin', 'test-plugin', 'debug-plugin', 'auth-plugin']
    for plugin in test_plugins:
        should_install = manager.should_install_plugin(plugin)
        print(f"插件 {plugin}: {'安装' if should_install else '跳过'}")


def main():
    """主函数"""
    print("本地插件自动安装配置访问测试")
    print("=" * 50)

    try:
        test_direct_config_access()
        test_plugin_config_loader()
        test_environment_variable_override()
        test_config_in_plugin_manager()

        print("\n" + "=" * 50)
        print("所有测试完成！")

    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
