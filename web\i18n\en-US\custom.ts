const translation = {
  custom: 'Customization',
  upgradeTip: {
    title: 'Upgrade your plan',
    des: 'Upgrade your plan to customize your brand',
    prefix: 'Upgrade your plan to',
    suffix: 'customize your brand.',
  },
  webapp: {
    title: 'Customize web app brand',
    removeBrand: 'Remove Powered by Dify',
    changeLogo: 'Change Powered by Brand Image',
    changeLogoTip: 'SVG or PNG format with a minimum size of 40x40px',
  },
  app: {
    title: 'Customize app header brand',
    changeLogoTip: 'SVG or PNG format with a minimum size of 80x80px',
  },
  upload: 'Upload',
  uploading: 'Uploading',
  uploadedFail: 'Image upload failed, please re-upload.',
  change: 'Change',
  apply: 'Apply',
  restore: 'Restore Defaults',
  customize: {
    contactUs: ' contact us ',
    prefix: 'To customize the brand logo within the app, please',
    suffix: 'to upgrade to the Enterprise edition.',
  },
}

export default translation
