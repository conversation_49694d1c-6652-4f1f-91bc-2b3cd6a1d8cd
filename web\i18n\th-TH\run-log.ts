const translation = {
  input: 'อินพุต',
  result: 'ผล',
  detail: 'รายละเอียด',
  tracing: 'ติดตาม',
  resultPanel: {
    status: 'สถานะ',
    time: 'เวลาที่ผ่านไป',
    tokens: 'โทเค็นทั้งหมด',
  },
  meta: {
    title: 'ข้อมูลเมตา',
    status: 'สถานะ',
    version: 'เวอร์ชัน',
    executor: 'ผู้ปฏิบัติการ',
    startTime: 'เวลาเริ่มต้น',
    time: 'เวลาที่ผ่านไป',
    tokens: 'โทเค็นทั้งหมด',
    steps: 'เรียกใช้ขั้นตอน',
  },
  resultEmpty: {
    title: 'เรียกใช้เฉพาะรูปแบบ JSON เอาต์พุต',
    tipLeft: 'กรุณาไปที่',
    link: 'แผงรายละเอียด',
    tipRight: 'ดูมัน',
  },
  circularInvocationTip: 'มีการเรียกใช้เครื่องมือ/โหนดแบบวงกลมในเวิร์กโฟลว์ปัจจุบัน',
  actionLogs: 'บันทึกการดําเนินการ',
}

export default translation
