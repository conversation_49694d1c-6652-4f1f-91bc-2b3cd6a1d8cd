#!/bin/sh
# get the list of modified files
files=$(git diff --cached --name-only)

# check if api or web directory is modified

api_modified=false
web_modified=false

for file in $files
do
    # Use POSIX compliant pattern matching
    case "$file" in
        api/*.py)
            # set api_modified flag to true
            api_modified=true
            ;;
        web/*)
            # set web_modified flag to true
            web_modified=true
            ;;
    esac
done

# run linters based on the modified modules

if $api_modified; then
    echo "Running Ruff linter on api module"

    # run Ruff linter auto-fixing
    uv run --project api --dev ruff check --fix ./api

    # run Ruff linter checks
    uv run --project api --dev ruff check  ./api || status=$?

    status=${status:-0}


    if [ $status -ne 0 ]; then
      echo "Ruff linter on api module error, exit code: $status"
      echo "Please run 'dev/reformat' to fix the fixable linting errors."
      exit 1
    fi
fi

if $web_modified; then
    echo "Running ESLint on web module"
    cd ./web || exit 1
    lint-staged

    echo "Running unit tests check"
    modified_files=$(git diff --cached --name-only -- utils | grep -v '\.spec\.ts$' || true)

    if [ -n "$modified_files" ]; then
        for file in $modified_files; do
            test_file="${file%.*}.spec.ts"
            echo "Checking for test file: $test_file"

            # check if the test file exists
            if [ -f "../$test_file" ]; then
                echo "Detected changes in $file, running corresponding unit tests..."
                pnpm run test "../$test_file"

                if [ $? -ne 0 ]; then
                    echo "Unit tests failed. Please fix the errors before committing."
                    exit 1
                fi
                echo "Unit tests for $file passed."
            else
                echo "Warning: $file does not have a corresponding test file."
            fi

        done
        echo "All unit tests for modified web/utils files have passed."
    fi

    cd ../
fi
