const translation = {
  category: {
    extensions: 'Estensioni',
    tools: 'Utensileria',
    agents: 'Strategie degli agenti',
    bundles: '<PERSON><PERSON><PERSON>',
    models: '<PERSON><PERSON>',
    all: '<PERSON><PERSON>',
  },
  categorySingle: {
    bundle: '<PERSON><PERSON><PERSON>',
    model: '<PERSON><PERSON>',
    agent: 'Strategia dell\'agente',
    extension: 'Estensione',
    tool: 'Strumento',
  },
  list: {
    source: {
      local: 'Installa dal file del pacchetto locale',
      github: 'Installa da GitHub',
      marketplace: 'Installa da Marketplace',
    },
    noInstalled: 'Nessun plug-in installato',
    notFound: 'Nessun plugin trovato',
  },
  source: {
    github: 'GitHub',
    local: 'File del pacchetto locale',
    marketplace: 'Mercato',
  },
  detailPanel: {
    categoryTip: {
      github: 'Installato da Github',
      marketplace: 'Installato da Marketplace',
      local: 'Plugin locale',
      debugging: 'Plugin di debug',
    },
    operation: {
      detail: 'Dettag<PERSON>',
      remove: 'Togliere',
      update: 'Aggiornare',
      install: 'Installare',
      viewDetail: 'vedi dettagli',
      checkUpdate: '<PERSON>la l\'aggiornamento',
      info: 'Informazioni sul plugin',
    },
    toolSelector: {
      paramsTip1: 'Controlla i parametri di inferenza LLM.',
      descriptionPlaceholder: 'Breve descrizione dello scopo dell\'utensile, ad es. ottenere la temperatura per una posizione specifica.',
      unsupportedTitle: 'Azione non supportata',
      uninstalledTitle: 'Strumento non installato',
      params: 'CONFIGURAZIONE DEL RAGIONAMENTO',
      uninstalledContent: 'Questo plugin viene installato dal repository locale/GitHub. Si prega di utilizzare dopo l\'installazione.',
      empty: 'Fare clic sul pulsante \'+\' per aggiungere strumenti. È possibile aggiungere più strumenti.',
      toolLabel: 'Strumento',
      unsupportedContent2: 'Fare clic per cambiare versione.',
      title: 'Aggiungi strumento',
      settings: 'IMPOSTAZIONI UTENTE',
      uninstalledLink: 'Gestisci nei plugin',
      placeholder: 'Seleziona uno strumento...',
      unsupportedContent: 'La versione del plug-in installata non fornisce questa azione.',
      descriptionLabel: 'Descrizione dell\'utensile',
      auto: 'Automatico',
      paramsTip2: 'Quando \'Automatico\' è disattivato, viene utilizzato il valore predefinito.',
      toolSetting: 'Impostazioni degli strumenti',
    },
    modelNum: '{{num}} MODELLI INCLUSI',
    endpointModalTitle: 'Endpoint di configurazione',
    endpointsDocLink: 'Visualizza il documento',
    endpointDisableTip: 'Disabilita endpoint',
    switchVersion: 'Versione switch',
    configureTool: 'Strumento di configurazione',
    serviceOk: 'Servizio OK',
    disabled: 'Disabile',
    configureModel: 'Configura modello',
    endpointModalDesc: 'Una volta configurate, è possibile utilizzare le funzionalità fornite dal plug-in tramite endpoint API.',
    endpointDeleteContent: 'Vuoi rimuovere {{name}}?',
    strategyNum: '{{num}} {{strategia}} INCLUSO',
    endpoints: 'Endpoint',
    configureApp: 'Configura l\'app',
    endpointsTip: 'Questo plug-in fornisce funzionalità specifiche tramite endpoint ed è possibile configurare più set di endpoint per l\'area di lavoro corrente.',
    endpointDisableContent: 'Vorresti disabilitare {{name}}?',
    endpointDeleteTip: 'Rimuovi punto finale',
    endpointsEmpty: 'Fare clic sul pulsante \'+\' per aggiungere un punto finale',
    actionNum: '{{num}} {{azione}} INCLUSO',
  },
  debugInfo: {
    title: 'Debug',
    viewDocs: 'Visualizza documenti',
  },
  privilege: {
    whoCanDebug: 'Chi può eseguire il debug dei plugin?',
    admins: 'Amministratori',
    title: 'Preferenze del plugin',
    noone: 'Nessuno',
    everyone: 'Ciascuno',
    whoCanInstall: 'Chi può installare e gestire i plugin?',
  },
  pluginInfoModal: {
    packageName: 'Pacco',
    release: 'Rilascio',
    repository: 'Deposito',
    title: 'Informazioni sul plugin',
  },
  action: {
    usedInApps: 'Questo plugin viene utilizzato nelle app {{num}}.',
    delete: 'Rimuovi plugin',
    pluginInfo: 'Informazioni sul plugin',
    checkForUpdates: 'Controlla gli aggiornamenti',
    deleteContentRight: 'plugin?',
    deleteContentLeft: 'Vorresti rimuovere',
  },
  installModal: {
    labels: {
      version: 'Versione',
      repository: 'Deposito',
      package: 'Pacco',
    },
    next: 'Prossimo',
    pluginLoadErrorDesc: 'Questo plugin non verrà installato',
    installComplete: 'Installazione completata',
    dropPluginToInstall: 'Rilascia qui il pacchetto del plug-in per installarlo',
    installedSuccessfully: 'Installazione riuscita',
    installedSuccessfullyDesc: 'Il plug-in è stato installato correttamente.',
    installPlugin: 'Installa il plugin',
    fromTrustSource: 'Assicurati di installare i plug-in solo da una <trustSource>fonte attendibile</trustSource>.',
    uploadFailed: 'Caricamento non riuscito',
    uploadingPackage: 'Caricamento di {{packageName}}...',
    pluginLoadError: 'Errore di caricamento del plugin',
    cancel: 'Annulla',
    readyToInstallPackage: 'Sto per installare il seguente plugin',
    installFailed: 'Installazione non riuscita',
    back: 'Indietro',
    close: 'Chiudere',
    installFailedDesc: 'Il plug-in è stato installato non riuscito.',
    readyToInstall: 'Sto per installare il seguente plugin',
    installing: 'Installazione...',
    install: 'Installare',
    readyToInstallPackages: 'Sto per installare i seguenti plugin {{num}}',
    installWarning: 'Questo plugin non è consentito essere installato.',
  },
  installFromGitHub: {
    installedSuccessfully: 'Installazione riuscita',
    selectPackagePlaceholder: 'Seleziona un pacchetto',
    installNote: 'Assicurati di installare i plug-in solo da una fonte attendibile.',
    updatePlugin: 'Aggiorna il plugin da GitHub',
    uploadFailed: 'Caricamento non riuscito',
    gitHubRepo: 'Repository GitHub',
    installPlugin: 'Installa il plugin da GitHub',
    installFailed: 'Installazione non riuscita',
    selectVersionPlaceholder: 'Seleziona una versione',
    selectPackage: 'Seleziona il pacchetto',
    selectVersion: 'Seleziona la versione',
  },
  upgrade: {
    upgrade: 'Installare',
    usedInApps: 'Utilizzato nelle app {{num}}',
    title: 'Installa il plugin',
    description: 'Sto per installare il seguente plugin',
    upgrading: 'Installazione...',
    successfulTitle: 'Installazione riuscita',
    close: 'Chiudere',
  },
  error: {
    fetchReleasesError: 'Impossibile recuperare le release. Riprova più tardi.',
    noReleasesFound: 'Nessuna pubblicazione trovata. Controlla il repository GitHub o l\'URL di input.',
    inValidGitHubUrl: 'URL GitHub non valido. Inserisci un URL valido nel formato: https://github.com/owner/repo',
  },
  marketplace: {
    sortOption: {
      recentlyUpdated: 'Aggiornato di recente',
      firstReleased: 'Prima pubblicazione',
      newlyReleased: 'Appena uscito',
      mostPopular: 'I più popolari',
    },
    moreFrom: 'Altro da Marketplace',
    difyMarketplace: 'Mercato Dify',
    discover: 'Scoprire',
    pluginsResult: '{{num}} risultati',
    noPluginFound: 'Nessun plug-in trovato',
    empower: 'Potenzia lo sviluppo dell\'intelligenza artificiale',
    sortBy: 'Ordina per',
    and: 'e',
    viewMore: 'Vedi di più',
    verifiedTip: 'Verificato da Dify',
    partnerTip: 'Verificato da un partner Dify',
  },
  task: {
    clearAll: 'Cancella tutto',
    installError: 'Impossibile installare i plugin {{errorLength}}, clicca per visualizzare',
    installing: 'Installazione dei plugin {{installingLength}}, 0 fatto.',
    installedError: 'Impossibile installare i plugin di {{errorLength}}',
    installingWithError: 'Installazione dei plugin {{installingLength}}, {{successLength}} successo, {{errorLength}} fallito',
    installingWithSuccess: 'Installazione dei plugin {{installingLength}}, {{successLength}} successo.',
  },
  searchInMarketplace: 'Cerca nel Marketplace',
  endpointsEnabled: '{{num}} set di endpoint abilitati',
  from: 'Da',
  installAction: 'Installare',
  allCategories: 'Tutte le categorie',
  fromMarketplace: 'Dal Marketplace',
  searchTools: 'Strumenti di ricerca...',
  searchCategories: 'Cerca Categorie',
  install: '{{num}} installazioni',
  findMoreInMarketplace: 'Scopri di più su Marketplace',
  installPlugin: 'Installa il plugin',
  searchPlugins: 'Plugin di ricerca',
  search: 'Ricerca',
  installFrom: 'INSTALLA DA',
  metadata: {
    title: 'Plugin',
  },
  difyVersionNotCompatible: 'L\'attuale versione di Dify non è compatibile con questo plugin, si prega di aggiornare alla versione minima richiesta: {{minimalDifyVersion}}',
  requestAPlugin: 'Richiedi un plugin',
  publishPlugins: 'Pubblicare plugin',
}

export default translation
