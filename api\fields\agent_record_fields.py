from flask_restful import fields
from libs.helper import TimestampField

# 创建信息字段
creation_info_fields = {
    "created_at": TimestampField,
    "created_by": fields.String,
    "creator_name": fields.String,
    "initial_config": fields.Raw,
}

# 发布信息字段
publish_info_fields = {
    "last_published_at": TimestampField,
    "published_by": fields.String,
    "publisher_name": fields.String,
    "version": fields.String,
    "workflow_id": fields.String,
}

# 运行信息字段
runtime_info_fields = {
    "total_runs": fields.Integer,
    "successful_runs": fields.Integer,
    "failed_runs": fields.Integer,
    "average_response_time": fields.Float,
    "total_tokens_used": fields.Integer,
    "total_cost": fields.Float,
    "currency": fields.String,
}

# 统计信息字段
statistics_fields = {
    "total_conversations": fields.Integer,
    "total_messages": fields.Integer,
    "user_satisfaction_rate": fields.Float,
    "daily_usage": fields.List(fields.Raw),
    "monthly_usage": fields.List(fields.Raw),
}

# 调用记录字段
call_record_fields = {
    "id": fields.String,
    "timestamp": TimestampField,
    "user_id": fields.String,
    "user_name": fields.String,
    "input": fields.String,
    "output": fields.String,
    "status": fields.String,
    "response_time": fields.Float,
    "tokens_used": fields.Integer,
    "conversation_id": fields.String,
    "message_id": fields.String,
}

# 智能体记录主字段
agent_record_fields = {
    "creation_info": fields.Nested(creation_info_fields),
    "publish_info": fields.Nested(publish_info_fields),
    "runtime_info": fields.Nested(runtime_info_fields),
    "statistics": fields.Nested(statistics_fields),
    "call_records": fields.List(fields.Nested(call_record_fields)),
} 