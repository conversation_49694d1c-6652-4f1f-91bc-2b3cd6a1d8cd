import base64
import hashlib
import hmac
import json
from datetime import datetime, timezone
from typing import Generator, Mapping, Optional, Union
import httpx
from dify_plugin.entities.model import (
    AIModelEntity,
    I18nObject,
    ModelFeature,
    ModelType,
    ParameterRule,
    ParameterType,
)
from dify_plugin.entities.model.llm import LLMResult, LLMResultChunk, LLMResultChunkDelta, LLMUsage
from dify_plugin.entities.model.message import (
    PromptMessage,
    PromptMessageContentType,
    PromptMessageTool,
    AssistantPromptMessage,
)
from dify_plugin.interfaces.model.large_language_model import LargeLanguageModel
from dify_plugin.errors.model import (
    CredentialsValidateFailedError,
    InvokeError, InvokeConnectionError, InvokeServerUnavailableError, InvokeRateLimitError, InvokeAuthorizationError,
    InvokeBadRequestError,
)

class AiGatewayPlatformLargeLanguageModel(LargeLanguageModel):
    """
    AI Gateway Platform Large Language Model with HMAC-SHA256 authentication
    """
    def get_customizable_model_schema(self, model: str, credentials: Mapping) -> AIModelEntity:
        """
        Get customizable model schema
        """
        entity = AIModelEntity(
            model=model,
            label=I18nObject(en_US=model, zh_Hans=model),
            model_type=ModelType.LLM,
            features=[
                ModelFeature.MULTI_TOOL_CALL,
                ModelFeature.TOOL_CALL,
                ModelFeature.STREAM_TOOL_CALL,
                ModelFeature.VISION,
            ],
            fetch_from=AIModelEntity.FetchFrom.CUSTOMIZABLE_MODEL,
            model_properties={
                "context_size": int(credentials.get("context_size", 4096)),
                "max_chunks": 1,
            },
            parameter_rules=[
                ParameterRule(
                    name="display_name",
                    label=I18nObject(en_US="Model display name", zh_Hans="模型显示名称"),
                    type=ParameterType.STRING,
                    help=I18nObject(
                        en_US="The display name of the model in the interface.",
                        zh_Hans="模型在界面的显示名称。"
                    ),
                    required=False,
                ),
                ParameterRule(
                    name="api_endpoint",
                    label=I18nObject(en_US="API Endpoint", zh_Hans="API端点"),
                    type=ParameterType.STRING,
                    help=I18nObject(
                        en_US="Enter your API endpoint URL (e.g., https://your-platform.com/ai-gateway/predict)",
                        zh_Hans="输入您的API端点URL（例如：https://your-platform.com/ai-gateway/predict）"
                    ),
                    required=True,
                ),
                ParameterRule(
                    name="customer_code",
                    label=I18nObject(en_US="Customer Code", zh_Hans="客户编码"),
                    type=ParameterType.STRING,
                    help=I18nObject(
                        en_US="Enter your customer system code",
                        zh_Hans="输入您的客户系统编码"
                    ),
                    required=True,
                ),
                ParameterRule(
                    name="secret_key",
                    label=I18nObject(en_US="Secret Key", zh_Hans="密钥"),
                    type=ParameterType.STRING,
                    help=I18nObject(
                        en_US="Enter your authentication secret key",
                        zh_Hans="输入您的认证密钥"
                    ),
                    required=True,
                ),
                ParameterRule(
                    name="context_size",
                    label=I18nObject(en_US="Context Size", zh_Hans="上下文大小"),
                    type=ParameterType.INT,
                    help=I18nObject(
                        en_US="Maximum context window size.",
                        zh_Hans="最大上下文窗口大小。"
                    ),
                    required=False,
                    default=4096,
                    min=1,
                    max=32768,
                ),
                ParameterRule(
                    name="temperature",
                    label=I18nObject(en_US="Temperature", zh_Hans="温度"),
                    type=ParameterType.FLOAT,
                    help=I18nObject(
                        en_US="Controls randomness in the response generation.",
                        zh_Hans="控制响应生成的随机性。"
                    ),
                    required=False,
                    default=1.0,
                    min=0.0,
                    max=2.0,
                ),
                ParameterRule(
                    name="top_p",
                    label=I18nObject(en_US="Top P", zh_Hans="Top P"),
                    type=ParameterType.FLOAT,
                    help=I18nObject(
                        en_US="Controls diversity via nucleus sampling.",
                        zh_Hans="通过核采样控制多样性。"
                    ),
                    required=False,
                    default=1.0,
                    min=0.0,
                    max=1.0,
                ),
                ParameterRule(
                    name="top_k",
                    label=I18nObject(en_US="Top K", zh_Hans="Top K"),
                    type=ParameterType.INT,
                    help=I18nObject(
                        en_US="Controls diversity by limiting the number of tokens considered.",
                        zh_Hans="通过限制考虑的token数量来控制多样性。"
                    ),
                    required=False,
                    min=-1,
                    max=2147483647,
                ),
                ParameterRule(
                    name="max_tokens",
                    label=I18nObject(en_US="Max Tokens", zh_Hans="最大 Token 数"),
                    type=ParameterType.INT,
                    help=I18nObject(
                        en_US="Maximum number of tokens in the response.",
                        zh_Hans="响应中的最大 Token 数。"
                    ),
                    required=False,
                    default=4096,
                    min=1,
                    max=32768,
                ),
                ParameterRule(
                    name="presence_penalty",
                    label=I18nObject(en_US="Presence Penalty", zh_Hans="存在惩罚"),
                    type=ParameterType.FLOAT,
                    help=I18nObject(
                        en_US="Penalizes tokens based on their presence in the text.",
                        zh_Hans="基于token在文本中的存在进行惩罚。"
                    ),
                    required=False,
                    default=0.0,
                    min=-2.0,
                    max=2.0,
                ),
                ParameterRule(
                    name="frequency_penalty",
                    label=I18nObject(en_US="Frequency Penalty", zh_Hans="频率惩罚"),
                    type=ParameterType.FLOAT,
                    help=I18nObject(
                        en_US="Penalizes tokens based on their frequency in the text.",
                        zh_Hans="基于token在文本中的频率进行惩罚。"
                    ),
                    required=False,
                    default=0.0,
                    min=-2.0,
                    max=2.0,
                ),
                ParameterRule(
                    name="repetition_penalty",
                    label=I18nObject(en_US="Repetition Penalty", zh_Hans="重复惩罚"),
                    type=ParameterType.FLOAT,
                    help=I18nObject(
                        en_US="Penalizes repetition in generated text.",
                        zh_Hans="惩罚生成文本中的重复。"
                    ),
                    required=False,
                    default=1.0,
                    min=0.0,
                    max=2.0,
                ),
                ParameterRule(
                    name="seed",
                    label=I18nObject(en_US="Random Seed", zh_Hans="随机种子"),
                    type=ParameterType.INT,
                    help=I18nObject(
                        en_US="Random seed for reproducible results.",
                        zh_Hans="用于可重现结果的随机种子。"
                    ),
                    required=False,
                    min=0,
                    max=18446744073709551615,
                ),
                ParameterRule(
                    name="stream",
                    label=I18nObject(en_US="Stream", zh_Hans="流式输出"),
                    type=ParameterType.BOOLEAN,
                    help=I18nObject(
                        en_US="Whether to stream the response.",
                        zh_Hans="是否启用流式响应。"
                    ),
                    required=False,
                    default=True,
                ),
                ParameterRule(
                    name="vision_support",
                    label=I18nObject(en_US="Vision Support", zh_Hans="Vision 支持"),
                    type=ParameterType.STRING,
                    help=I18nObject(
                        en_US="Whether to support vision input.",
                        zh_Hans="是否支持 Vision 输入。"
                    ),
                    required=False,
                    default="no_support",
                ),
                ParameterRule(
                    name="skip_special_tokens",
                    label=I18nObject(en_US="Skip Special Tokens", zh_Hans="跳过特殊Token"),
                    type=ParameterType.BOOLEAN,
                    help=I18nObject(
                        en_US="Whether to skip special tokens in output.",
                        zh_Hans="是否在输出中跳过特殊token。"
                    ),
                    required=False,
                    default=True,
                ),
                ParameterRule(
                    name="ignore_eos",
                    label=I18nObject(en_US="Ignore EOS Token", zh_Hans="忽略结束符"),
                    type=ParameterType.BOOLEAN,
                    help=I18nObject(
                        en_US="Whether to ignore end-of-sequence token.",
                        zh_Hans="是否忽略序列结束符。"
                    ),
                    required=False,
                    default=False,
                ),
            ],
        )
        return entity

    def _generate_hmac_auth(self, customer_code: str, secret_key: str) -> dict:
        """
        Generate HMAC-SHA256 authentication headers
        """
        gmt_format = "%a, %d %b %Y %H:%M:%S GMT"
        x_date = datetime.now(timezone.utc).strftime(gmt_format)
        str_to_sign = f"x-date: {x_date}"

        signature = base64.b64encode(
            hmac.new(secret_key.encode(), str_to_sign.encode(), hashlib.sha256).digest()
        ).decode()

        auth_header = f'hmac username="{customer_code}", algorithm="hmac-sha256", headers="x-date", signature="{signature}"'

        return {
            "x-date": x_date,
            "Authorization": auth_header,
            "Content-Type": "application/json"
        }

    def _convert_messages_to_platform_format(self, messages: list[PromptMessage]) -> list[dict]:
        """
        Convert dify PromptMessage to platform format, including vision/image_url support
        """
        converted_messages = []

        for message in messages:
            msg_dict = {
                "role": message.role.value,
                "content": None
            }

            # Handle different content types
            if isinstance(message.content, str):
                msg_dict["content"] = message.content
            elif isinstance(message.content, list):
                # 支持 vision/image_url + text 混合
                content_parts = []
                for content_item in message.content:
                    if hasattr(content_item, "type"):
                        if content_item.type == PromptMessageContentType.TEXT:
                            content_parts.append({"type": "text", "text": content_item.data})
                        elif content_item.type == PromptMessageContentType.IMAGE_URL:
                            content_parts.append({"type": "image_url", "image_url": content_item.data})
                msg_dict["content"] = content_parts

            converted_messages.append(msg_dict)

        return converted_messages

    def _convert_tools_to_platform_format(self, tools: Optional[list[PromptMessageTool]]) -> Optional[list[dict]]:
        """
        Convert Dify tools to platform format
        """
        if not tools:
            return None

        platform_tools = []
        for tool in tools:
            platform_tool = {
                "type": "function",
                "function": {
                    "name": tool.name,
                    "description": tool.description or "",
                    "parameters": tool.parameters or {}
                }
            }
            platform_tools.append(platform_tool)

        return platform_tools

    def _invoke(
        self,
        model: str,
        credentials: Mapping,
        prompt_messages: list[PromptMessage],
        model_parameters: dict,
        tools: Optional[list[PromptMessageTool]] = None,
        stop: Optional[list[str]] = None,
        stream: bool = True,
        user: Optional[str] = None,
    ) -> Union[LLMResult, Generator]:
        """
        Invoke the custom platform API
        """
        # Extract credentials
        api_endpoint = credentials.get("api_endpoint")
        customer_code = credentials.get("customer_code")
        secret_key = credentials.get("secret_key")

        if not all([api_endpoint, customer_code, secret_key]):
            raise CredentialsValidateFailedError("Missing required credentials: api_endpoint, customer_code, or secret_key")

        # Generate authentication headers
        auth_headers = self._generate_hmac_auth(customer_code, secret_key)

        # Convert messages to platform format
        platform_messages = self._convert_messages_to_platform_format(prompt_messages)

        # Convert tools to platform format
        platform_tools = self._convert_tools_to_platform_format(tools)

        # Construct request body for platform API
        request_body = {
            "componentCode": model,  # Use model name as componentCode
            "model": model,  # Add model field for compatibility
            "messages": platform_messages,
            "stream": stream
        }

        # Add model parameters if provided
        if model_parameters.get("temperature") is not None:
            request_body["temperature"] = model_parameters["temperature"]
        if model_parameters.get("max_tokens") is not None:
            request_body["max_tokens"] = model_parameters["max_tokens"]
        if model_parameters.get("top_p") is not None:
            request_body["top_p"] = model_parameters["top_p"]
        if model_parameters.get("top_k") is not None:
            request_body["top_k"] = model_parameters["top_k"]
        if model_parameters.get("presence_penalty") is not None:
            request_body["presence_penalty"] = model_parameters["presence_penalty"]
        if model_parameters.get("frequency_penalty") is not None:
            request_body["frequency_penalty"] = model_parameters["frequency_penalty"]
        if model_parameters.get("repetition_penalty") is not None:
            request_body["repetition_penalty"] = model_parameters["repetition_penalty"]
        if model_parameters.get("seed") is not None:
            request_body["seed"] = model_parameters["seed"]
        if model_parameters.get("stream") is not None:
            request_body["stream"] = model_parameters["stream"]
        if model_parameters.get("vision_support") is not None:
            request_body["vision_support"] = model_parameters["vision_support"]
        if model_parameters.get("skip_special_tokens") is not None:
            request_body["skip_special_tokens"] = model_parameters["skip_special_tokens"]
        if model_parameters.get("ignore_eos") is not None:
            request_body["ignore_EOS"] = model_parameters["ignore_eos"]
        if stop:
            request_body["stop"] = stop
        if platform_tools:
            request_body["tools"] = platform_tools
        if model_parameters.get("tool_choice") is not None:
            request_body["tool_choice"] = model_parameters["tool_choice"]

        try:
            with httpx.Client(timeout=60.0, verify=False) as client:
                if stream:
                    response = client.stream(
                        "POST",
                        api_endpoint,
                        headers=auth_headers,
                        json=request_body,
                    )
                    return self._handle_stream_response(response, prompt_messages)
                else:
                    response = client.post(
                        api_endpoint,
                        headers=auth_headers,
                        json=request_body,
                    )
                    response.raise_for_status()
                    return self._handle_non_stream_response(response, prompt_messages)

        except httpx.HTTPStatusError as e:
            raise InvokeError(f"API request failed: {e.response.status_code} - {e.response.text}")
        except Exception as e:
            raise InvokeError(f"Request error: {str(e)}")

    def _handle_stream_response(self, response: httpx.Response, prompt_messages: list[PromptMessage]) -> Generator:
        """
        Handle streaming response from the platform API
        """
        buffer = ""

        with response as stream_response:
            for chunk in stream_response.iter_text():
                buffer += chunk

                # Process complete lines
                while "\n" in buffer:
                    line, buffer = buffer.split("\n", 1)
                    line = line.strip()

                    if not line:
                        continue

                    # Handle SSE format
                    if line.startswith("data: "):
                        data = line[6:]  # Remove "data: " prefix

                        if data == "[DONE]":
                            break

                        try:
                            chunk_data = json.loads(data)

                            # Extract content from platform response
                            if "choices" in chunk_data and len(chunk_data["choices"]) > 0:
                                choice = chunk_data["choices"][0]

                                # Handle delta content
                                delta_content = ""
                                tool_calls = []

                                if "delta" in choice:
                                    delta = choice["delta"]
                                    if "content" in delta and delta["content"]:
                                        delta_content = delta["content"]

                                    # Handle tool calls
                                    if "tool_calls" in delta and delta["tool_calls"]:
                                        for tool_call in delta["tool_calls"]:
                                            tool_calls.append(AssistantPromptMessage.ToolCall(
                                                id=tool_call.get("id", ""),
                                                type=tool_call.get("type", "function"),
                                                function=AssistantPromptMessage.ToolCall.ToolCallFunction(
                                                    name=tool_call.get("function", {}).get("name", ""),
                                                    arguments=tool_call.get("function", {}).get("arguments", "")
                                                )
                                            ))

                                # Create chunk
                                chunk = LLMResultChunk(
                                    model=chunk_data.get("model", ""),
                                    prompt_messages=prompt_messages,
                                    delta=LLMResultChunkDelta(
                                        message=AssistantPromptMessage(
                                            content=delta_content,
                                            tool_calls=tool_calls
                                        ),
                                        usage=LLMUsage(
                                            prompt_tokens=chunk_data.get("usage", {}).get("prompt_tokens", 0),
                                            completion_tokens=chunk_data.get("usage", {}).get("completion_tokens", 0),
                                            total_tokens=chunk_data.get("usage", {}).get("total_tokens", 0)
                                        ) if "usage" in chunk_data else None,
                                        system_fingerprint=chunk_data.get("system_fingerprint")
                                    )
                                )

                                yield chunk

                        except json.JSONDecodeError:
                            continue

    def _handle_non_stream_response(self, response: httpx.Response, prompt_messages: list[PromptMessage]) -> LLMResult:
        """
        Handle non-streaming response from the platform API
        """
        response_data = response.json()

        if "choices" not in response_data or len(response_data["choices"]) == 0:
            raise InvokeError("Invalid response format: no choices found")

        choice = response_data["choices"][0]
        message = choice.get("message", {})

        # Extract content and tool calls
        content = message.get("content", "")
        tool_calls = []

        if "tool_calls" in message and message["tool_calls"]:
            for tool_call in message["tool_calls"]:
                tool_calls.append(AssistantPromptMessage.ToolCall(
                    id=tool_call.get("id", ""),
                    type=tool_call.get("type", "function"),
                    function=AssistantPromptMessage.ToolCall.ToolCallFunction(
                        name=tool_call.get("function", {}).get("name", ""),
                        arguments=tool_call.get("function", {}).get("arguments", "")
                    )
                ))

        # Extract usage information
        usage_data = response_data.get("usage", {})
        usage = LLMUsage(
            prompt_tokens=usage_data.get("prompt_tokens", 0),
            completion_tokens=usage_data.get("completion_tokens", 0),
            total_tokens=usage_data.get("total_tokens", 0)
        )

        return LLMResult(
            model=response_data.get("model", ""),
            prompt_messages=prompt_messages,
            message=AssistantPromptMessage(
                content=content,
                tool_calls=tool_calls
            ),
            usage=usage,
            system_fingerprint=response_data.get("system_fingerprint")
        )

    def validate_credentials(self, model: str, credentials: Mapping) -> None:
        """
        Validate the credentials by making a test request
        """
        # Extract credentials
        api_endpoint = credentials.get("api_endpoint")
        customer_code = credentials.get("customer_code")
        secret_key = credentials.get("secret_key")

        if not all([api_endpoint, customer_code, secret_key]):
            raise CredentialsValidateFailedError("Missing required credentials: api_endpoint, customer_code, or secret_key")

        # Generate test authentication headers
        try:
            auth_headers = self._generate_hmac_auth(customer_code, secret_key)
        except Exception as e:
            raise CredentialsValidateFailedError(f"Failed to generate authentication: {str(e)}")

        # Make a simple test request
        test_request = {
            "componentCode": model,
            "model": model,
            "messages": [{"role": "user", "content": "Hello"}],
            "stream": False,
            "max_tokens": 10
        }

        try:
            with httpx.Client(timeout=30.0, verify=False) as client:
                response = client.post(
                    api_endpoint,
                    headers=auth_headers,
                    json=test_request,
                )
                response.raise_for_status()

                # Validate response format
                response_data = response.json()
                if "choices" not in response_data:
                    raise CredentialsValidateFailedError("Invalid response format: no choices found")

        except httpx.HTTPStatusError as e:
            if e.response.status_code == 401:
                raise CredentialsValidateFailedError("Authentication failed: invalid credentials")
            elif e.response.status_code == 403:
                raise CredentialsValidateFailedError("Access denied: insufficient permissions")
            else:
                raise CredentialsValidateFailedError(f"API request failed: {e.response.status_code}")
        except Exception as e:
            raise CredentialsValidateFailedError(f"Validation error: {str(e)}")

    def get_num_tokens(self, model: str, credentials: Mapping, prompt_messages: list[PromptMessage], tools: Optional[list[PromptMessageTool]] = None) -> int:
        """
        Get number of tokens for given prompt messages
        For now, return 0 as the platform doesn't provide token counting
        """
        return 0

    @property
    def _invoke_error_mapping(self) -> dict[type[InvokeError], list[type[Exception]]]:
        """
        Map model invoke error to unified error
        """
        return {
            InvokeConnectionError: [httpx.ConnectError, httpx.TimeoutException],
            InvokeServerUnavailableError: [httpx.HTTPStatusError],  # 5xx errors
            InvokeRateLimitError: [httpx.HTTPStatusError],  # 429 errors
            InvokeAuthorizationError: [httpx.HTTPStatusError],  # 401, 403 errors
            InvokeBadRequestError: [httpx.HTTPStatusError],  # 4xx errors (except 401, 403, 429)
        }
