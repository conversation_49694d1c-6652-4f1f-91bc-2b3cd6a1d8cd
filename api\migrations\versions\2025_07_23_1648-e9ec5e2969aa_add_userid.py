"""add userid

Revision ID: e9ec5e2969aa
Revises: fb9f0d3e4818
Create Date: 2025-07-23 16:48:36.634919

"""
from alembic import op
import models as models
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e9ec5e2969aa'
down_revision = 'fb9f0d3e4818'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('accounts', schema=None) as batch_op:
        batch_op.alter_column('user_id',
               existing_type=sa.VARCHAR(length=64),
               type_=sa.String(length=36),
               existing_comment='客户端自定义用户ID',
               existing_nullable=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('accounts', schema=None) as batch_op:
        batch_op.alter_column('user_id',
               existing_type=sa.String(length=36),
               type_=sa.VARCHAR(length=64),
               existing_comment='客户端自定义用户ID',
               existing_nullable=True)

    # ### end Alembic commands ###
