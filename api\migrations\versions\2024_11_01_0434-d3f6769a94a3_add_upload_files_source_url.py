"""Add upload_files.source_url

Revision ID: d3f6769a94a3
Revises: 43fa78bc3b7d
Create Date: 2024-11-01 04:34:23.816198

"""
from alembic import op
import models as models
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'd3f6769a94a3'
down_revision = '43fa78bc3b7d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('upload_files', schema=None) as batch_op:
        batch_op.add_column(sa.Column('source_url', sa.String(length=255), server_default='', nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('upload_files', schema=None) as batch_op:
        batch_op.drop_column('source_url')
    # ### end Alembic commands ###
