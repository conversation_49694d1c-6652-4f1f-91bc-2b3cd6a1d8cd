import urllib.parse
from dataclasses import dataclass
from typing import Optional

import requests


@dataclass
class OAuthUserInfo:
    id: str
    name: str
    email: str


class OAuth:
    def __init__(self, client_id: str, client_secret: str, redirect_uri: str):
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri

    def get_authorization_url(self):
        raise NotImplementedError()

    def get_access_token(self, code: str):
        raise NotImplementedError()

    def get_raw_user_info(self, token: str):
        raise NotImplementedError()

    def get_user_info(self, token: str) -> OAuthUserInfo:
        raw_info = self.get_raw_user_info(token)
        return self._transform_user_info(raw_info)

    def _transform_user_info(self, raw_info: dict) -> OAuthUserInfo:
        raise NotImplementedError()


class GitHubOAuth(OAuth):
    _AUTH_URL = "https://github.com/login/oauth/authorize"
    _TOKEN_URL = "https://github.com/login/oauth/access_token"
    _USER_INFO_URL = "https://api.github.com/user"
    _EMAIL_INFO_URL = "https://api.github.com/user/emails"

    def get_authorization_url(self, invite_token: Optional[str] = None):
        params = {
            "client_id": self.client_id,
            "redirect_uri": self.redirect_uri,
            "scope": "user:email",  # Request only basic user information
        }
        if invite_token:
            params["state"] = invite_token
        return f"{self._AUTH_URL}?{urllib.parse.urlencode(params)}"

    def get_access_token(self, code: str):
        data = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": code,
            "redirect_uri": self.redirect_uri,
        }
        headers = {"Accept": "application/json"}
        response = requests.post(self._TOKEN_URL, data=data, headers=headers)

        response_json = response.json()
        access_token = response_json.get("access_token")

        if not access_token:
            raise ValueError(f"Error in GitHub OAuth: {response_json}")

        return access_token

    def get_raw_user_info(self, token: str):
        headers = {"Authorization": f"token {token}"}
        response = requests.get(self._USER_INFO_URL, headers=headers)
        response.raise_for_status()
        user_info = response.json()

        email_response = requests.get(self._EMAIL_INFO_URL, headers=headers)
        email_info = email_response.json()
        primary_email: dict = next((email for email in email_info if email["primary"] == True), {})

        return {**user_info, "email": primary_email.get("email", "")}

    def _transform_user_info(self, raw_info: dict) -> OAuthUserInfo:
        email = raw_info.get("email")
        if not email:
            email = f"{raw_info['id']}+{raw_info['login']}@users.noreply.github.com"
        return OAuthUserInfo(id=str(raw_info["id"]), name=raw_info["name"], email=email)


class GoogleOAuth(OAuth):
    _AUTH_URL = "https://accounts.google.com/o/oauth2/v2/auth"
    _TOKEN_URL = "https://oauth2.googleapis.com/token"
    _USER_INFO_URL = "https://www.googleapis.com/oauth2/v3/userinfo"

    def get_authorization_url(self, invite_token: Optional[str] = None):
        params = {
            "client_id": self.client_id,
            "response_type": "code",
            "redirect_uri": self.redirect_uri,
            "scope": "openid email",
        }
        if invite_token:
            params["state"] = invite_token
        return f"{self._AUTH_URL}?{urllib.parse.urlencode(params)}"

    def get_access_token(self, code: str):
        data = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": self.redirect_uri,
        }
        headers = {"Accept": "application/json"}
        response = requests.post(self._TOKEN_URL, data=data, headers=headers)

        response_json = response.json()
        access_token = response_json.get("access_token")

        if not access_token:
            raise ValueError(f"Error in Google OAuth: {response_json}")

        return access_token

    def get_raw_user_info(self, token: str):
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(self._USER_INFO_URL, headers=headers)
        response.raise_for_status()
        return response.json()

    def _transform_user_info(self, raw_info: dict) -> OAuthUserInfo:
        return OAuthUserInfo(id=str(raw_info["sub"]), name="", email=raw_info["email"])


class IAMOAuth(OAuth):
    """IAM数字身份平台OAuth实现"""

    def __init__(self, client_id: str, client_secret: str, redirect_uri: str,
                 iam_host: str, namespace: str):
        super().__init__(client_id, client_secret, redirect_uri)
        self.iam_host = iam_host
        self.namespace = namespace

        # 动态构建IAM接口URL
        self._AUTH_URL = f"https://{iam_host}/idp/authCenter/authenticate"
        self._TOKEN_URL = f"http://{iam_host}/am-gateway/{namespace}/am-protocol-service/oauth2/getToken"
        self._REFRESH_URL = f"http://{iam_host}/am-gateway/{namespace}/am-protocol-service/oauth2/refreshToken"
        self._USER_INFO_URL = f"http://{iam_host}/am-gateway/{namespace}/am-protocol-service/oauth2/getUserInfo"
        self._LOGOUT_URL = f"http://{iam_host}/idp/authCenter/GLO"

    def get_authorization_url(self, invite_token: Optional[str] = None):
        """生成IAM授权URL"""
        import json
        import base64

        # 将redirect_uri和invite_token编码到state参数中
        state_data = {
            "invite_token": invite_token,
            "oauth_config": {
                "redirect_uri": self.redirect_uri
            }
        }

        # 将state数据编码为base64字符串
        state_encoded = base64.b64encode(json.dumps(state_data).encode()).decode()

        params = {
            "client_id": self.client_id,
            "response_type": "code",
            "redirect_uri": self.redirect_uri,
            "state": state_encoded
        }
        return f"{self._AUTH_URL}?{urllib.parse.urlencode(params)}"

    def get_access_token(self, code: str):
        """获取IAM访问令牌 - 返回完整token数据而非仅access_token"""
        params = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": code,
            "grant_type": "authorization_code"
        }

        response = requests.post(self._TOKEN_URL, params=params)

        if response.status_code != 200:
            raise ValueError(f"Failed to get IAM access token: {response.text}")

        response_json = response.json()

        # 检查IAM特有的错误格式
        if "errcode" in response_json:
            raise ValueError(f"IAM error: {response_json.get('msg', 'Unknown error')}")

        # IAM返回完整的token数据，包含access_token, refresh_token, uid等
        access_token = response_json.get("access_token")
        if not access_token:
            raise ValueError(f"No access_token in IAM response: {response_json}")

        return response_json  # 返回完整数据，而不仅仅是access_token

    def get_raw_user_info(self, token_data):
        """通过调用IAM的getUserInfo接口获取用户信息"""
        # 从token_data中提取access_token
        if isinstance(token_data, str):
            # 如果传入的是access_token字符串
            access_token = token_data
        else:
            # 如果传入的是完整的token数据字典
            access_token = token_data.get("access_token")
            if not access_token:
                raise ValueError("No access_token found in token data")

        # 调用IAM的getUserInfo接口
        params = {
            "access_token": access_token,
            "client_id": self.client_id
        }

        response = requests.get(self._USER_INFO_URL, params=params)

        if response.status_code != 200:
            raise ValueError(f"Failed to get IAM user info: {response.text}")

        user_info = response.json()

        # 检查IAM特有的错误格式
        if "errcode" in user_info:
            raise ValueError(f"IAM getUserInfo error: {user_info.get('msg', 'Unknown error')}")

        # 将access_token也包含在返回的用户信息中，以便后续使用
        user_info["access_token"] = access_token

        return user_info

    def _transform_user_info(self, raw_info: dict) -> OAuthUserInfo:
        """转换IAM用户信息为标准格式"""
        # 从IAM getUserInfo接口返回的数据中提取用户信息
        # TODO 后续根据对接接口实际传入来调整数据获取内容
        user_name = raw_info.get("userName", "")
        if not user_name:
            raise ValueError("No userName found in IAM user info response")

        # 构建标准用户信息
        # 使用userName作为用户ID，因为IAM文档说明可以通过userName获取到当前登录的帐号
        return OAuthUserInfo(
            id=user_name,  # 使用userName作为用户ID
            name=user_name,  # 使用userName作为显示名称
            email=f"{user_name}@iam.local"  # 生成默认邮箱，可根据实际需求调整
        )

    def refresh_token(self, refresh_token: str) -> dict:
        """刷新IAM访问令牌"""
        data = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "refresh_token": refresh_token,
            "grant_type": "refresh_token"
        }

        response = requests.post(self._REFRESH_URL, json=data)

        if response.status_code != 200:
            raise ValueError(f"Failed to refresh IAM token: {response.text}")

        response_json = response.json()

        if "errcode" in response_json:
            raise ValueError(f"IAM refresh error: {response_json.get('msg', 'Unknown error')}")

        return response_json

    def get_logout_url(self, redirect_url: str) -> str:
        """生成IAM登出URL"""
        params = {
            "redirectToUrl": redirect_url,
            "redirectToLogin": "true",
            "entityId": self.client_id
        }
        return f"{self._LOGOUT_URL}?{urllib.parse.urlencode(params)}"
