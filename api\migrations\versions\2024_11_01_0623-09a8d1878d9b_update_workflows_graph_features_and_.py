"""update workflows graph, features and updated_at

Revision ID: 09a8d1878d9b
Revises: d07474999927
Create Date: 2024-11-01 06:23:59.579186

"""
from alembic import op
import models as models
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '09a8d1878d9b'
down_revision = 'd07474999927'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('conversations', schema=None) as batch_op:
        batch_op.alter_column('inputs',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               nullable=False)

    with op.batch_alter_table('messages', schema=None) as batch_op:
        batch_op.alter_column('inputs',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               nullable=False)

    op.execute("UPDATE workflows SET updated_at = created_at WHERE updated_at IS NULL")
    op.execute("UPDATE workflows SET graph = '' WHERE graph IS NULL")
    op.execute("UPDATE workflows SET features = '' WHERE features IS NULL")

    with op.batch_alter_table('workflows', schema=None) as batch_op:
        batch_op.alter_column('graph',
               existing_type=sa.TEXT(),
               nullable=False)
        batch_op.alter_column('features',
               existing_type=sa.TEXT(),
               nullable=False)
        batch_op.alter_column('updated_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('workflows', schema=None) as batch_op:
        batch_op.alter_column('updated_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
        batch_op.alter_column('features',
               existing_type=sa.TEXT(),
               nullable=True)
        batch_op.alter_column('graph',
               existing_type=sa.TEXT(),
               nullable=True)

    with op.batch_alter_table('messages', schema=None) as batch_op:
        batch_op.alter_column('inputs',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               nullable=True)

    with op.batch_alter_table('conversations', schema=None) as batch_op:
        batch_op.alter_column('inputs',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               nullable=True)

    # ### end Alembic commands ###
