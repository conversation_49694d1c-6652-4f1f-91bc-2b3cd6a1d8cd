"""add-embeddings-provider-name

Revision ID: a8d7385a7b66
Revises: 17b5ab037c40
Create Date: 2024-04-02 12:17:22.641525

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'a8d7385a7b66'
down_revision = '17b5ab037c40'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('embeddings', schema=None) as batch_op:
        batch_op.add_column(sa.Column('provider_name', sa.String(length=40), server_default=sa.text("''::character varying"), nullable=False))
        batch_op.drop_constraint('embedding_hash_idx', type_='unique')
        batch_op.create_unique_constraint('embedding_hash_idx', ['model_name', 'hash', 'provider_name'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('embeddings', schema=None) as batch_op:
        batch_op.drop_constraint('embedding_hash_idx', type_='unique')
        batch_op.create_unique_constraint('embedding_hash_idx', ['model_name', 'hash'])
        batch_op.drop_column('provider_name')
    # ### end Alembic commands ###
