from datetime import datetime
from typing import Optional, List, Dict, Any
from flask import request, jsonify
from flask_restful import Resource, reqparse
from sqlalchemy import and_, or_
from sqlalchemy.orm import Session

from controllers.console import api
from controllers.console.wraps import account_initialization_required, setup_required
from extensions.ext_database import db
from libs.helper import DatetimeString
from libs.login import login_required
from models.model import (
    Account, App, Conversation, Message, OperationLog, EndUser
)
from models.workflow import WorkflowAppLog, WorkflowRun
from models.model import AppMode


class AuditLogReportApi(Resource):
    """统一审计日志上报接口"""
    
    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        """
        获取审计日志数据用于上报
        支持多种类型的日志查询和上报
        """
        parser = reqparse.RequestParser()
        parser.add_argument("log_type", type=str, required=True, 
                          choices=["login", "operation", "runtime", "orchestration", "content"],
                          help="日志类型: login(登录), operation(操作), runtime(运行), orchestration(编排), content(内容)")
        parser.add_argument("start_time", type=DatetimeString("%Y-%m-%d %H:%M"), location="args")
        parser.add_argument("end_time", type=DatetimeString("%Y-%m-%d %H:%M"), location="args")
        parser.add_argument("app_ids", type=str, location="args", help="应用ID列表，逗号分隔")
        parser.add_argument("user_ids", type=str, location="args", help="用户ID列表，逗号分隔")
        parser.add_argument("page", type=int, default=1, location="args")
        parser.add_argument("limit", type=int, default=100, location="args")
        args = parser.parse_args()

        try:
            # 解析时间范围
            start_datetime = args.get("start_time")
            end_datetime = args.get("end_time")
            
            # 解析应用ID列表
            app_ids = []
            if args.get("app_ids"):
                app_ids = [app_id.strip() for app_id in args["app_ids"].split(",")]
            
            # 解析用户ID列表
            user_ids = []
            if args.get("user_ids"):
                user_ids = [user_id.strip() for user_id in args["user_ids"].split(",")]

            # 根据日志类型调用对应的查询方法
            if args["log_type"] == "login":
                result = self._get_login_logs(start_datetime, end_datetime, user_ids, args["page"], args["limit"])
            elif args["log_type"] == "operation":
                result = self._get_operation_logs(start_datetime, end_datetime, app_ids, user_ids, args["page"], args["limit"])
            elif args["log_type"] == "runtime":
                result = self._get_runtime_logs(start_datetime, end_datetime, app_ids, args["page"], args["limit"])
            elif args["log_type"] == "orchestration":
                result = self._get_orchestration_logs(start_datetime, end_datetime, app_ids, args["page"], args["limit"])
            elif args["log_type"] == "content":
                result = self._get_content_logs(start_datetime, end_datetime, app_ids, user_ids, args["page"], args["limit"])
            else:
                return {"error": "不支持的日志类型"}, 400

            return jsonify({
                "success": True,
                "data": result["data"],
                "total": result["total"],
                "page": args["page"],
                "limit": args["limit"],
                "log_type": args["log_type"]
            })

        except Exception as e:
            return {"error": f"查询失败: {str(e)}"}, 500

    def _get_login_logs(self, start_datetime: Optional[datetime], end_datetime: Optional[datetime], 
                       user_ids: List[str], page: int, limit: int) -> Dict[str, Any]:
        """获取登录信息审计日志"""
        query = db.session.query(OperationLog).filter(
            OperationLog.action.in_(["login", "logout", "switch_workspace"])
        )

        # 添加时间过滤
        if start_datetime:
            query = query.filter(OperationLog.created_at >= start_datetime)
        if end_datetime:
            query = query.filter(OperationLog.created_at <= end_datetime)

        # 添加用户过滤
        if user_ids:
            query = query.filter(OperationLog.account_id.in_(user_ids))

        # 获取总数
        total = query.count()

        # 分页查询
        logs = query.order_by(OperationLog.created_at.desc()).offset((page - 1) * limit).limit(limit).all()

        # 格式化数据
        data = []
        for log in logs:
            # 获取用户信息
            account = db.session.query(Account).filter(Account.id == log.account_id).first()
            
            data.append({
                "id": str(log.id),
                "action": log.action,
                "account_id": str(log.account_id),
                "account_name": account.name if account else "Unknown",
                "account_email": account.email if account else "Unknown",
                "tenant_id": str(log.tenant_id),
                "content": log.content,
                "created_ip": log.created_ip,
                "created_at": log.created_at.isoformat() if log.created_at else None,
                "log_type": "login_audit"
            })

        return {"data": data, "total": total}

    def _get_operation_logs(self, start_datetime: Optional[datetime], end_datetime: Optional[datetime], 
                           app_ids: List[str], user_ids: List[str], page: int, limit: int) -> Dict[str, Any]:
        """获取页面操作信息审计日志"""
        query = db.session.query(OperationLog).filter(
            OperationLog.action.notin_(["login", "logout", "switch_workspace"])
        )

        # 添加时间过滤
        if start_datetime:
            query = query.filter(OperationLog.created_at >= start_datetime)
        if end_datetime:
            query = query.filter(OperationLog.created_at <= end_datetime)

        # 添加用户过滤
        if user_ids:
            query = query.filter(OperationLog.account_id.in_(user_ids))

        # 获取总数
        total = query.count()

        # 分页查询
        logs = query.order_by(OperationLog.created_at.desc()).offset((page - 1) * limit).limit(limit).all()

        # 格式化数据
        data = []
        for log in logs:
            # 获取用户信息
            account = db.session.query(Account).filter(Account.id == log.account_id).first()
            
            data.append({
                "id": str(log.id),
                "action": log.action,
                "account_id": str(log.account_id),
                "account_name": account.name if account else "Unknown",
                "account_email": account.email if account else "Unknown",
                "tenant_id": str(log.tenant_id),
                "content": log.content,
                "created_ip": log.created_ip,
                "created_at": log.created_at.isoformat() if log.created_at else None,
                "log_type": "operation_audit"
            })

        return {"data": data, "total": total}

    def _get_runtime_logs(self, start_datetime: Optional[datetime], end_datetime: Optional[datetime], 
                         app_ids: List[str], page: int, limit: int) -> Dict[str, Any]:
        """获取智能体运行信息审计日志"""
        # 查询会话日志
        conversation_query = db.session.query(Conversation)
        message_query = db.session.query(Message)
        workflow_query = db.session.query(WorkflowAppLog)

        # 添加时间过滤
        if start_datetime:
            conversation_query = conversation_query.filter(Conversation.created_at >= start_datetime)
            message_query = message_query.filter(Message.created_at >= start_datetime)
            workflow_query = workflow_query.filter(WorkflowAppLog.created_at >= start_datetime)
        
        if end_datetime:
            conversation_query = conversation_query.filter(Conversation.created_at <= end_datetime)
            message_query = message_query.filter(Message.created_at <= end_datetime)
            workflow_query = workflow_query.filter(WorkflowAppLog.created_at <= end_datetime)

        # 添加应用过滤
        if app_ids:
            conversation_query = conversation_query.filter(Conversation.app_id.in_(app_ids))
            message_query = message_query.filter(Message.app_id.in_(app_ids))
            workflow_query = workflow_query.filter(WorkflowAppLog.app_id.in_(app_ids))

        # 获取数据
        conversations = conversation_query.order_by(Conversation.created_at.desc()).limit(limit).all()
        messages = message_query.order_by(Message.created_at.desc()).limit(limit).all()
        workflow_logs = workflow_query.order_by(WorkflowAppLog.created_at.desc()).limit(limit).all()

        # 格式化数据
        data = []
        
        # 处理会话数据
        for conv in conversations:
            app = db.session.query(App).filter(App.id == conv.app_id).first()
            data.append({
                "id": str(conv.id),
                "type": "conversation",
                "app_id": str(conv.app_id),
                "app_name": app.name if app else "Unknown",
                "app_mode": app.mode if app else "Unknown",
                "from_end_user_id": conv.from_end_user_id,
                "from_account_id": conv.from_account_id,
                "created_at": conv.created_at.isoformat() if conv.created_at else None,
                "log_type": "runtime_audit"
            })

        # 处理消息数据
        for msg in messages:
            app = db.session.query(App).filter(App.id == msg.app_id).first()
            data.append({
                "id": str(msg.id),
                "type": "message",
                "app_id": str(msg.app_id),
                "app_name": app.name if app else "Unknown",
                "app_mode": app.mode if app else "Unknown",
                "conversation_id": str(msg.conversation_id),
                "from_end_user_id": msg.from_end_user_id,
                "from_account_id": msg.from_account_id,
                "query": msg.query,
                "answer": msg.answer,
                "status": msg.status,
                "created_at": msg.created_at.isoformat() if msg.created_at else None,
                "log_type": "runtime_audit"
            })

        # 处理工作流日志
        for wf_log in workflow_logs:
            app = db.session.query(App).filter(App.id == wf_log.app_id).first()
            data.append({
                "id": str(wf_log.id),
                "type": "workflow",
                "app_id": str(wf_log.app_id),
                "app_name": app.name if app else "Unknown",
                "app_mode": app.mode if app else "Unknown",
                "workflow_run_id": str(wf_log.workflow_run_id),
                "status": wf_log.status,
                "inputs": wf_log.inputs,
                "outputs": wf_log.outputs,
                "created_at": wf_log.created_at.isoformat() if wf_log.created_at else None,
                "log_type": "runtime_audit"
            })

        # 按时间排序并分页
        data.sort(key=lambda x: x["created_at"], reverse=True)
        total = len(data)
        data = data[(page - 1) * limit:page * limit]

        return {"data": data, "total": total}

    def _get_orchestration_logs(self, start_datetime: Optional[datetime], end_datetime: Optional[datetime], 
                               app_ids: List[str], page: int, limit: int) -> Dict[str, Any]:
        """获取编排信息审计日志"""
        # 查询工作流相关的操作日志
        workflow_operation_query = db.session.query(OperationLog).filter(
            or_(
                OperationLog.action.like("%workflow%"),
                OperationLog.action.like("%orchestration%"),
                OperationLog.action.like("%workflow_run%")
            )
        )

        # 查询工作流运行记录
        workflow_run_query = db.session.query(WorkflowRun)

        # 添加时间过滤
        if start_datetime:
            workflow_operation_query = workflow_operation_query.filter(OperationLog.created_at >= start_datetime)
            workflow_run_query = workflow_run_query.filter(WorkflowRun.created_at >= start_datetime)
        
        if end_datetime:
            workflow_operation_query = workflow_operation_query.filter(OperationLog.created_at <= end_datetime)
            workflow_run_query = workflow_run_query.filter(WorkflowRun.created_at <= end_datetime)

        # 获取数据
        workflow_operations = workflow_operation_query.order_by(OperationLog.created_at.desc()).limit(limit).all()
        workflow_runs = workflow_run_query.order_by(WorkflowRun.created_at.desc()).limit(limit).all()

        # 格式化数据
        data = []
        
        # 处理工作流操作日志
        for op in workflow_operations:
            account = db.session.query(Account).filter(Account.id == op.account_id).first()
            data.append({
                "id": str(op.id),
                "type": "workflow_operation",
                "action": op.action,
                "account_id": str(op.account_id),
                "account_name": account.name if account else "Unknown",
                "tenant_id": str(op.tenant_id),
                "content": op.content,
                "created_at": op.created_at.isoformat() if op.created_at else None,
                "log_type": "orchestration_audit"
            })

        # 处理工作流运行记录
        for run in workflow_runs:
            app = db.session.query(App).filter(App.id == run.app_id).first()
            data.append({
                "id": str(run.id),
                "type": "workflow_run",
                "app_id": str(run.app_id),
                "app_name": app.name if app else "Unknown",
                "status": run.status,
                "inputs": run.inputs,
                "outputs": run.outputs,
                "created_at": run.created_at.isoformat() if run.created_at else None,
                "log_type": "orchestration_audit"
            })

        # 按时间排序并分页
        data.sort(key=lambda x: x["created_at"], reverse=True)
        total = len(data)
        data = data[(page - 1) * limit:page * limit]

        return {"data": data, "total": total}

    def _get_content_logs(self, start_datetime: Optional[datetime], end_datetime: Optional[datetime], 
                         app_ids: List[str], user_ids: List[str], page: int, limit: int) -> Dict[str, Any]:
        """获取创建者等内容相关日志信息审计"""
        # 查询应用创建和修改记录
        app_query = db.session.query(App)
        
        # 添加时间过滤
        if start_datetime:
            app_query = app_query.filter(App.created_at >= start_datetime)
        if end_datetime:
            app_query = app_query.filter(App.created_at <= end_datetime)

        # 添加应用过滤
        if app_ids:
            app_query = app_query.filter(App.id.in_(app_ids))

        # 获取应用数据
        apps = app_query.order_by(App.created_at.desc()).limit(limit).all()

        # 格式化数据
        data = []
        for app in apps:
            # 获取创建者信息
            creator = db.session.query(Account).filter(Account.id == app.created_by).first()
            updater = db.session.query(Account).filter(Account.id == app.updated_by).first() if app.updated_by else None
            
            data.append({
                "id": str(app.id),
                "type": "app",
                "name": app.name,
                "mode": app.mode,
                "description": app.description,
                "creator_id": str(app.created_by),
                "creator_name": creator.name if creator else "Unknown",
                "creator_email": creator.email if creator else "Unknown",
                "updater_id": str(app.updated_by) if app.updated_by else None,
                "updater_name": updater.name if updater else None,
                "updater_email": updater.email if updater else None,
                "created_at": app.created_at.isoformat() if app.created_at else None,
                "updated_at": app.updated_at.isoformat() if app.updated_at else None,
                "log_type": "content_audit"
            })

        # 查询其他内容相关的操作日志
        content_operation_query = db.session.query(OperationLog).filter(
            or_(
                OperationLog.action.like("%create%"),
                OperationLog.action.like("%update%"),
                OperationLog.action.like("%delete%")
            )
        )

        # 添加时间过滤
        if start_datetime:
            content_operation_query = content_operation_query.filter(OperationLog.created_at >= start_datetime)
        if end_datetime:
            content_operation_query = content_operation_query.filter(OperationLog.created_at <= end_datetime)

        # 添加用户过滤
        if user_ids:
            content_operation_query = content_operation_query.filter(OperationLog.account_id.in_(user_ids))

        # 获取操作日志
        content_operations = content_operation_query.order_by(OperationLog.created_at.desc()).limit(limit).all()

        # 处理操作日志
        for op in content_operations:
            account = db.session.query(Account).filter(Account.id == op.account_id).first()
            data.append({
                "id": str(op.id),
                "type": "content_operation",
                "action": op.action,
                "account_id": str(op.account_id),
                "account_name": account.name if account else "Unknown",
                "account_email": account.email if account else "Unknown",
                "tenant_id": str(op.tenant_id),
                "content": op.content,
                "created_at": op.created_at.isoformat() if op.created_at else None,
                "log_type": "content_audit"
            })

        # 按时间排序并分页
        data.sort(key=lambda x: x["created_at"], reverse=True)
        total = len(data)
        data = data[(page - 1) * limit:page * limit]

        return {"data": data, "total": total}


# 注册API路由
api.add_resource(AuditLogReportApi, "/audit-logs/report") 