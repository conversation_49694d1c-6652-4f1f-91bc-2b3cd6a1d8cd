"""add_anntation_history_match_response

Revision ID: 714aafe25d39
Revises: f2a6fc85e260
Create Date: 2023-12-14 06:38:02.972527

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '714aafe25d39'
down_revision = 'f2a6fc85e260'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('app_annotation_hit_histories', schema=None) as batch_op:
        batch_op.add_column(sa.Column('annotation_question', sa.Text(), nullable=False))
        batch_op.add_column(sa.Column('annotation_content', sa.Text(), nullable=False))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('app_annotation_hit_histories', schema=None) as batch_op:
        batch_op.drop_column('annotation_content')
        batch_op.drop_column('annotation_question')

    # ### end Alembic commands ###
