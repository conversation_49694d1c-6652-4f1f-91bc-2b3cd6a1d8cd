provider: ai_gateway_platform
label:
  en_US: AI Gateway Platform
  zh_Hans: 南网人工智能平台网关服务
description:
  en_US: API gateway providing secure HMAC-SHA256 access to South Power AI Platform's large language models
  zh_Hans: 提供安全的HMAC-SHA256接口，连接人工智能平台网关服务
icon_small:
  en_US: icon.svg
icon_large:
  en_US: icon.svg
background: "#FFFFFF"

supported_model_types:
  - llm

configurate_methods:
  - customizable-model

model_credential_schema:
  model:
    label:
      en_US: Component Code
      zh_Hans: 组件编码
    placeholder:
      en_US: Enter the model/component code
      zh_Hans: 输入模型/组件编码
  credential_form_schemas:
    - variable: display_name
      label:
        en_US: Model display name
        zh_Hans: 模型显示名称
      type: text-input
      required: false
      placeholder:
        zh_Hans: 模型在界面的显示名称
        en_US: The display name of the model in the interface.
    - variable: api_endpoint
      label:
        en_US: API Endpoint
        zh_Hans: API端点
      type: text-input
      required: true
      placeholder:
        en_US: Enter your API endpoint URL (e.g., https://your-platform.com/ai-gateway/predict)
        zh_Hans: 输入您的API端点URL（例如：https://your-platform.com/ai-gateway/predict）
    - variable: customer_code
      label:
        en_US: Customer Code
        zh_Hans: 客户系统编码
      type: text-input
      required: true
      placeholder:
        en_US: Enter your customer system code
        zh_Hans: 输入您的客户系统编码

    - variable: secret_key
      label:
        en_US: Secret Key
        zh_Hans: 密钥
      type: secret-input
      required: true
      placeholder:
        en_US: Enter your authentication secret key
        zh_Hans: 输入您的认证密钥
    - variable: context_size
      label:
        en_US: Context Size
        zh_Hans: 上下文大小
      type: text-input
      required: false
      default: "4096"
      placeholder:
        en_US: Enter your Model context size
        zh_Hans: 输入上下文大小（默认：4096）
    - variable: temperature
      label:
        en_US: Temperature
        zh_Hans: 温度
      type: text-input
      required: false
      default: "1.0"
      placeholder:
        en_US: Enter temperature (0.0-2.0)
        zh_Hans: 输入温度（0.0-2.0）
    - variable: top_p
      label:
        en_US: Top P
        zh_Hans: Top P
      type: text-input
      required: false
      default: "1.0"
      placeholder:
        en_US: Enter top_p (0.0-1.0)
        zh_Hans: 输入 top_p（0.0-1.0）
    - variable: top_k
      label:
        en_US: Top K
        zh_Hans: Top K
      type: text-input
      required: false
      placeholder:
        en_US: Enter top_k (-1 or 0-2147483647)
        zh_Hans: 输入 top_k（-1 或 0-2147483647）
    - variable: max_tokens
      label:
        en_US: Max Tokens
        zh_Hans: 最大 Token 数
      type: text-input
      required: false
      default: "4096"
      placeholder:
        en_US: Enter max tokens
        zh_Hans: 输入最大 Token 数
    - variable: presence_penalty
      label:
        en_US: Presence Penalty
        zh_Hans: 存在惩罚
      type: text-input
      required: false
      default: "0.0"
      placeholder:
        en_US: Enter presence_penalty (-2.0 to 2.0)
        zh_Hans: 输入存在惩罚（-2.0 到 2.0）
    - variable: frequency_penalty
      label:
        en_US: Frequency Penalty
        zh_Hans: 频率惩罚
      type: text-input
      required: false
      default: "0.0"
      placeholder:
        en_US: Enter frequency_penalty (-2.0 to 2.0)
        zh_Hans: 输入频率惩罚（-2.0 到 2.0）
    - variable: repetition_penalty
      label:
        en_US: Repetition Penalty
        zh_Hans: 重复惩罚
      type: text-input
      required: false
      default: "1.0"
      placeholder:
        en_US: Enter repetition_penalty (0.0-2.0)
        zh_Hans: 输入重复惩罚（0.0-2.0）
    - variable: seed
      label:
        en_US: Random Seed
        zh_Hans: 随机种子
      type: text-input
      required: false
      placeholder:
        en_US: Enter random seed (0-18446744073709551615)
        zh_Hans: 输入随机种子（0-18446744073709551615）
    - variable: stream
      label:
        en_US: Stream
        zh_Hans: 流式输出
      type: select
      required: false
      default: "false"
      options:
        - value: "true"
          label:
            en_US: Enable
            zh_Hans: 启用
        - value: "false"
          label:
            en_US: Disable
            zh_Hans: 禁用
    - variable: vision_support
      label:
        en_US: Vision Support
        zh_Hans: Vision 支持
      type: select
      required: false
      default: "no_support"
      options:
        - value: support
          label:
            en_US: Support
            zh_Hans: 支持
        - value: no_support
          label:
            en_US: Not Support
            zh_Hans: 不支持
    - variable: skip_special_tokens
      label:
        en_US: Skip Special Tokens
        zh_Hans: 跳过特殊Token
      type: select
      required: false
      default: "true"
      options:
        - value: "true"
          label:
            en_US: Enable
            zh_Hans: 启用
        - value: "false"
          label:
            en_US: Disable
            zh_Hans: 禁用
    - variable: ignore_eos
      label:
        en_US: Ignore EOS Token
        zh_Hans: 忽略结束符
      type: select
      required: false
      default: "false"
      options:
        - value: "true"
          label:
            en_US: Enable
            zh_Hans: 启用
        - value: "false"
          label:
            en_US: Disable
            zh_Hans: 禁用

extra:
  python:
    provider_source: provider/ai_gateway_platform.py
    model_sources:
      - "models/llm/llm.py"
