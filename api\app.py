import os
import sys


def is_db_command():
    if len(sys.argv) > 1 and sys.argv[0].endswith("flask") and sys.argv[1] == "db":
        return True
    return False


# create app
if is_db_command():
    from app_factory import create_migrations_app

    app = create_migrations_app()
else:
    # It seems that JetBrains Python debugger does not work well with gevent,
    # so we need to disable gevent in debug mode.
    # If you are using debugpy and set GEVENT_SUPPORT=True, you can debug with gevent.
    if (flask_debug := os.environ.get("FLASK_DEBUG", "0")) and flask_debug.lower() in {"false", "0", "no"}:
        from gevent import monkey

        # gevent
        monkey.patch_all()

        from grpc.experimental import gevent as grpc_gevent  # type: ignore

        # grpc gevent
        grpc_gevent.init_gevent()

        import psycogreen.gevent  # type: ignore

        psycogreen.gevent.patch_psycopg()

    from app_factory import create_app

    app = create_app()
    celery = app.extensions["celery"]

    # 初始化本地插件自动安装器
    try:
        import logging
        from threading import Timer
        from configs import dify_config

        # 确保插件模块可以被导入
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'plugins'))

        from plugins.local_plugin_manager import auto_install_plugins_for_all_tenants

        # 检查是否启用了启动时安装
        if dify_config.LOCAL_PLUGIN_AUTO_INSTALL_ENABLED and dify_config.LOCAL_PLUGIN_AUTO_INSTALL_ON_STARTUP:
            logger = logging.getLogger(__name__)
            logger.info("准备自动安装本地插件 ...")

            def run_plugin_installation():
                """在应用上下文中执行插件安装"""
                try:
                    with app.app_context():
                        logger.info("开始执行本地插件自动安装...")
                        result = auto_install_plugins_for_all_tenants()
                        success_count = result.get('installed', 0) + result.get('skipped', 0)
                        logger.info(f"本地插件自动安装完成: 成功 {success_count}/{result.get('total', 0)}")
                except Exception as e:
                    logger.error(f"本地插件自动安装过程中发生严重错误: {str(e)}", exc_info=True)

            # 直接在应用启动时执行
            run_plugin_installation()

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to initialize local plugin auto installer: {str(e)}", exc_info=True)


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5001)
