{"name": "dify-client", "version": "2.3.2", "description": "This is the Node.js SDK for the Dify.AI API, which allows you to easily integrate Dify.AI into your Node.js applications.", "main": "index.js", "type": "module", "types": "index.d.ts", "keywords": ["Dify", "Dify.AI", "LLM"], "author": "<PERSON>", "contributors": ["<crazywoola> <<<EMAIL>>> (https://github.com/crazywoola)"], "license": "MIT", "scripts": {"test": "jest"}, "jest": {"transform": {"^.+\\.[t|j]sx?$": "babel-jest"}}, "dependencies": {"axios": "^1.3.5"}, "devDependencies": {"@babel/core": "^7.21.8", "@babel/preset-env": "^7.21.5", "babel-jest": "^29.5.0", "jest": "^29.5.0"}}