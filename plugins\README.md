# 本地插件自动安装系统

## 概述

本地插件自动安装系统允许您自动为所有租户或特定租户安装本地插件包。该系统使用Dify标准配置系统，支持环境变量配置。

## 配置

### 环境变量配置

在您的`.env`文件中添加以下配置项：

```bash
# ==============================================
# 本地插件自动安装配置
# ==============================================

# 是否启用本地插件自动安装
LOCAL_PLUGIN_AUTO_INSTALL_ENABLED=true

# 本地插件包目录路径（相对于项目根目录）
LOCAL_PLUGIN_AUTO_INSTALL_DIR=plugins/packages

# 是否验证插件签名（生产环境建议启用）
LOCAL_PLUGIN_AUTO_INSTALL_VERIFY_SIGNATURE=false

# 启动时自动安装
LOCAL_PLUGIN_AUTO_INSTALL_ON_STARTUP=true

# 新用户创建时自动安装
LOCAL_PLUGIN_AUTO_INSTALL_FOR_NEW_USERS=true

# 安装日志级别
LOCAL_PLUGIN_AUTO_INSTALL_LOG_LEVEL=INFO

# 最大重试次数
LOCAL_PLUGIN_AUTO_INSTALL_MAX_RETRIES=3

# 排除的插件列表（插件名称，不包含文件扩展名，用逗号分隔）
LOCAL_PLUGIN_AUTO_INSTALL_EXCLUDE_PLUGINS=test-plugin,debug-plugin

# 包含的插件列表（如果指定，只会安装列表中的插件，留空表示安装所有插件）
LOCAL_PLUGIN_AUTO_INSTALL_INCLUDE_PLUGINS=

# 是否强制重新安装已存在的插件
LOCAL_PLUGIN_AUTO_INSTALL_FORCE_REINSTALL=false
```

### 配置说明

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `LOCAL_PLUGIN_AUTO_INSTALL_ENABLED` | bool | `true` | 是否启用本地插件自动安装 |
| `LOCAL_PLUGIN_AUTO_INSTALL_DIR` | string | `plugins/packages` | 插件包目录路径 |
| `LOCAL_PLUGIN_AUTO_INSTALL_VERIFY_SIGNATURE` | bool | `false` | 是否验证插件签名 |
| `LOCAL_PLUGIN_AUTO_INSTALL_ON_STARTUP` | bool | `true` | 启动时自动安装 |
| `LOCAL_PLUGIN_AUTO_INSTALL_FOR_NEW_USERS` | bool | `true` | 新用户创建时自动安装 |
| `LOCAL_PLUGIN_AUTO_INSTALL_LOG_LEVEL` | string | `INFO` | 日志级别 |
| `LOCAL_PLUGIN_AUTO_INSTALL_MAX_RETRIES` | int | `3` | 最大重试次数 |
| `LOCAL_PLUGIN_AUTO_INSTALL_EXCLUDE_PLUGINS` | string | `""` | 排除的插件列表 |
| `LOCAL_PLUGIN_AUTO_INSTALL_INCLUDE_PLUGINS` | string | `""` | 包含的插件列表 |
| `LOCAL_PLUGIN_AUTO_INSTALL_FORCE_REINSTALL` | bool | `false` | 强制重新安装 |

## 使用方法

### 1. 准备插件包

将您的插件包文件（`.difypkg`、`.zip`、`.tar.gz`格式）放置在配置的插件目录中：

```
plugins/
└── packages/
    ├── my-plugin-1.difypkg
    ├── my-plugin-2.zip
    └── my-plugin-3.tar.gz
```

### 2. 配置环境变量

在`.env`文件中配置相关参数。

### 3. 自动安装

#### 为所有租户安装插件

```python
from plugins.local_plugin_manager import auto_install_plugins_for_all_tenants

# 为所有租户安装插件
result = auto_install_plugins_for_all_tenants()
print(f"安装结果: {result}")
```

#### 为特定租户安装插件

```python
from plugins.local_plugin_manager import auto_install_plugins_for_tenant

# 为特定租户安装插件
tenant_id = "your-tenant-id"
result = auto_install_plugins_for_tenant(tenant_id)
print(f"安装结果: {result}")
```

#### 使用管理器实例

```python
from plugins.local_plugin_manager import get_local_plugin_manager

# 获取管理器实例
manager = get_local_plugin_manager()

# 为所有租户安装插件
result = manager.install_plugins_for_all_tenants()

# 为特定租户安装插件
result = manager.install_plugins_for_single_tenant("tenant-id")
```

### 4. 插件过滤

系统支持通过配置过滤插件：

#### 排除特定插件

```bash
# 排除测试和调试插件
LOCAL_PLUGIN_AUTO_INSTALL_EXCLUDE_PLUGINS=test-plugin,debug-plugin,development-plugin
```

#### 只安装特定插件

```bash
# 只安装生产环境插件
LOCAL_PLUGIN_AUTO_INSTALL_INCLUDE_PLUGINS=production-plugin,auth-plugin,analytics-plugin
```

## 集成到应用启动

您可以在应用启动时自动安装插件：

```python
# 在应用启动脚本中
from plugins.local_plugin_manager import get_local_plugin_manager
from api.plugins.config_loader import get_local_plugin_config

def auto_install_plugins_on_startup():
    """应用启动时自动安装插件"""
    config = get_local_plugin_config()
    
    if not config.enabled:
        print("本地插件自动安装已禁用")
        return
    
    if not config.auto_install_on_startup:
        print("启动时自动安装已禁用")
        return
    
    print("开始自动安装本地插件...")
    manager = get_local_plugin_manager()
    result = manager.install_plugins_for_all_tenants()
    
    print(f"插件安装完成：成功 {result['success']}/{result['total']}")
    if result['failed'] > 0:
        print(f"失败 {result['failed']} 个安装任务")

# 在应用启动时调用
auto_install_plugins_on_startup()
```

## 日志配置

系统会根据配置的日志级别输出详细信息：

- `DEBUG`: 详细的调试信息
- `INFO`: 一般信息（默认）
- `WARNING`: 警告信息
- `ERROR`: 错误信息

## 错误处理

系统包含完善的错误处理机制：

1. **插件文件不存在**: 跳过并记录警告
2. **上传失败**: 重试指定次数后放弃
3. **安装失败**: 记录错误但继续处理其他插件
4. **租户查询失败**: 记录错误并返回空结果

## 最佳实践

1. **生产环境**: 启用签名验证 (`LOCAL_PLUGIN_AUTO_INSTALL_VERIFY_SIGNATURE=true`)
2. **开发环境**: 禁用签名验证以提高开发效率
3. **插件管理**: 使用排除/包含列表精确控制插件安装
4. **监控**: 定期检查安装日志确保插件正常安装
5. **备份**: 在批量安装前备份现有插件配置

## 故障排除

### 常见问题

1. **插件安装失败**
   - 检查插件包格式是否正确
   - 确认插件包没有损坏
   - 查看日志获取详细错误信息

2. **配置不生效**
   - 确认环境变量名称正确
   - 重启应用使配置生效
   - 检查配置文件语法

3. **权限问题**
   - 确认应用有读取插件目录的权限
   - 检查数据库连接权限

### 调试模式

启用调试模式获取更多信息：

```bash
LOCAL_PLUGIN_AUTO_INSTALL_LOG_LEVEL=DEBUG
``` 
