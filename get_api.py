import os
import ast
import csv

def find_python_files(root_dir):
    python_files = []
    for dirpath, _, filenames in os.walk(root_dir):
        for filename in filenames:
            if filename.endswith('.py'):
                python_files.append(os.path.join(dirpath, filename))
    return python_files

class ApiAddResourceVisitor(ast.NodeVisitor):
    def __init__(self):
        self.calls = []

    def visit_Call(self, node):
        if isinstance(node.func, ast.Attribute):
            if node.func.attr == 'add_resource':
                # Get the full name (e.g., api.add_resource)
                value = node.func.value
                if isinstance(value, ast.Name):
                    full_name = f"{value.id}.{node.func.attr}"
                elif isinstance(value, ast.Attribute):
                    parts = []
                    current = value
                    while isinstance(current, ast.Attribute):
                        parts.insert(0, current.attr)
                        current = current.value
                    if isinstance(current, ast.Name):
                        parts.insert(0, current.id)
                    full_name = ".".join(parts) + f".{node.func.attr}"
                else:
                    full_name = node.func.attr
                # Extract arguments as strings
                try:
                    args = [ast.unparse(arg) for arg in node.args]
                except AttributeError:
                    # For Python versions < 3.9
                    args = [ast.dump(arg) for arg in node.args]
                self.calls.append(f"{full_name}({', '.join(args)})")
        self.generic_visit(node)

def analyze_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        try:
            tree = ast.parse(f.read(), filename=file_path)
        except SyntaxError:
            return []
    visitor = ApiAddResourceVisitor()
    visitor.visit(tree)
    return visitor.calls

def main():
    root_dir = '.'  # Change to your repository root if needed
    python_files = find_python_files(root_dir)
    results = []
    repo_path = os.path.abspath(root_dir)
    for file_path in python_files:
        calls = analyze_file(file_path)
        if calls:
            relative_path = os.path.relpath(file_path, repo_path)
            for call in calls:
                results.append({
                    'repository_path': repo_path,
                    'file_path': relative_path,
                    'api_add_resource_call': call
                })
    # Write to CSV
    with open('api_add_resource_calls.csv', 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['repository_path', 'file_path', 'api_add_resource_call']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for row in results:
            writer.writerow(row)

if __name__ == "__main__":
    main()
