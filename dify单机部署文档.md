mkdir offline-rpm
---

# 麒麟V10环境 Docker & Compose 离线部署及权限问题完整处理文档

## 一、系统环境准备

- 操作系统：麒麟V10（Kylin V10）、UOS、银河麒麟等国产发行版通用
- 建议服务器以 root 用户登陆操作全流程
- 假定 Docker 工作目录为默认 `/var/lib/docker`
- 确认网络不通外网（离线）
- **重要**：XFS 文件系统的 ftype=1，已支持 overlay2

---

## 二、系统依赖和目录检查

1. **检查文件系统类型**
   ```bash
   df -Th /
   xfs_info /
   ```
    - 文件系统需为 xfs，且 `naming = ... ftype=1`
    - 否则可能无法 overlay2，无法彻底解决 Docker 权限问题

2. **确保 docker 数据目录权限正常**
   ```bash
   ls -ld /var/lib/docker
   ```
    - 一般为 `drwx--x--x. 18 <USER> <GROUP> ... /var/lib/docker`
    - 若不是 root 拥有，请修正：

   ```bash
   chown root:root /var/lib/docker
   chmod 755 /var/lib/docker
   ```

---

## 三、离线包准备

- 本地准备目录（如 /data/offline-rpm/），包含如下文件：
    - docker-<version>.tgz           #官方docker离线tgz安装包
    - docker-compose-linux-x86_64    #compose二进制
    - runc.amd64                     #官方runc二进制，版本≥1.1.0
    - 你自己的 docker-compose.yml 和其他业务相关包

---

## 四、卸载系统原生 Docker/Runc（如有）

1. **检查并卸载原有包（如已提前装过/有残留）**
   ```bash
   rpm -qa | grep docker
   rpm -qa | grep runc
   ```
   如发现 rpm 安装的 docker/runc
   ```bash
   yum remove -y docker docker-ce docker-ce-cli docker-engine docker-selinux docker-common containerd runc
   ```

---

## 五、部署最新版 runc 到所有可能路径

> 科学踩坑：哪怕你覆盖了 `/usr/bin/runc`，但实际系统用的是 `/usr/local/bin/runc`，一定要**都覆盖**！

1. **复制最新版 runc 并授权**
   ```bash
   cp /data/offline-rpm/runc.amd64 /usr/local/bin/runc
   cp /data/offline-rpm/runc.amd64 /usr/bin/runc
   chmod +x /usr/local/bin/runc /usr/bin/runc
   ```

2. **验证版本号**
   ```bash
   which runc
   runc --version
   ```
    - 期望看到 `runc version 1.1.x`，版本号不再是 1.0.0-rc3

---

## 六、部署 Docker Engine

1. **解压并安装 Docker**
   ```bash
   tar -xzvf /data/offline-rpm/docker-*.tgz -C /usr/local/
   ```

2. **建立软连接（docker命令能全局用）**
   ```bash
   ln -sf /usr/local/docker/* /usr/bin/
   ```

3. **配置 systemd（service/daemon）**
mkdir -p /usr/lib/systemd/system
vi /usr/lib/systemd/system/docker.service
    - 如无 docker.service 文件，创建如下（可从社区或官方拷贝标准模板）：
      ```
[Unit]
Description=Docker Application Container Engine
Documentation=https://docs.docker.com
After=network.target firewalld.service
 
[Service]
Type=notify
ExecStart=/usr/bin/dockerd
ExecReload=/bin/kill -s HUP $MAINPID
LimitNOFILE=1048576
LimitNPROC=1048576
LimitCORE=infinity
TimeoutStartSec=0
Delegate=yes
KillMode=process
Restart=on-failure
StartLimitBurst=3
StartLimitInterval=60s
 
[Install]
WantedBy=multi-user.target
      ```
      保存为 `/usr/lib/systemd/system/docker.service`

    - 重新加载 systemd
      ```bash
      systemctl daemon-reload
      ```
mkdir -p /data/docker
chown root:root /data/docker
chmod 755 /data/docker
mkdir -p /etc/docker
vi /etc/docker/daemon.json
{
"data-root": "/data/docker"
}

4. **启动 Docker**
   ```bash
   systemctl enable docker --now
   systemctl start docker
   systemctl status docker
   ```

---

## 七、安装 Docker Compose（二进制版）

```bash
cp /data/offline-rpm/docker-compose-linux-x86_64 /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose
```
验证版本：
```bash
docker-compose --version
```

---

## 八、运行容器及排查

1. **首次测试（务必先单容器测试）**
   ```bash
   docker run --rm -it busybox sh
   ```
    - 能进入 busybox shell 说明基本没问题

2. **启动你的项目**
   ```bash
   cd /路径/你的项目目录
   docker-compose up           # 或 docker compose up
   ```

---

## 九、典型权限/失败排查FAQ

- **报“starting container process caused permission denied”：**
    - 核查 `xfs_info /` 的 ftype=1
    - `runc --version` 必须为 >=1.1.0，并为实际 `which runc` 指向的路径
    - `ls -l $(which runc)` 看实际用的文件
    - 遇到与 /usr/bin/runc /usr/local/bin/runc “文件一样但版本不对”，请用 md5sum 对比/找出真用路径

- **vfs对比测试（极慢，仅debug用）：**
    ```bash
    dockerd --storage-driver=vfs
    docker run --rm -it busybox sh
    ```

- **Linux 能力缺损检测**
    ```bash
    unshare -r -n true && echo OK || echo Capabilities lost
    ```
    - Capabilities lost 说明主机能力不够不支持docker

- **SElinux/AppArmor 关闭建议**
    - 麒麟/银河环境通常默认关闭
    - 检查：`getenforce` 应该为 Disabled

---

## 十、常见失败修正一览表

| 报错 | 可能原因 | 解决办法 |
|------|-----------|---------|
| permission denied | runc版本太老/路径没覆盖成功 | 覆盖所有可能的bin，把新版runc放在`which runc`实际指向路径 |
| overlay2不支持 | xfs ftype=0 | 只能重建分区（fdisk+mkfs.xfs挂载参数）为ftype=1|
| registry镜像拉不了 | 无外网 | 先离线下载官方docker镜像tar包，docker load导入即可 |
| systemd服务起不来 | daemon进程残留 | `pkill dockerd/containerd ; rm -f /var/run/docker.pid` 重启 |

---

## 十一、验证与交付

1. 项目容器能正常 compose up
2. `docker ps` 实例全部运行状态
3. 没有 `permission denied` 或 `overlay2` 相关报错
4. 共识：**一切以`runc --version`为准，路径至上，所有实际调用路径都替换新版**

---

## 十二、附：常用离线下载资源

- [官方 runc releases (github)](https://github.com/opencontainers/runc/releases)
- [官方 Docker releases](https://download.docker.com/linux/static/stable/x86_64/)
- [Compose stand-alone](https://github.com/docker/compose/releases)

---

# 核心经验教训

1. **一定根据 `which runc` 覆盖对应路径**
2. 所有 bin 路径优先以 `/usr/local/bin` 为主（国产linux模板极多软链、优先级坑）
3. 再清理 or 启 docker
4. overlay2 相关权限问题多数为 runc 路径冗余/版本老/能力裁剪/分区ftype

---

### 【特别贴士】
**一定要确认 runc --version ≥ 1.1.0，并出自 `which runc` 路径后，再启动 Docker。否则所有离线重装/清理均无效！**

---

如需更详细的 compose 项目自动化部署范例或官方service文件，可以私信/追问。


你要解压 dify_init.tar.gz 文件，可以按如下命令操作：
tar -zxvf dify_init.tar.gz
如果希望解压到指定目录，比如 /opt/dify_init：
mkdir -p /opt/dify_init
tar -zxvf dify_init.tar.gz -C /opt/dify_init
批量导入 docker 镜像的标准方法
批量导入脚本
你可以一条一条导入，比如：
docker load -i dify_init/langgenius-dify-api-1.6.0.tar
docker load -i dify_init/langgenius-dify-plugin-daemon-0.1.3-local.tar
docker load -i dify_init/langgenius-dify-sandbox-0.2.12.tar
docker load -i dify_init/langgenius-dify-web-1.6.0.tar
docker load -i dify_init/nginx-latest.tar
docker load -i dify_init/postgres-15-alpine.tar
docker load -i dify_init/redis-6-alpine.tar
docker load -i dify_init/semitenologies-weaviate-1.19.0.tar
docker load -i dify_init/ubuntu-squid-latest.tar
但你可以一行命令全部导入：
tar -zxvf dify_init.tar.gz
for i in dify_init/*.tar; do docker load -i "$i"; done