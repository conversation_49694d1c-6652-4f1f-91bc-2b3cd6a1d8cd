const translation = {
  title: 'पुनर्प्राप्ति परीक्षण',
  desc: 'दिए गए प्रश्न पाठ के आधार पर ज्ञान की प्रभावशीलता का परीक्षण करें।',
  dateTimeFormat: 'MM/DD/YYYY hh:mm A',
  recents: 'हाल के',
  table: {
    header: {
      source: 'स्रोत',
      text: 'पाठ',
      time: 'समय',
    },
  },
  input: {
    title: 'स्रोत पाठ',
    placeholder: 'कृपया एक पाठ दर्ज करें, एक छोटा वाक्यांश सुझाया गया है।',
    countWarning: 'अधिकतम 200 अक्षर।',
    indexWarning: 'केवल उच्च गुणवत्ता वाला ज्ञान।',
    testing: 'परीक्षण कर रहा है',
  },
  hit: {
    title: 'पुनर्प्राप्ति अनुच्छेद',
    emptyTip: 'पुनर्प्राप्ति परीक्षण के परिणाम यहां दिखाई देंगे',
  },
  noRecentTip: 'यहां कोई हाल के प्रश्न परिणाम नहीं हैं',
  viewChart: 'वेक्टर चार्ट देखें',
  viewDetail: 'विस्तार से देखें',
  settingTitle: 'पुनर्प्राप्ति सेटिंग',
  hitChunks: '{{num}} बच्चे के टुकड़े मारो',
  keyword: 'खोजशब्दों',
  chunkDetail: 'चंक विवरण',
  open: 'खोलना',
  records: 'रिकॉर्ड',
}

export default translation
